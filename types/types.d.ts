import { ErrorMessage, ToastName } from '~/types/toaster'
declare module '#app' {
  interface NuxtApp {
    $diffForHumans: (value: string) => string
    $diffForHumansMessageFeed: (value: string) => string
    $diffForHumansShortFlag: (value: string) => string
    $toast: (name: ToastName, value?: ErrorMessage) => void
  }
}

declare module 'vue' {
  interface ComponentCustomProperties {
    $diffForHumans: (value: string) => string
    $diffForHumansMessageFeed: (value: string) => string
    $diffForHumansShortFlag: (value: string) => string
    $toast: (name: ToastName, value?: ErrorMessage) => void
  }
}

export {}
