export default cachedEventHandler(
  async (event) => {
    const storage = useStorage()
    // try to get from cache first
    const responseFromCache = await storage.getItem('industries')
    if (responseFromCache) {
      return responseFromCache
    } else {
      // set response in redis cache
      const response = await $fetch(
        `${useRuntimeConfig().apiUrl}/get/industries/`,
        {
          headers: {
            Accept: 'application/json',
            'x-real-ip': event.node.req.headers['x-real-ip'],
            'x-forwarded-for': event.node.req.headers['x-forwarded-for'],
            'x-forwarded-proto': event.node.req.headers['x-forwarded-proto'],
            'user-agent': event.node.req.headers['user-agent'],
          },
        }
      )

      if (response && response.status === 200) {
        await storage.setItem('industries', response.data)

        return response.data
      }
    }
    return null
  },
  {
    swr: true,
    group: 'api',
    maxAge: 60 * 60,
  }
)
