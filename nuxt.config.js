import path from 'path'
import { fileURLToPath } from 'url'
import fs from 'fs'
const __filename = fileURLToPath(import.meta.url)

const __dirname = path.dirname(__filename)
// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
  //end New Changes After Deployment and Causing Page Loading
  app: {
    pageTransition: { name: 'page', mode: 'out-in' },
    // layoutTransition: false,
    // layoutTransition: { name: 'my-layouts', mode: 'out-in' },
    head: {
      title:
        'Social Media Monitoring Software | Social Media Archiving Software | Sharp Archive',
      titleTemplate: '%s',
      htmlAttrs: {
        lang: 'en',
      },
      meta: [
        { name: 'viewport', content: 'width=device-width, initial-scale=1' },
        { name: 'keywords', content: process.env.META_KEYWORDS },
        {
          name: 'description',
          content:
            'Sharp archive provides ringtail ediscovery services, archive softwares, social media archive, web archiving and instant message archiving service in USA.',
        },
        { property: 'og:type', content: 'website' },
        { property: 'fb:app_id', content: '391253345709260' },
        { 'http-equiv': 'cache-control', content: 'no-cache' },
        { 'http-equiv': 'expires', content: '0' },
        { 'http-equiv': 'pragma', content: 'no-cache' },
        {
          hid: 'og:image',
          property: 'og:image',
          content: `${process.env.NUXT_PUBLIC_APP_URL}/icon.png`,
        },
      ],
      link: [
        {
          rel: 'icon',
          type: 'image/x-icon',
          href: `${process.env.NUXT_PUBLIC_APP_URL}/favicon.png`,
        },
        {
          hid: 'canonical',
          rel: 'canonical',
          href: process.env.NUXT_PUBLIC_APP_URL,
        },
      ],
      // script: [
      //   {
      //     src: 'https://unpkg.com/@googlemaps/js-api-loader@1.x/dist/index.min.js', // Add external script source
      //     type: 'text/javascript', // Specify script type (optional, default is JavaScript)
      //     defer: true, // Specify if the script should be deferred
      //     async: true, // You can also use `async` if needed
      //   },
      // ],
    },
  },
  //Start New Changes After Deployment and Causing Page Loading
  render: {
    static: {
      maxAge: '90000',
      setHeaders(res) {
        res.setHeader('Cache-Control', 'public, max-age=86400')
      },
      cacheControl: false,
    },
  },

  build: {
    // transpile: ['@nuxt/types'],
    extractCSS: true,
    filenames: {
      app: ({ isDev }) => (isDev ? '[name].js' : '[contenthash].js'),
      chunk: ({ isDev }) => (isDev ? '[name].js' : '[contenthash].js'),
      css: ({ isDev }) => (isDev ? '[name].css' : '[contenthash].css'),
    },
  },
  imports: {
    autoImport: true,
  },

  generate: {
    etag: true,
  },

  runtimeConfig: {
    apiUrl: process.env.NUXT_API_URL,
    authSecret: 'secret',

    public: {
      appName: process.env.NUXT_PUBLIC_APP_NAME,
      siteUrl: process.env.NUXT_PUBLIC_APP_URL,
      apiUrl: process.env.NUXT_PUBLIC_API_URL,
      wsHost: process.env.NUXT_PUBLIC_WS_HOST,
      wsPort: process.env.NUXT_PUBLIC_WS_PORT,
      stripeKey: process.env.NUXT_PUBLIC_STRIPE_KEY,
      googleMapKey: process.env.GOOGLE_MAP_KEY,
      facebookPixelId: process.env.NUXT_PUBLIC_FACEBOOK_PIXEL_ID,
      workflow: process.env.NUXT_PUBLIC_WORKFLOW,
      sentryDSN: process.env.NUXT_PUBLIC_SENTRY_DSN,
      gtm: {
        id: process.env.GOOGLE_TAG_MANAGER_ID,
        enabled: false,
        loadScript: false,
      },
      metapixel: {
        default: { id: process.env.NUXT_PUBLIC_FACEBOOK_PIXEL_ID },
      },
      social: {
        facebook: {
          clientId: process.env.NUXT_PUBLIC_SOCIAL_FACEBOOK_CLIENT_ID,
          authorization: 'https://www.facebook.com/v18.0/dialog/oauth',
          scope: process.env.NUXT_PUBLIC_SOCIAL_FACEBOOK_SCOPE,
          responseType: 'code',
          codeChallengeMethod: '',
          redirectUri: process.env.NUXT_PUBLIC_SOCIAL_FACEBOOK_REDIRECT_URL,
        },
        instagram: {
          clientId: process.env.NUXT_PUBLIC_SOCIAL_INSTAGRAM_CLIENT_ID,
          authorization: 'https://www.instagram.com/oauth/authorize',
          scope: process.env.NUXT_PUBLIC_SOCIAL_INSTAGRAM_SCOPE,
          responseType: 'code',
          // codeChallengeMethod: '',
          redirectUri: process.env.NUXT_PUBLIC_SOCIAL_INSTAGRAM_REDIRECT_URL,
          forceAuthentication: '1',
        },
        linkedin: {
          clientId: process.env.NUXT_PUBLIC_SOCIAL_LINKEDIN_CLIENT_ID,
          authorization: 'https://www.linkedin.com/oauth/v2/authorization',
          scope: process.env.NUXT_PUBLIC_SOCIAL_LINKEDIN_SCOPE,
          responseType: 'code',
          codeChallengeMethod: '',
          redirectUri: process.env.NUXT_PUBLIC_SOCIAL_LINKEDIN_REDIRECT_URL,
        },
        twitter: {
          clientId: process.env.NUXT_PUBLIC_SOCIAL_TWITTER_CLIENT_ID,
          authorization: 'https://twitter.com/i/oauth2/authorize',
          scope: process.env.NUXT_PUBLIC_SOCIAL_TWITTER_SCOPE,
          responseType: 'code',
          codeChallengeMethod: 'plain',
          redirectUri: process.env.NUXT_PUBLIC_SOCIAL_TWITTER_REDIRECT_URL,
        },
        pinterest: {
          clientId: process.env.NUXT_PUBLIC_SOCIAL_PINTEREST_CLIENT_ID,
          authorization: 'https://www.pinterest.com/oauth/',
          scope: process.env.NUXT_PUBLIC_SOCIAL_PINTEREST_SCOPE,
          responseType: 'code',
          redirectUri: process.env.NUXT_PUBLIC_SOCIAL_PINTEREST_REDIRECT_URL,
        },
        reddit: {
          clientId: process.env.NUXT_PUBLIC_SOCIAL_REDDIT_CLIENT_ID,
          authorization: 'https://www.reddit.com/api/v1/authorize',
          scope: process.env.NUXT_PUBLIC_SOCIAL_REDDIT_SCOPE,
          responseType: 'code',
          redirectUri: process.env.NUXT_PUBLIC_SOCIAL_REDDIT_REDIRECT_URL,
          duration: 'permanent',
        },
        tiktok: {
          clientKey: process.env.NUXT_PUBLIC_SOCIAL_TIKTOK_CLIENT_KEY,
          authorization: 'https://www.tiktok.com/v2/auth/authorize/',
          scope: process.env.NUXT_PUBLIC_SOCIAL_TIKTOK_SCOPE,
          responseType: 'code',
          redirectUri: process.env.NUXT_PUBLIC_SOCIAL_TIKTOK_REDIRECT_URL,
        },
        google: {
          clientId: process.env.NUXT_PUBLIC_SOCIAL_GOOGLE_CLIENT_ID,
          nonce: Math.floor(Math.random() * 100 + 10),
          prompt: 'consent',
          accessType: 'offline',
          authorization: 'https://accounts.google.com/o/oauth2/v2/auth',
          scope: process.env.NUXT_PUBLIC_SOCIAL_GOOGLE_SCOPE,
          responseType: 'code',
          redirectUri: process.env.NUXT_PUBLIC_SOCIAL_GOOGLE_REDIRECT_URL,
        },
        microsoft: {
          clientId: process.env.NUXT_PUBLIC_SOCIAL_MICROSOFT_CLIENT_ID,
          authorization:
            'https://login.microsoftonline.com/common/oauth2/v2.0/authorize',
          scope: process.env.NUXT_PUBLIC_SOCIAL_MICROSOFT_SCOPE,
          responseType: 'code',
          redirectUri: process.env.NUXT_PUBLIC_SOCIAL_MICROSOFT_REDIRECT_URL,
        },
        youtube: {
          clientId: process.env.NUXT_PUBLIC_SOCIAL_YOUTUBE_CLIENT_ID,
          prompt: 'consent',
          accessType: 'offline',
          authorization: 'https://accounts.google.com/o/oauth2/v2/auth',
          scope: process.env.NUXT_PUBLIC_SOCIAL_YOUTUBE_SCOPE,
          responseType: 'code',
          redirectUri: process.env.NUXT_PUBLIC_SOCIAL_YOUTUBE_REDIRECT_URL,
        },
      },
    },
  },
  css: [
    '@fortawesome/fontawesome-svg-core/styles.css',
    'v-calendar/dist/style.css',
    '@/assets/css/tailwind.css',
  ],
  components: {
    global: true,
    dirs: ['~/components'],
  },
  // typescript: {
  //   shim: true,
  //   strict: true
  // },
  // Modules for dev and build (recommended): https://go.nuxtjs.dev/config-modules
  buildModules: [
    // https://go.nuxtjs.dev/eslint
    '@nuxtjs/eslint-module',
    // https://go.nuxtjs.dev/tailwindcss
    // '@nuxtjs/tailwindcss',
  ],
  // Modules: https://go.nuxtjs.dev/config-modules
  modules: [
    // '@nuxtjs/robots',
    // 'nuxt-delay-hydration',
    '@nuxtjs/tailwindcss', // '@nuxtjs/sentry',
    '@vueuse/nuxt',
    'nuxt-lodash',
    'nuxt-simple-robots',
    '@nuxtjs/sitemap',
    'nuxt-security',
    '@zadigetvoltaire/nuxt-gtm',
    '@dargmuesli/nuxt-cookie-control',
    'nuxt-seo-experiments',
    '@nuxt/fonts',
    'nuxt-meta-pixel', // 'nuxt-tiptap-editor',
    '@nuxt/image',
    '@nuxt/test-utils/module',
    'nuxt-emoji-picker',
    '@pinia/nuxt',
  ],
  tiptap: {
    prefix: 'Tiptap', //prefix for Tiptap imports, composables not included
  },
  cookieControl: {
    // typed module options
    barPosition: 'bottom-full',
    closeModalOnClickOutside: true,
    colors: {
      barBackground: '#E4801D',
      barButtonBackground: '#fff',
      barButtonColor: '#E4801D',
      barButtonHoverBackground: '#fff',
      barButtonHoverColor: '#E4801D',
      barTextColor: '#fff',
      checkboxActiveBackground: '#000',
      checkboxActiveCircleBackground: '#fff',
      checkboxDisabledBackground: '#ddd',
      checkboxDisabledCircleBackground: '#fff',
      checkboxInactiveBackground: '#000',
      checkboxInactiveCircleBackground: '#fff',
      controlButtonBackground: '#fff',
      controlButtonHoverBackground: '#000',
      controlButtonIconColor: '#000',
      controlButtonIconHoverColor: '#fff',
      focusRingColor: '#808080',
      modalBackground: '#E4801D',
      modalButtonBackground: '#fff',
      modalButtonColor: '#E4801D',
      modalButtonHoverBackground: '#fff',
      modalButtonHoverColor: '#E4801D',
      modalOverlay: '#000',
      modalOverlayOpacity: 0.8,
      modalTextColor: '#fff',
      modalUnsavedColor: '#fff',
    },
    cookies: {
      necessary: [
        {
          id: 'auth',
          name: {
            en: 'Authentication',
          },
          description: { en: 'Used for authentication purposes.' },
          targetCookieIds: [
            'token',
            'userInfo',
            'refresh',
            'loggedIn',
            'codeInfo',
          ],
        },
        {
          id: 'google_maps',
          name: 'Google Maps API',
          description: 'Used for displaying maps on the website.',
          targetCookieIds: ['_gmap'],
        },
      ],
      optional: [
        {
          id: 'google_analytics',
          name: {
            en: 'Google Analytics',
          },
          description: {
            en: 'Google Analytics cookies for website analytics.',
          },
          targetCookieIds: [
            'gtm',
            'gtag',
            '_gcl_au',
            '_gcl_au',
            '_gcl_gb',
            'IDE',
            'ANID',
            'NID',
            '_gads',
          ],
          isPreselected: false,
        },
        {
          id: 'hotjar',
          name: {
            en: 'Hotjar',
          },
          description: {
            en: 'Hotjar cookies for user behavior tracking.',
          },
          targetCookieIds: ['__Host-csrf', '_hjSessionUser*', '_hjSession*'],
          isPreselected: false,
        },
      ],
    },
    // isIframeBlocked: true,
  },
  robots: {
    sitemap: [`${process.env.NUXT_PUBLIC_APP_URL}/sitemap.xml`],
    robotsEnabledValue:
      'index, follow, max-image-preview:large, max-snippet:-1, max-video-preview:-1',
    robotsDisabledValue: 'noindex, nofollow',
    mergeWithRobotsTxtPath: true,
  },
  security: {
    headers: {
      crossOriginEmbedderPolicy: 'unsafe-none',
      // process.env.NODE_ENV === 'development'
      //   ? 'unsafe-none'
      //   : 'require-corp',
      strictTransportSecurity: {
        maxAge: 31536000,
        includeSubDomains: true,
        preload: true,
      },
      xXSSProtection: '1; mode=block',
      contentSecurityPolicy: {
        'script-src': [
          "'self'",
          'https://ct.capterra.com',
          `${process.env.NUXT_PUBLIC_APP_URL}`,
          `${process.env.NUXT_CONTENT_SECURITY_POLICY}`,
          "'unsafe-inline'",
          "'strict-dynamic'",
          "'nonce-{{nonce}}'",
          'https://maps.googleapis.com',
          'https://share.hsforms.com',
          'https://assets.calendly.com',
          'https://js.stripe.com',
          'http://www.tiktok.com',
          'https://sharparchive.com/_nuxt/',
          'https:',
          'https://www.gstatic.com',
          'https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css',
        ],
        'style-src': [
          "'self'",
          'https://ct.capterra.com',
          `${process.env.NUXT_PUBLIC_APP_URL}`,
          'https://fonts.googleapis.com',
          `${process.env.NUXT_CONTENT_SECURITY_POLICY}`,
          'https://js.stripe.com',
          "'unsafe-inline'",
          'http://www.tiktok.com',
          'https://sharparchive.com/_nuxt/',
          'https:',
          'https://www.gstatic.com',
          'https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css',
        ],
        'img-src': [
          "'self'",
          `${process.env.NUXT_PUBLIC_APP_URL}`,
          'blob:',
          'https://www.facebook.com',
          'https://js.stripe.com',
          `${process.env.NUXT_CONTENT_SECURITY_POLICY}`,
          'data:',
          'https://ct.capterra.com',
          'https://forms.hsforms.com',
          'https://www.google.com',
          'https://www.google.com.bd',
          'https://maps.gstatic.com',
          'https://maps.googleapis.com',
          'https://lh3.googleusercontent.com',
          'https://i.pinimg.com',
          'https://styles.redditmedia.com',
          'https://p16-sign.tiktokcdn-us.com',
          'https://www.googleadservices.com',
          'https://googleads.g.doubleclick.net',
          'https://share.hsforms.com',
          'http://www.tiktok.com',
          'https://sharparchive.com/_nuxt/',
          'https:',
          'https://www.gstatic.com',
          'https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css',
        ],
        'font-src': [
          "'self'",
          'https://fonts.gstatic.com',
          'https://fonts.googleapis.com',
          'https://js.stripe.com',
          `${process.env.NUXT_PUBLIC_APP_URL}`,
          'https://sharparchive.com/',
          'https://sharparchive.com/_nuxt/',
          'data:',
          'https://ct.capterra.com',
          'https://www.gstatic.com',
          'https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css',
        ],
        'worker-src': ["'self'", 'blob:'],
        'connect-src': [
          "'self'",
          'https://ct.capterra.com',
          'https://pdf-generator.sharparchive.com/upload-html',
          'https://pdf-generator.sharparchive.com/check-pdf',
          'https://pdf-generator.sharparchive.com/delete-pdf',
          'https://sa-pdf-generator.s3.us-east-2.amazonaws.com/',
          'https://wznojprm72iww227v2kvj47x340dxezx.lambda-url.us-east-2.on.aws/',
          `${process.env.NUXT_PUBLIC_APP_URL}`,
          `${process.env.NUXT_CONTENT_SECURITY_POLICY}`,
          `${process.env.NUXT_PUBLIC_WS_HOST}`,
          'https://js.stripe.com',
          'https://api.hubapi.com',
          'https://forms.hscollectedforms.net',
          'https://o1049196.ingest.sentry.io',
          'https://o1049196.ingest.us.sentry.io',
          'https://google.com',
          'https://analytics.google.com',
          'https://maps.googleapis.com',
          'https://share.hsforms.com',
          'share.hsforms.com',
          'https://stats.g.doubleclick.net',
          'https://merchant-ui-api.stripe.com/link/set-cookie',
          'wss://ws.hotjar.com',
          'http://www.tiktok.com',
          'https://sharparchive.com/_nuxt/',
          'https:',
          'https://www.gstatic.com',
          'https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css',
        ],
        'object-src': ["'none'"],
        'base-uri': ["'none'"],
        // Do not use default-src
      },
    },
    sri: true,
    // csrf: true,
  },
  site: {
    url: process.env.NUXT_PUBLIC_APP_URL,
    indexable:
      process.env.NUXT_PUBLIC_WORKFLOW === 'live' ||
      process.env.NUXT_PUBLIC_WORKFLOW === 'demo'
        ? true
        : false,
  },
  sitemap: {
    xslColumns: [
      { label: 'URL', width: '50%' },
      { label: 'Last Modified', select: 'sitemap:lastmod', width: '25%' },
      { label: 'Priority', select: 'sitemap:priority', width: '12.5%' },
    ],
    xslTips: true,
    urls: async () => {
      const staticRoutes = [
        {
          loc: '/blog/',
          lastmod: '2024-11-22',
          changefreq: 'daily',
          priority: 0.8,
        },
      ]

      const communitiesContent = [
        {
          id: 'finance',
        },
        {
          id: 'publicSchools',
        },
        {
          id: 'governmentAgencies',
        },
        {
          id: 'citiesTowns',
        },
        {
          id: 'policeStations',
        },
        {
          id: 'investmentFirms',
        },
        {
          id: 'collegesUniversities',
        },
        {
          id: 'fireStations',
        },
        {
          id: 'finance-boston',
        },
        {
          id: 'finance-sacramento',
        },
        {
          id: 'finance-for-chicago',
        },
        {
          id: 'public-schools-boston',
        },
        {
          id: 'public-schools-sacramento',
        },
        {
          id: 'public-schools-chicago',
        },
        {
          id: 'government-agencies-boston',
        },
        {
          id: 'government-agencies-sacramento',
        },
        {
          id: 'government-agencies-chicago',
        },
        {
          id: 'cities-towns-boston',
        },
        {
          id: 'cities-towns-sacramento',
        },
        {
          id: 'cities-towns-chicago',
        },
        {
          id: 'police-stations-boston',
        },
        {
          id: 'police-stations-sacramento',
        },
        {
          id: 'police-stations-for-chicago',
        },
        {
          id: 'investment-firms-boston',
        },
        {
          id: 'investment-firms-sacramento',
        },
        {
          id: 'investment-firms-for-chicago',
        },
        {
          id: 'colleges-universities-boston',
        },
        {
          id: 'colleges-universities-sacramento',
        },
        {
          id: 'colleges-and-universities-for-chicago',
        },
        {
          id: 'fire-stations-boston',
        },
        {
          id: 'fire-stations-sacramento',
        },
        {
          id: 'fire-stations-for-chicago',
        },
        {
          id: 'email-archiving-finance',
        },
        {
          id: 'website-archiving-government-agencies',
        },
        {
          id: 'social-media-archiving-for-government-agencies',
        },
        {
          id: 'social-media-archiving-for-finance',
        },
        {
          id: 'social-media-archiving-for-public-schools',
        },
        {
          id: 'social-media-archiving-for-cities-and-towns',
        },
        {
          id: 'social-media-archiving-for-police-stations',
        },
        {
          id: 'social-media-archiving-for-investment-firms',
        },
        {
          id: 'social-media-archiving-for-colleges-and-universities',
        },
        {
          id: 'social-media-archiving-for-fire-stations',
        },
        {
          id: 'email-archiving-for-public-schools',
        },
        {
          id: 'email-archiving-for-government-agencies',
        },
        {
          id: 'email-archiving-for-cities-and-towns',
        },
        {
          id: 'email-archiving-for-police-stations',
        },
        {
          id: 'email-archiving-for-investment-firms',
        },
        {
          id: 'email-archiving-for-colleges-universities',
        },
        {
          id: 'email-archiving-for-fire-station',
        },
        {
          id: 'website-archiving-public-schools',
        },
        {
          id: 'website-archiving-for-cities-towns',
        },
        {
          id: 'website-archiving-for-police-stations',
        },
        {
          id: 'website-archiving-for-investment-firms',
        },
        {
          id: 'website-archiving-for-colleges-universities',
        },
        {
          id: 'website-archiving-for-fire-stations',
        },
        {
          id: 'mobile-archiving-for-fire-stations',
        },
        {
          id: 'gmail-archiving-for-finance',
        },
        {
          id: 'gmail-archiving-for-public-schools',
        },
        {
          id: 'gmail-archiving-for-government-agencies',
        },
        {
          id: 'gmail-archiving-for-cities-and-towns',
        },
        {
          id: 'gmail-archiving-for-police-stations',
        },
        {
          id: 'gmail-archiving-for-investment-firms',
        },
        {
          id: 'gmail-archiving-for-colleges-and-universities',
        },
        {
          id: 'gmail-archiving-for-fire-stations',
        },
        {
          id: 'website-archiving-for-finance',
        },
        {
          id: 'gmail-archiving-for-finance',
        },
        {
          id: 'mobile-archiving-for-finance',
        },
        {
          id: 'mobile-archiving-for-investment-firms',
        },
        {
          id: 'mobile-archiving-for-colleges-and-universities',
        },
        {
          id: 'facebook-archiving-for-finance',
        },
        {
          id: 'facebook-archiving-for-public-schools',
        },
        {
          id: 'facebook-archiving-for-government-agencies',
        },
        {
          id: 'facebook-archiving-for-cities-and-towns',
        },
        {
          id: 'facebook-archiving-for-police-stations',
        },
        {
          id: 'facebook-archiving-for-investment-firms',
        },
      ]

      // Map API slugs to routes with metadata
      const dynamicRoutes = communitiesContent.map((item) => ({
        loc: `/${item.id}`,
        changefreq: 'daily',
        priority: 1,
        lastmod: '2024-11-22', // Assuming API returns an `updatedAt` field
      }))

      const archiveContents = [
        {
          id: 'facebook',
        },
        {
          id: 'instagram',
        },
        {
          id: 'twitter',
        },
        {
          id: 'linkedin',
        },
        {
          id: 'microsoftmail',
        },
        {
          id: 'gmail',
        },
        {
          id: 'youtube',
        },
        {
          id: 'website',
        },
        {
          id: 'pinterest',
        },
        {
          id: 'reddit',
        },
        {
          id: 'tiktok',
        },
      ]

      // Map API slugs to routes with metadata
      const dynamicArchiveContentsRoutes = archiveContents.map((item) => ({
        loc: `/feeds/${item.id}`,
        changefreq: 'daily',
        priority: 1,
        lastmod: '2024-11-22', // Assuming API returns an `updatedAt` field
      }))
      // Combine static and dynamic routes
      return [
        ...staticRoutes,
        ...dynamicRoutes,
        ...dynamicArchiveContentsRoutes,
      ]
    },
    // include all URLs that start with /public
    // include: [
    //   '/about-us',
    //   '/finance',
    //   '/finance-boston',
    //   '/finance-sacramento',
    //   '/finance-for-chicago',
    //   '/feeds',
    //   '/feeds/**',
    //   '/blog/',
    // ],
    exclude: [
      // List the paths you want to disallow
      '/home',
      '/archive',
      '/search',
      '/settings/**',
      '/help',
      '/status',
      '/payment',
      '/authuser/set-password',
      '/hoskin',
      '/error',
      '/agent-book',
      '/pricingForDevOnly',
      '/pricing-calculate',
    ],
  },
  routeRules: {
    // modify the sitemap.xml entry for specific URLs
    '/': {
      sitemap: { lastmod: '2024-11-22T00:00:00+00:00', priority: 1.0 },
    },
    'industries/': {
      sitemap: { lastmod: '2024-11-22T00:00:00+00:00', priority: 0.8 },
    },
    'about-us/': {
      sitemap: { lastmod: '2024-11-22T00:00:00+00:00', priority: 0.8 },
    },
    'faq/': {
      sitemap: { lastmod: '2024-11-22T00:00:00+00:00', priority: 0.8 },
    },
    'terms-and-conditions/': {
      sitemap: { lastmod: '2024-11-22T00:00:00+00:00', priority: 0.64 },
    },
    'terms-of-service/': {
      sitemap: { lastmod: '2024-11-22T00:00:00+00:00', priority: 0.64 },
    },
    'privacy-policy/': {
      sitemap: { lastmod: '2024-11-22T00:00:00+00:00', priority: 0.64 },
    },
    'partner-program/': {
      sitemap: { lastmod: '2024-11-22T00:00:00+00:00', priority: 0.64 },
    },
    'contact/': {
      sitemap: { lastmod: '2024-11-22T00:00:00+00:00', priority: 0.64 },
    },
    'release-notes/': {
      sitemap: { lastmod: '2024-11-22T00:00:00+00:00', priority: 0.64 },
    },
    '/home': {
      security: {
        rateLimiter: false,
        headers: {
          // crossOriginEmbedderPolicy: 'require-corp',
          contentSecurityPolicy: {
            'script-src': [
              "'self'",
              'https://ct.capterra.com',
              `${process.env.NUXT_PUBLIC_APP_URL}`,
              `${process.env.NUXT_CONTENT_SECURITY_POLICY}`,
              "'unsafe-inline'",
              "'strict-dynamic'",
              "'nonce-{{nonce}}'",
              'https://maps.googleapis.com',
              'https://share.hsforms.com',
              'https://assets.calendly.com',
              'https://js.stripe.com',
              'http://www.tiktok.com',
              'https://sharparchive.com/_nuxt/',
              'https:',
              'https://www.gstatic.com',
              'https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css',
            ],
            'style-src': [
              "'self'",
              'https://ct.capterra.com',
              `${process.env.NUXT_PUBLIC_APP_URL}`,
              'https://fonts.googleapis.com',
              'https://js.stripe.com',
              `${process.env.NUXT_CONTENT_SECURITY_POLICY}`,
              'http://www.tiktok.com',
              'https://sharparchive.com/_nuxt/',
              'https:',
              "'unsafe-inline'",
              'https://www.gstatic.com',
              'https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css',
            ],
            'img-src': [
              "'self'",
              `${process.env.NUXT_PUBLIC_APP_URL}`,
              'blob:',
              'https://www.facebook.com',
              'https://js.stripe.com',
              `${process.env.NUXT_CONTENT_SECURITY_POLICY}`,
              'data:',
              'https://ct.capterra.com',
              'https://forms.hsforms.com',
              'https://www.google.com',
              'https://www.google.com.bd',
              'https://maps.gstatic.com',
              'https://maps.googleapis.com',
              'https://lh3.googleusercontent.com',
              'https://i.pinimg.com',
              'https://styles.redditmedia.com',
              'https://p16-sign.tiktokcdn-us.com',
              'https://www.googleadservices.com',
              'https://googleads.g.doubleclick.net',
              'https://share.hsforms.com',
              'http://www.tiktok.com',
              'https://sharparchive.com/_nuxt/',
              'https:',
              'https://www.gstatic.com',
              'https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css',
            ],
            'font-src': [
              "'self'",
              'https://ct.capterra.com',
              'https://fonts.gstatic.com',
              'https://fonts.googleapis.com',
              'https://js.stripe.com',
              `${process.env.NUXT_PUBLIC_APP_URL}`,
              'https://sharparchive.com/',
              'https://sharparchive.com/_nuxt/',
              'data:',
              'https://www.gstatic.com',
              'https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css',
            ],
            'worker-src': ["'self'", 'blob:'],
            'connect-src': [
              "'self'",
              'https://ct.capterra.com',
              'https://pdf-generator.sharparchive.com/upload-html',
              'https://pdf-generator.sharparchive.com/check-pdf',
              'https://pdf-generator.sharparchive.com/delete-pdf',
              'https://sa-pdf-generator.s3.us-east-2.amazonaws.com/',
              'https://wznojprm72iww227v2kvj47x340dxezx.lambda-url.us-east-2.on.aws/',
              `${process.env.NUXT_PUBLIC_APP_URL}`,
              `${process.env.NUXT_CONTENT_SECURITY_POLICY}`,
              `${process.env.NUXT_PUBLIC_WS_HOST}`,
              'https://js.stripe.com',
              'https://api.hubapi.com',
              'https://forms.hscollectedforms.net',
              'https://o1049196.ingest.sentry.io',
              'https://o1049196.ingest.us.sentry.io',
              'https://google.com',
              'https://analytics.google.com',
              'https://maps.googleapis.com',
              'https://share.hsforms.com',
              'share.hsforms.com',
              'https://stats.g.doubleclick.net',
              'https://merchant-ui-api.stripe.com/link/set-cookie',
              'wss://ws.hotjar.com',
              'http://www.tiktok.com',
              'https://sharparchive.com/_nuxt/',
              'https:',
              'https://www.gstatic.com',
              'https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css',
            ],
            'object-src': ["'none'"],
            'base-uri': ["'none'"],
            // Do not use default-src
          },
        },
      },
    },
    '/archive': {
      security: {
        rateLimiter: false,
        headers: {
          // crossOriginEmbedderPolicy: 'require-corp',
          contentSecurityPolicy: {
            'script-src': [
              "'self'",
              'https://ct.capterra.com',
              `${process.env.NUXT_PUBLIC_APP_URL}`,
              `${process.env.NUXT_CONTENT_SECURITY_POLICY}`,
              "'unsafe-inline'",
              "'strict-dynamic'",
              "'nonce-{{nonce}}'",
              'https://maps.googleapis.com',
              'https://share.hsforms.com',
              'https://assets.calendly.com',
              'https://js.stripe.com',
              'http://www.tiktok.com',
              'https://sharparchive.com/_nuxt/',
              'https:',
              'https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css',
            ],
            'style-src': [
              "'self'",
              'https://ct.capterra.com',
              `${process.env.NUXT_PUBLIC_APP_URL}`,
              'https://fonts.googleapis.com',
              'https://js.stripe.com',
              `${process.env.NUXT_CONTENT_SECURITY_POLICY}`,
              'http://www.tiktok.com',
              'https://sharparchive.com/_nuxt/',
              'https:',
              "'unsafe-inline'",
              'https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css',
            ],
            'img-src': [
              "'self'",
              `${process.env.NUXT_PUBLIC_APP_URL}`,
              'blob:',
              'https://www.facebook.com',
              'https://js.stripe.com',
              `${process.env.NUXT_CONTENT_SECURITY_POLICY}`,
              'data:',
              'https://ct.capterra.com',
              'https://forms.hsforms.com',
              'https://www.google.com',
              'https://www.google.com.bd',
              'https://maps.gstatic.com',
              'https://maps.googleapis.com',
              'https://lh3.googleusercontent.com',
              'https://i.pinimg.com',
              'https://styles.redditmedia.com',
              'https://p16-sign.tiktokcdn-us.com',
              'https://www.googleadservices.com',
              'https://googleads.g.doubleclick.net',
              'https://share.hsforms.com',
              'http://www.tiktok.com',
              'https://sharparchive.com/_nuxt/',
              'https:',
              'https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css',
            ],
            'font-src': [
              "'self'",
              'https://ct.capterra.com',
              'https://fonts.gstatic.com',
              'https://fonts.googleapis.com',
              'https://js.stripe.com',
              `${process.env.NUXT_PUBLIC_APP_URL}`,
              'https://sharparchive.com/',
              'https://sharparchive.com/_nuxt/',
              'data:',
              'https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css',
            ],
            'worker-src': ["'self'", 'blob:'],
            'connect-src': [
              "'self'",
              'https://ct.capterra.com',
              'https://pdf-generator.sharparchive.com/upload-html',
              'https://pdf-generator.sharparchive.com/check-pdf',
              'https://pdf-generator.sharparchive.com/delete-pdf',
              'https://sa-pdf-generator.s3.us-east-2.amazonaws.com/',
              'https://wznojprm72iww227v2kvj47x340dxezx.lambda-url.us-east-2.on.aws/',
              `${process.env.NUXT_PUBLIC_APP_URL}`,
              `${process.env.NUXT_CONTENT_SECURITY_POLICY}`,
              `${process.env.NUXT_PUBLIC_WS_HOST}`,
              'https://js.stripe.com',
              'https://api.hubapi.com',
              'https://forms.hscollectedforms.net',
              'https://o1049196.ingest.sentry.io',
              'https://o1049196.ingest.us.sentry.io',
              'https://google.com',
              'https://analytics.google.com',
              'https://maps.googleapis.com',
              'https://share.hsforms.com',
              'share.hsforms.com',
              'https://stats.g.doubleclick.net',
              'https://merchant-ui-api.stripe.com/link/set-cookie',
              'wss://ws.hotjar.com',
              'http://www.tiktok.com',
              'https://sharparchive.com/_nuxt/',
              'https:',
              'https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css',
            ],
            'object-src': ["'none'"],
            'base-uri': ["'none'"],
            // Do not use default-src
          },
        },
      },
    },
    '/search': {
      security: {
        rateLimiter: false,
        headers: {
          // crossOriginEmbedderPolicy: 'require-corp',
          contentSecurityPolicy: {
            'script-src': [
              "'self'",
              'https://ct.capterra.com',
              `${process.env.NUXT_PUBLIC_APP_URL}`,
              `${process.env.NUXT_CONTENT_SECURITY_POLICY}`,
              "'unsafe-inline'",
              "'strict-dynamic'",
              "'nonce-{{nonce}}'",
              'https://maps.googleapis.com',
              'https://share.hsforms.com',
              'https://assets.calendly.com',
              'https://js.stripe.com',
              'http://www.tiktok.com',
              'https://sharparchive.com/_nuxt/',
              'https:',
              'https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css',
            ],
            'style-src': [
              "'self'",
              'https://ct.capterra.com',
              `${process.env.NUXT_PUBLIC_APP_URL}`,
              'https://fonts.googleapis.com',
              'https://js.stripe.com',
              `${process.env.NUXT_CONTENT_SECURITY_POLICY}`,
              'http://www.tiktok.com',
              'https://sharparchive.com/_nuxt/',
              'https:',
              "'unsafe-inline'",
              'https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css',
            ],
            'img-src': [
              "'self'",
              `${process.env.NUXT_PUBLIC_APP_URL}`,
              'blob:',
              'https://www.facebook.com',
              'https://js.stripe.com',
              `${process.env.NUXT_CONTENT_SECURITY_POLICY}`,
              'data:',
              'https://ct.capterra.com',
              'https://forms.hsforms.com',
              'https://www.google.com',
              'https://www.google.com.bd',
              'https://maps.gstatic.com',
              'https://maps.googleapis.com',
              'https://lh3.googleusercontent.com',
              'https://i.pinimg.com',
              'https://styles.redditmedia.com',
              'https://p16-sign.tiktokcdn-us.com',
              'https://www.googleadservices.com',
              'https://googleads.g.doubleclick.net',
              'https://share.hsforms.com',
              'http://www.tiktok.com',
              'https://sharparchive.com/_nuxt/',
              'https:',
              'https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css',
            ],
            'font-src': [
              "'self'",
              'https://fonts.gstatic.com',
              'https://fonts.googleapis.com',
              'https://js.stripe.com',
              `${process.env.NUXT_PUBLIC_APP_URL}`,
              'https://sharparchive.com/',
              'https://sharparchive.com/_nuxt/',
              'data:',
              'https://ct.capterra.com',
              'https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css',
            ],
            'worker-src': ["'self'", 'blob:'],
            'connect-src': [
              "'self'",
              'https://ct.capterra.com',
              'https://pdf-generator.sharparchive.com/upload-html',
              'https://pdf-generator.sharparchive.com/check-pdf',
              'https://pdf-generator.sharparchive.com/delete-pdf',
              'https://sa-pdf-generator.s3.us-east-2.amazonaws.com/',
              'https://wznojprm72iww227v2kvj47x340dxezx.lambda-url.us-east-2.on.aws/',
              `${process.env.NUXT_PUBLIC_APP_URL}`,
              `${process.env.NUXT_CONTENT_SECURITY_POLICY}`,
              `${process.env.NUXT_PUBLIC_WS_HOST}`,
              'https://js.stripe.com',
              'https://api.hubapi.com',
              'https://forms.hscollectedforms.net',
              'https://o1049196.ingest.sentry.io',
              'https://o1049196.ingest.us.sentry.io',
              'https://google.com',
              'https://analytics.google.com',
              'https://maps.googleapis.com',
              'https://share.hsforms.com',
              'share.hsforms.com',
              'https://stats.g.doubleclick.net',
              'https://merchant-ui-api.stripe.com/link/set-cookie',
              'wss://ws.hotjar.com',
              'http://www.tiktok.com',
              'https://sharparchive.com/_nuxt/',
              'https:',
              'https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css',
            ],
            'object-src': ["'none'"],
            'base-uri': ["'none'"],
            // Do not use default-src
          },
        },
      },
    },
    '/settings/**': {},
    '/help': {},
  },
  // delayHydration: {
  //   // enables nuxt-delay-hydration in dev mode for testing
  //   debug: process.env.NUXT_PUBLIC_WORKFLOW === 'dev',
  //   mode: 'mount',
  // },
  devtools: {
    enabled: true,

    timeline: {
      enabled: true,
    },
  },
  nitro: {
    devStorage: {
      cache: {
        driver: 'fs',
        base: './data/cache',
      },
    },
    routeRules: {
      '/server/**': {
        proxy: { to: `${process.env.NUXT_API_URL}/**` },
        swr: false,
        maxAge: 0,
      },
    },
    // prerender: {
    //   routes: ['/'], // Only prerender the root page
    //   exclude: ['/about-us', '/contact'], // Exclude specific routes
    // },
  },
  features: {
    inlineStyles: false,
  },
  // facebook: {
  //   /* module options */
  //   pixelId: process.env.NUXT_PUBLIC_FACEBOOK_PIXEL_ID,
  //   autoPageView: true,
  // },
  devServer: {
    https: {
      key: './localhost-key.pem',
      cert: './localhost.pem',
    },
  },
  // server: {
  //   port: 3000, // default: 3000
  //   host: '0.0.0.0', // default: localhost
  // },   // other configs
  lodash: {
    prefix: '_',
    prefixSkip: ['string'],
    upperAfterPrefix: false,
  },
  // nuxt image optimize
  image: {
    // Options
  },
})
