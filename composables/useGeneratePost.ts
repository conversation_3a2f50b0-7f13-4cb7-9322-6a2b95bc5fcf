import { useStore } from 'vuex'
import type {
  Hashtag,
  PreviewContent,
  Tag,
} from '~/composables/useSavePostSettings'

export const useGeneratePost = () => {
  const store = useStore()

  const text = ref('')
  const selectedImages = ref<string[]>([])
  const selectedHashtags = ref<Hashtag[]>([])
  const selectedTags = ref<Tag[]>([])

  const previewContent = computed<PreviewContent | null>(
    () => store.state.createPost.previewContent,
  )
  const updatePreviewContent = (content: PreviewContent) => {
    store.commit('createPost/UPDATE_PREVIEW_CONTENT', content)
  }

  watch(text, (newValue) => {
    updatePreviewContent({ text: newValue })
  })

  watch(
    () => previewContent.value,
    (newContent) => {
      if (newContent) {
        if (newContent.text !== undefined) {
          text.value = newContent.text
        }
        if (newContent.images !== undefined) {
          selectedImages.value = newContent.images
        }
        if (newContent.hashtags !== undefined) {
          selectedHashtags.value = newContent.hashtags
        }
        if (newContent.tags !== undefined) {
          selectedTags.value = newContent.tags
        }
      }
    },
    { immediate: true, deep: true },
  )

  return {
    text,
    selectedImages,
    selectedHashtags,
    selectedTags,
    updatePreviewContent,
  }
}
