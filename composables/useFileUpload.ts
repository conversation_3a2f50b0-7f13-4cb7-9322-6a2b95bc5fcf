export const useFileUpload = () => {
  const avatarPreview = ref<string>('')
  const onFileSelected = (event: Event) => {
    const target = event.target as HTMLInputElement
    // selectFile.value = target.files?.[0]
    let reader
    const files = target.files
    if (files && files.length > 0) {
      reader = new FileReader()
      reader.onload = (event) => {
        avatarPreview.value = event.target?.result as string
        // profileUserInfo.value.avatar = selectFile.value
      }
      reader.readAsDataURL(files[0])
    }
  }

  return {
    avatarPreview,
    onFileSelected,
  }
}
