import { addDays, isSameWeek, startOfDay, subDays } from 'date-fns'

export interface MonthYear {
  month: number
  year: number
}

export interface WeekRange {
  start: Date
  end: Date
}

export type CalendarTab = 'List' | 'Week' | 'Month'

export function useCalendarManager() {
  const { calendarApi, setCalendarApi } = useCalendarApi()
  const route = useRoute()

  // State
  const selectedDate = ref<Date | null>(new Date())
  const currentTab = ref<CalendarTab>('List')
  const currentTitle = ref('')
  const currentYear = ref(new Date().getFullYear())
  const currentMonthYear = ref<MonthYear>({
    month: new Date().getMonth(),
    year: new Date().getFullYear(),
  })
  const weekRange = ref<WeekRange>({
    start: new Date(),
    end: new Date(),
  })

  // Computed properties
  const isToday = computed(() => {
    if (currentTab.value !== 'List' || !selectedDate.value) {
      return false
    }
    return (
      startOfDay(selectedDate.value).getTime() ===
      startOfDay(new Date()).getTime()
    )
  })

  const isThisWeek = computed(() => {
    if (currentTab.value !== 'Week' || !weekRange.value.start) {
      return false
    }
    return isSameWeek(weekRange.value.start, new Date())
  })

  const isThisMonth = computed(() => {
    if (currentTab.value !== 'Month') {
      return false
    }

    const now = new Date()
    const currentMonthIndex = now.getMonth()
    const currentYearMatch = currentYear.value === now.getFullYear()

    const monthMatch = currentTitle.value.match(/^([A-Za-z]+)/)
    if (!monthMatch) return false

    const titleMonth = new Date(`${monthMatch[1]} 1, 2000`).getMonth()
    return currentYearMatch && titleMonth === currentMonthIndex
  })

  // Methods for calendar synchronization
  const synchronizeCalendarRange = () => {
    setTimeout(() => {
      if (currentTab.value === 'Week' && calendarApi.value) {
        const start = calendarApi.value.view.activeStart
        const end = calendarApi.value.view.activeEnd
        const adjustedEnd = subDays(end, 1)
        weekRange.value = { start, end: adjustedEnd }
      }
    }, 700)
  }

  const synchronizeCalendarMonth = () => {
    setTimeout(() => {
      if (currentTab.value === 'Month' && calendarApi.value) {
        const date = calendarApi.value.getDate()
        currentMonthYear.value = {
          month: date.getMonth(),
          year: date.getFullYear(),
        }
      }
    }, 700)
  }

  const updateTitle = () => {
    if (calendarApi.value) {
      currentTitle.value = calendarApi.value.view.title
    }
  }

  // Navigation methods
  const goToToday = () => {
    if (calendarApi.value) {
      calendarApi.value.today()
      synchronizeCalendarRange()
      synchronizeCalendarMonth()
      updateTitle()
    }
  }

  const goToPrev = () => {
    if (calendarApi.value) {
      calendarApi.value.prev()
      synchronizeCalendarRange()
      synchronizeCalendarMonth()
      updateTitle()
    }
  }

  const goToNext = () => {
    if (calendarApi.value) {
      calendarApi.value.next()
      synchronizeCalendarRange()
      synchronizeCalendarMonth()
      updateTitle()
    }
  }

  const handleGoToPrev = () => {
    if (selectedDate.value && currentTab.value === 'List') {
      selectedDate.value = subDays(selectedDate.value, 1)
    }
    goToPrev()
  }

  const handleGoToNext = () => {
    if (selectedDate.value && currentTab.value === 'List') {
      selectedDate.value = addDays(selectedDate.value, 1)
    }
    goToNext()
  }

  const handleGoToToday = () => {
    selectedDate.value = new Date()
    goToToday()
  }

  const handleTabChange = () => {
    setCalendarApi(null)
    synchronizeCalendarRange()
    synchronizeCalendarMonth()
  }

  const setMonthYear = (monthYear: MonthYear) => {
    currentMonthYear.value = monthYear
    if (calendarApi.value) {
      calendarApi.value.gotoDate(new Date(monthYear.year, monthYear.month, 1))
      updateTitle()
    }
  }

  // Initial setup and watchers
  watch(
    () => currentTitle.value,
    (newTitle) => {
      const yearMatch = newTitle.match(/\d{4}/)
      if (yearMatch) {
        currentYear.value = parseInt(yearMatch[0])
      }
    },
  )

  watch(
    [calendarApi, weekRange, currentTab, currentMonthYear],
    ([api, newRange, tab, monthYear]) => {
      if (api) {
        updateTitle()

        if (tab === 'Week' && newRange) {
          api.gotoDate(newRange.start)
          updateTitle()
        } else if (tab === 'Month' && monthYear) {
          api.gotoDate(new Date(monthYear.year, monthYear.month, 1))
          updateTitle()
        }
      }
    },
    { immediate: true, deep: true },
  )

  onMounted(() => {
    const postHash = route.name.split('-')[2]
    if (postHash === 'list' || postHash === 'week' || postHash === 'month') {
      currentTab.value = postHash.charAt(0).toUpperCase() + postHash.slice(1)
    }
  })

  return {
    // State
    selectedDate,
    currentTab,
    currentTitle,
    currentMonthYear,
    weekRange,
    // Computed
    isToday,
    isThisWeek,
    isThisMonth,
    // Methods
    synchronizeCalendarRange,
    synchronizeCalendarMonth,
    updateTitle,
    goToToday,
    goToPrev,
    goToNext,
    handleGoToPrev,
    handleGoToNext,
    handleGoToToday,
    handleTabChange,
    setMonthYear,
    setCalendarApi,
  }
}
