export const useFetched = () => {
  const fetch = async (request, options, accessToken) => {
    const tokenCookie = useCookie('token')

    return await $fetch(request, {
      ...options,
      headers: {
        'Cache-Control': 'no-cache',
        Authorization:
          tokenCookie.value || accessToken
            ? `Bearer ${tokenCookie.value || accessToken}`
            : '',
        'X-CSRF-Token': tokenCookie.value,
      },
    })
  }

  return {
    fetch,
  }
}
