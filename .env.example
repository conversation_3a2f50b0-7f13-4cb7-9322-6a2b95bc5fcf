# app name
NUXT_PUBLIC_APP_NAME="Sharp Archive"
# app short name
NUXT_PUBLIC_APP_SHORT_NAME="SharpArchive"
# app url
NUXT_PUBLIC_APP_URL=https://localhost:3000
# api fake url
API_FAKE_URL=http://localhost:9000

# api base url/api
NUXT_API_URL=https://dev-api.sharparchive.com/api
NUXT_PUBLIC_API_URL=https://dev-api.sharparchive.com/api

NUXT_CONTENT_SECURITY_POLICY='https://dev-api.sharparchive.com'

# stripe key
NUXT_PUBLIC_STRIPE_KEY=pk_test_51JPtOfFxsmIraXHUAgpplP9Gxn4LxlAnIgPgEaP5ruKyycAdJvv8ZrHHuPq11EQIDPSzDDjen1hZzF8cRiMqHOQg00kf7ORCg1
# google map key
GOOGLE_MAP_KEY=AIzaSyBMQgZvqzCVG0FFT5RsE59wjEYzQLncahI
# google tag manager id
GOOGLE_TAG_MANAGER_ID=GTM-KKLJCQB
GOOGLE_ADS_TAG_ID_1=AW-***********
GOOGLE_ADS_TAG_ID_2=AW-***********
# ngrok auth token
NGROK_AUTHTOKEN=1qGWGeg2Wpiu0pn7F1axpcEPz0I_24CBoaHvmtGus8FT28gs4
# facebook client id
NUXT_PUBLIC_SOCIAL_FACEBOOK_CLIENT_ID=2134221493410644
NUXT_PUBLIC_SOCIAL_FACEBOOK_REDIRECT_URL="${NUXT_PUBLIC_APP_URL}/authorize/facebook"
NUXT_PUBLIC_SOCIAL_FACEBOOK_SCOPE="email public_profile user_posts business_management pages_show_list pages_read_engagement pages_read_user_content page_events groups_access_member_info instagram_basic pages_messaging"
# instagram client id
NUXT_PUBLIC_SOCIAL_INSTAGRAM_CLIENT_ID=723774612148635
NUXT_PUBLIC_SOCIAL_INSTAGRAM_REDIRECT_URL="${NUXT_PUBLIC_APP_URL}/authorize/instagram"
NUXT_PUBLIC_SOCIAL_INSTAGRAM_SCOPE="user_profile user_media"
# linkedin client id
NUXT_PUBLIC_SOCIAL_LINKEDIN_CLIENT_ID=86rkl9h4s5i1lf
NUXT_PUBLIC_SOCIAL_LINKEDIN_REDIRECT_URL="${NUXT_PUBLIC_APP_URL}/authorize/linkedin"
NUXT_PUBLIC_SOCIAL_LINKEDIN_SCOPE="r_emailaddress r_basicprofile r_organization_social rw_organization_admin"
# twitter client id
NUXT_PUBLIC_SOCIAL_TWITTER_CLIENT_ID=SVFBc2NHNEZtRVRTVG52dGZFZWg6MTpjaQ
NUXT_PUBLIC_SOCIAL_TWITTER_REDIRECT_URL="${NUXT_PUBLIC_APP_URL}/authorize/twitter"
NUXT_PUBLIC_SOCIAL_TWITTER_SCOPE="tweet.read users.read follows.read offline.access space.read mute.read like.read list.read block.read"
# pinterest client id
NUXT_PUBLIC_SOCIAL_PINTEREST_CLIENT_ID=1484299
NUXT_PUBLIC_SOCIAL_PINTEREST_REDIRECT_URL="https://localhost:3000/authorize/pinterest"
NUXT_PUBLIC_SOCIAL_PINTEREST_SCOPE="boards:read, boards:read_secret, pins:read, pins:read_secret, ads:read, catalogs:read, user_accounts:read"
# reddit client id
NUXT_PUBLIC_SOCIAL_REDDIT_CLIENT_ID=YCT467SfNeQkt3BqA7q93g
NUXT_PUBLIC_SOCIAL_REDDIT_REDIRECT_URL="https://localhost:3000/authorize/reddit"
NUXT_PUBLIC_SOCIAL_REDDIT_SCOPE="identity read history mysubreddits privatemessages"
# google client id
NUXT_PUBLIC_SOCIAL_GOOGLE_CLIENT_ID=************-ih7t44lf8eklje0150sq1ui19f9puabv.apps.googleusercontent.com
NUXT_PUBLIC_SOCIAL_GOOGLE_REDIRECT_URL="${NUXT_PUBLIC_APP_URL}/authorize/google"
NUXT_PUBLIC_SOCIAL_GOOGLE_SCOPE="https://www.googleapis.com/auth/userinfo.email https://www.googleapis.com/auth/userinfo.profile https://www.googleapis.com/auth/gmail.readonly"
# microsoft client id
NUXT_PUBLIC_SOCIAL_MICROSOFT_CLIENT_ID=2aa3a4f6-76b0-46a6-83da-02e4d90c4f21
NUXT_PUBLIC_SOCIAL_MICROSOFT_REDIRECT_URL="${NUXT_PUBLIC_APP_URL}/authorize/microsoft"
NUXT_PUBLIC_SOCIAL_MICROSOFT_SCOPE="offline_access User.Read User.ReadBasic.All Mail.Read Mail.Read.Shared"
# youtube client id
NUXT_PUBLIC_SOCIAL_YOUTUBE_CLIENT_ID = 642815832269-oj6j6567biqpi8434pqm5hb2rve98puo.apps.googleusercontent.com
NUXT_PUBLIC_SOCIAL_YOUTUBE_REDIRECT_URL = "${NUXT_PUBLIC_APP_URL}/authorize/youtube"
NUXT_PUBLIC_SOCIAL_YOUTUBE_SCOPE = "https://www.googleapis.com/auth/userinfo.email https://www.googleapis.com/auth/userinfo.profile https://www.googleapis.com/auth/youtube.force-ssl"
# google analytics
GOOGLE_MEASUREMENT_ID=G-QFTYD4W9S1
# facebook pixel
NUXT_PUBLIC_FACEBOOK_PIXEL_ID=520303615079381
META_KEYWORDS="Document review platforms, eDiscovery service providers, eDiscovery software, Ediscovery tool, Mirror web, Relativity ediscovery, Ringtail ediscovery, social media archive, Text message archive, Archival and primary source websites, Archive softwares, social media archive, social media monitoring software, Web archiving, Archive sms, Archive SMS messages iPhone, Im archiving, Instant message archiving, archiving solutions, archiving services, email archiving too, instagram archive, archiving software, global relay social media archiving, smarsh archiving, twitter archive, website and social media archiving, social media archiving software, archiving all instagram posts, social media archiving, archivesocial, email archiving service, twitter deleted tweets archive, office 365 email archiving, email archiving software, email archiving tools, email archiving solution, smarsh email archiving, best archive software, pagefreezer pricing, social media archiving solutions, how to archive emails on outlook, email archive system, best email archiving solution, social media recovery service, archiving emails, how to see archived posts on instagram, archive social media content, email archiving, archiving software reviews, archiving facebook posts, smarsh competitors, archive social pricing, archivesocial com, how to archive instagram, how to archive office 365 emails, email archive, how to archive all instagram posts at once, email archiving solutions, how to archive exchange email,	how to find old tweets on twitter, email archiving software, office 365 email archiving solutions, social media archiving services, social media archiving for government, facebook archiving software, website and social media archiving, facebook archiving software, best archive software, email archiving companies, how to archive emails in outlook, smarsh alternatives, smarsh social media, email archive solutions, archive emails outlook 365, exchange archiving, automatically archive outlook, global relay alternatives, pagefreezer cost, social media archiving for Government Agencies, archive social competitors, how to archive youtube videos, Social Media Archiving Software, small business email archiving solutions, pagefreezer alternatives, outlook backup software, social media archiving for financial advisors, finra social media archiving, how to return archived post on instagram, jatheon alternatives, onna alternatives, how to retrieve archived post on instagram, archive outlook emails, archive tweets, archive twitter feed, how to archive twitter, smarsh social media archiving, how to archive facebook account, email archiving compliance solutions, zl email archiving, how to repost archived posts instagram, how to archive facebook page"
# NODE_TLS_REJECT_UNAUTHORIZED = 0
# platform environment, local/dev/staging/live
NUXT_PUBLIC_WORKFLOW=dev

# Web socket Host
NUXT_PUBLIC_WS_HOST=wss://dev-api.sharparchive.com

NUXT_PUBLIC_AWS_CDN=https://cdn.sharparchive.com/dev-files

"jest/globals": true

REDIS_URL="redis://default:@127.0.0.1:6379"

NUXT_PUBLIC_SENTRY_DSN="https://<EMAIL>/6253144"