const browseStore = {
  namespaced: true,
  state() {
    return {
      facebookPosts: [
        {
          // Core post properties
          id: 'post123456',
          type: 'photo', // Possible values: "photo", "video", "reel", "event"
          statusType: 'added_photos', // Possible values: "added_photos", "added_video", "shared_story", "mobile_status_update", "created_event"
          provider: 'Facebook',

          // Profile information
          profilename: '<PERSON>',
          profileImageUrl: '/social/profile-picture.png',

          // Post content
          text: 'This is the main text content of the post.',
          description: '',
          link: 'https://example.com/linked-content',
          linkName: '',
          caption: '',

          // Dates
          createdAt: '2025-03-15T12:00:00Z',
          updatedAt: '2025-03-15T14:30:00Z',

          // Location information
          placeName: '',
          placeCity: '',

          // Privacy settings
          privacy: 'Public', // Possible values: "Public", "Friends", "Friends of friends", "Only me", "Your friends", "Custom"

          // Mentions
          mentionText: '',
          // "With <a href='#'>@<PERSON></a> and <a href='#'>@<PERSON></a>",

          // Engagement metrics
          reactionsCount: 42,
          commentsCount: 15,
          sharesCount: 7,

          // Media content
          fullPicture: 'https://picsum.photos/800/600',
          archivedFullPicture: 'https://picsum.photos/800/600',
          sourceUrl:
            'https://dev-api.sharparchive.com/api/social/media/17960511854508413_1676274412.mp4?provider=Instagram&u=3&a=311&c=source_url',
          archivedSourceUrl:
            'https://dev-api.sharparchive.com/api/social/media/17960511854508413_1676274412.mp4?provider=Instagram&u=3&a=311&c=source_url',

          // Multiple images
          attachmentImages: ['https://picsum.photos/800/600'],
          archivedAttachmentImages: ['https://picsum.photos/800/600'],

          // Event information
          event: null,
        },
        {
          // Video post
          id: 'post345678',
          type: 'video',
          statusType: 'added_video',
          provider: 'Facebook',
          profilename: 'Mike Johnson',
          profileImageUrl: '/social/profile-picture.png',
          text: 'Check out this cool video I made!',
          createdAt: '2025-03-14T18:45:00Z',
          privacy: 'Public',
          sourceUrl:
            'https://dev-api.sharparchive.com/api/social/media/17960511854508413_1676274412.mp4?provider=Instagram&u=3&a=311&c=source_url',
          fullPicture: 'https://picsum.photos/800/600',
          reactionsCount: 108,
          commentsCount: 32,
          sharesCount: 15,
          archivedAttachmentImages: [],
          attachmentImages: [],
        },
        {
          // Photo post with multiple images
          id: 'post234567',
          type: 'photo',
          statusType: 'added_photos',
          provider: 'Facebook',
          profilename: 'Emily Davis',
          profileImageUrl: '/social/profile-picture.png',
          text: 'Vacation memories from last week!',
          createdAt: '2025-03-12T20:15:00Z',
          placeName: 'Paradise Beach Resort',
          placeCity: 'Cancun',
          privacy: 'Friends',
          attachmentImages: [
            'https://picsum.photos/800/600',
            'https://picsum.photos/800/600',
            'https://picsum.photos/800/600',
            'https://picsum.photos/800/600',
            'https://picsum.photos/800/600',
            'https://picsum.photos/800/600',
          ],
          reactionsCount: 154,
          commentsCount: 37,
          sharesCount: 3,
          archivedAttachmentImages: [
            'https://picsum.photos/800/600',
            'https://picsum.photos/800/600',
            'https://picsum.photos/800/600',
            'https://picsum.photos/800/600',
            'https://picsum.photos/800/600',
            'https://picsum.photos/800/600',
          ],
        },
      ],
      instagramPosts: [
        {
          // Basic user information
          userName: 'Sharparchive',
          isVerified: true,
          profileImageUrl: '/social/profile-picture.png',
          // Post content
          type: 'CAROUSEL_ALBUM', // Options: "CAROUSEL_ALBUM", "IMAGE", "VIDEO"
          text: 'Exploring the beautiful beaches of Bali 🌊🏝️\n Swipe to see more pics from this amazing trip! #bali #travelgram #paradise',
          provider: 'Instagram',
          createdAt: '2025-02-20T14:30:00Z', // ISO date format
          likeCount: 1234,
          commentsCount: 123,
          // Media URLs
          sourceUrl:
            'https://dev-api.sharparchive.com/api/social/media/103338412240799_612690651356536_1738219084_0.jpg?provider=Facebook&u=3&a=61&c=attachment_images',
          archivedSourceUrl:
            'https://dev-api.sharparchive.com/api/social/media/103338412240799_612690651356536_1738219084_0.jpg?provider=Facebook&u=3&a=61&c=attachment_images',
          archivedThumbnailUrl:
            'https://dev-api.sharparchive.com/api/social/media/103338412240799_612690651356536_1738219084_0.jpg?provider=Facebook&u=3&a=61&c=attachment_images',
          // For carousel albums
          archivedChildrenMedias: [
            'https://dev-api.sharparchive.com/api/social/media/103338412240799_612690651356536_1738219084_0.jpg?provider=Facebook&u=3&a=61&c=attachment_images',
            'https://dev-api.sharparchive.com/api/social/media/18311386162081629_1724848606.jpg?provider=Instagram&u=3&a=311&c=source_url',
            'https://dev-api.sharparchive.com/api/social/media/17991720661774025_1677044667.jpg?provider=Instagram&u=3&a=311&c=source_url',
          ],
        },
        {
          // Basic user information
          userName: 'Sharparchive',
          isVerified: false,
          profileImageUrl: '/social/profile-picture.png',
          // Post content
          type: 'IMAGE', // Options: "CAROUSEL_ALBUM", "IMAGE", "VIDEO"
          text: 'Beautiful sunset in Bali tonight 🌅 #nofilter #bali',
          provider: 'Instagram',
          createdAt: '2025-02-20T14:30:00Z', // ISO date format
          likeCount: 1234,
          commentsCount: 123,
          // Media URLs
          sourceUrl:
            'https://dev-api.sharparchive.com/api/social/media/103338412240799_612690651356536_1738219084_0.jpg?provider=Facebook&u=3&a=61&c=attachment_images',
          archivedSourceUrl:
            'https://dev-api.sharparchive.com/api/social/media/103338412240799_612690651356536_1738219084_0.jpg?provider=Facebook&u=3&a=61&c=attachment_images',
          archivedThumbnailUrl:
            'https://dev-api.sharparchive.com/api/social/media/103338412240799_612690651356536_1738219084_0.jpg?provider=Facebook&u=3&a=61&c=attachment_images',
          // For carousel albums
          archivedChildrenMedias: [],
        },
        {
          // Basic user information
          userName: 'Sharparchive',
          isVerified: true,
          profileImageUrl: '/social/profile-picture.png',
          // Post content
          type: 'VIDEO', // Options: "CAROUSEL_ALBUM", "IMAGE", "VIDEO"
          text: 'The waves are perfect today! 🏄‍♂️ #surfing #bali',
          provider: 'Instagram',
          createdAt: '2025-02-20T14:30:00Z', // ISO date format
          likeCount: 1234,
          commentsCount: 123,
          // Media URLs
          sourceUrl:
            'https://dev-api.sharparchive.com/api/social/media/17960511854508413_1676274412.mp4?provider=Instagram&u=3&a=311&c=source_url',
          archivedSourceUrl:
            'https://dev-api.sharparchive.com/api/social/media/17960511854508413_1676274412.mp4?provider=Instagram&u=3&a=311&c=source_url',
          archivedThumbnailUrl:
            'https://dev-api.sharparchive.com/api/social/media/103338412240799_612690651356536_1738219084_0.jpg?provider=Facebook&u=3&a=61&c=attachment_images',
          // For carousel albums
          archivedChildrenMedias: [],
        },
      ],
      twitterPosts: [
        {
          id: '**********',
          socialId: 'twitter_12345',
          name: 'Jane Smith',
          username: 'janesmith',
          profileImageUrl: '/social/profile-picture.png',
          text: 'Just published my latest article on web development trends for 2025! #WebDev #Programming',
          createdAt: '2025-02-25T14:30:00.000Z',
          replyCount: 5,
          retweetCount: 12,
          likeCount: 45,
          mediaUrl: [],
          mediaType: null,
          videoUrl: null,
          polls: [],
          provider: 'Twitter',
        },
        {
          id: '**********',
          socialId: 'twitter_23456',
          name: 'Tech News',
          username: 'technews',
          profileImageUrl: '/social/profile-picture.png',
          text: "Our team is at the annual tech conference! Here's a sneak peek at the latest gadgets. #TechConf2025",
          createdAt: '2025-02-24T09:15:00.000Z',
          replyCount: 8,
          retweetCount: 24,
          likeCount: 87,
          mediaUrl: [
            'https://dev-api.sharparchive.com/api/social/media/17991720661774025_1677044667.jpg?provider=Instagram&u=3&a=311&c=source_url',
          ],
          mediaType: 'photo',
          videoUrl: null,
          polls: [],
          provider: 'Twitter',
        },
        {
          id: '**********',
          socialId: 'twitter_34567',
          name: 'Travel Explorer',
          username: 'travelxplorer',
          profileImageUrl: '/social/profile-picture.png',
          text: 'Beautiful sunset views from my trip to Bali! The beaches are absolutely stunning. #TravelDiary #Bali',
          createdAt: '2025-02-23T18:45:00.000Z',
          replyCount: 12,
          retweetCount: 35,
          likeCount: 152,
          mediaUrl: [
            'https://dev-api.sharparchive.com/api/social/media/18311386162081629_1724848606.jpg?provider=Instagram&u=3&a=311&c=source_url',
            'https://dev-api.sharparchive.com/api/social/media/17991720661774025_1677044667.jpg?provider=Instagram&u=3&a=311&c=source_url',
          ],
          mediaType: 'photo',
          videoUrl: null,
          polls: [],
          provider: 'Twitter',
        },
        {
          id: '**********',
          socialId: 'twitter_78901',
          name: 'Sports Reporter',
          username: 'sportsupdate',
          profileImageUrl: '/social/profile-picture.png',
          text: 'What a game! Check out this incredible last-minute goal that won the championship. #SportsHighlight #Championship',
          createdAt: '2025-02-19T22:05:00.000Z',
          replyCount: 32,
          retweetCount: 156,
          likeCount: 478,
          mediaUrl: [],
          mediaType: 'video',
          videoUrl:
            'https://dev-api.sharparchive.com/api/social/media/17960511854508413_1676274412.mp4?provider=Instagram&u=3&a=311&c=source_url',
          polls: [],
          provider: 'Twitter',
        },
        {
          id: '**********',
          socialId: 'twitter_89012',
          name: 'Meme Channel',
          username: 'dailymemes',
          profileImageUrl: '/social/profile-picture.png',
          text: "When it's finally Friday and you've survived another week! 😂 #FridayFeeling #WeekendVibes",
          createdAt: '2025-02-18T15:30:00.000Z',
          replyCount: 18,
          retweetCount: 87,
          likeCount: 312,
          mediaUrl: [],
          mediaType: 'animated_gif',
          videoUrl:
            'https://dev-api.sharparchive.com/api/social/media/17960511854508413_1676274412.mp4?provider=Instagram&u=3&a=311&c=source_url',
          polls: [],
          provider: 'Twitter',
        },
        {
          id: '**********',
          socialId: 'twitter_90123',
          name: 'Tech Reviewer',
          username: 'gadgetreview',
          profileImageUrl: '/social/profile-picture.png',
          text: "What's your preferred smartphone operating system? Vote in our poll! #TechPoll #Smartphones",
          createdAt: '2025-02-17T13:45:00.000Z',
          replyCount: 25,
          retweetCount: 54,
          likeCount: 98,
          mediaUrl: [],
          mediaType: null,
          videoUrl: null,
          polls: [
            {
              voting_status: 'open',
              end_datetime: '2025-03-01T13:45:00.000Z',
              options: [
                {
                  label: 'iOS',
                  votes: 1250,
                },
                {
                  label: 'Android',
                  votes: 1380,
                },
                {
                  label: 'Other',
                  votes: 125,
                },
              ],
            },
          ],
          provider: 'Twitter',
        },
        {
          id: '**********',
          socialId: 'twitter_01234',
          name: 'Coffee Lovers',
          username: 'caffeineaddict',
          profileImageUrl: '/social/profile-picture.png',
          text: 'The results are in! The community has spoken about their favorite brewing method. #CoffeeLover #BrewingMethods',
          createdAt: '2025-02-16T08:20:00.000Z',
          replyCount: 14,
          retweetCount: 32,
          likeCount: 87,
          mediaUrl: [],
          mediaType: null,
          videoUrl: null,
          polls: [
            {
              voting_status: 'closed',
              end_datetime: '2025-02-23T08:20:00.000Z',
              options: [
                {
                  label: 'French Press',
                  votes: 856,
                },
                {
                  label: 'Pour Over',
                  votes: 712,
                },
                {
                  label: 'Espresso Machine',
                  votes: 923,
                },
                {
                  label: 'Aeropress',
                  votes: 598,
                },
              ],
            },
          ],
          provider: 'Twitter',
        },
        {
          id: '**********',
          socialId: 'twitter_12312',
          name: 'Digital Marketer',
          username: 'marketingstrats',
          profileImageUrl: '/social/profile-picture.png',
          text: 'This analysis of social media trends is spot on! A must-read for all marketers. #DigitalMarketing #SocialMedia',
          createdAt: '2025-02-15T10:05:00.000Z',
          replyCount: 3,
          retweetCount: 18,
          likeCount: 42,
          mediaUrl: [],
          mediaType: null,
          videoUrl: null,
          polls: [],
          referencedTweetsId: '**********',
          referencedTweetsType: 'retweeted',
          referencedTweetsName: 'Marketing Expert',
          referencedTweetsUsername: 'marketexpert',
          referencedTweetsProfileImageUrl:
            'https://dev-api.sharparchive.com/api/social/media/103338412240799_612690651356536_1738219084_0.jpg?provider=Facebook&u=3&a=61&c=attachment_images',
          referencedTweetsCreatedAt: '2025-02-14T16:30:00.000Z',
          provider: 'Twitter',
        },
        {
          id: '**********',
          socialId: 'twitter_23423',
          name: 'Fitness Coach',
          username: 'fitnessgoals',
          profileImageUrl: '/social/profile-picture.png',
          text: "Great point! I'd also add that consistency is key for any fitness routine. Small steps every day lead to big results over time. #FitnessTips",
          createdAt: '2025-02-14T14:25:00.000Z',
          replyCount: 1,
          retweetCount: 5,
          likeCount: 23,
          mediaUrl: [],
          mediaType: null,
          videoUrl: null,
          polls: [],
          referencedTweetsId: '**********',
          referencedTweetsType: 'replied_to',
          referencedTweetsName: 'Health Advisor',
          referencedTweetsUsername: 'healthtips',
          referencedTweetsProfileImageUrl: '/social/profile-picture.png',
          referencedTweetsCreatedAt: '2025-02-14T13:15:00.000Z',
          provider: 'Twitter',
        },
        {
          id: '**********',
          socialId: 'twitter_34534',
          name: 'Book Reviewer',
          username: 'bookworm',
          profileImageUrl: '/social/profile-picture.png',
          text: 'Just finished reading this amazing book!\n\nIt completely changed my perspective on climate change and what we can do as individuals.\n\nCheck out my full review: https://bookreview.example.com/climate-change\n\n#BookReview #ClimateChange',
          createdAt: '2025-02-13T20:10:00.000Z',
          replyCount: 7,
          retweetCount: 14,
          likeCount: 56,
          mediaUrl: [
            'https://dev-api.sharparchive.com/api/social/media/17991720661774025_1677044667.jpg?provider=Instagram&u=3&a=311&c=source_url',
          ],
          mediaType: 'photo',
          videoUrl: null,
          polls: [],
          provider: 'Twitter',
        },
        {
          id: '**********',
          socialId: 'twitter_45645',
          name: 'Anonymous User',
          username: 'anon123',
          profileImageUrl: null,
          text: 'Sometimes staying anonymous online is the best approach. Thoughts? #Privacy #OnlineSafety',
          createdAt: '2025-02-12T11:50:00.000Z',
          replyCount: 28,
          retweetCount: 42,
          likeCount: 97,
          mediaUrl: [],
          mediaType: null,
          videoUrl: null,
          polls: [],
          provider: 'Twitter',
        },
        {
          id: '**********',
          socialId: 'twitter_56756',
          name: 'Music Poll',
          username: 'musicfan',
          profileImageUrl: '/social/profile-picture.png',
          text: 'Which genre of music do you listen to most often? The votes are tied! #MusicPoll #FavoriteGenre',
          createdAt: '2025-02-11T16:35:00.000Z',
          replyCount: 11,
          retweetCount: 23,
          likeCount: 68,
          mediaUrl: [],
          mediaType: null,
          videoUrl: null,
          polls: [
            {
              voting_status: 'open',
              end_datetime: '2025-02-18T16:35:00.000Z',
              options: [
                {
                  label: 'Rock',
                  votes: 450,
                },
                {
                  label: 'Pop',
                  votes: 450,
                },
                {
                  label: 'Hip Hop',
                  votes: 380,
                },
                {
                  label: 'Classical',
                  votes: 220,
                },
              ],
            },
          ],
          provider: 'Twitter',
        },
      ],
      twitterComments: [
        {
          // Basic tweet with text only
          name: 'John Doe',
          username: 'johndoe',
          profileImageUrl: 'https://randomuser.me/api/portraits/men/1.jpg',
          text: "Just sharing my thoughts on Vue.js. It's a fantastic framework! <a href='https://vuejs.org' class='link'>vuejs.org</a>",
          createdAt: '2025-02-20T12:33:45Z',
          replyCount: 5,
          retweetCount: 12,
          likeCount: 38,
          mediaUrl: [],
          mediaType: null,
          videoUrl: [],
          replies: [
            {
              name: 'Sarah Wilson',
              username: 'sarahw',
              profileImageUrl:
                'https://randomuser.me/api/portraits/women/2.jpg',
              text: 'I completely agree! Vue.js has been my go-to framework for the past year.',
              createdAt: '2025-02-20T13:15:22Z',
              replyCount: 1,
              retweetCount: 0,
              likeCount: 5,
              mediaUrl: [],
              mediaType: null,
              videoUrl: [],
              replies: [],
              referencedTweetsUsername: 'johndoe',
            },
          ],
        },
        {
          // Tweet with a single image
          name: 'Emily Chen',
          username: 'emilychen',
          profileImageUrl: 'https://randomuser.me/api/portraits/women/3.jpg',
          text: 'Check out this amazing sunset from my balcony! 🌅',
          createdAt: '2025-02-21T18:42:32Z',
          replyCount: 8,
          retweetCount: 25,
          likeCount: 143,
          mediaUrl: ['https://picsum.photos/200'],
          mediaType: 'photo',
          videoUrl: [],
          replies: [],
        },
        {
          // Tweet with 3 images (special layout)
          name: 'Jessica Taylor',
          username: 'jessicat',
          profileImageUrl: 'https://randomuser.me/api/portraits/women/5.jpg',
          text: 'Weekend adventures with friends. Hiking, picnic, and stargazing!',
          createdAt: '2025-02-18T21:15:30Z',
          replyCount: 7,
          retweetCount: 4,
          likeCount: 52,
          mediaUrl: [
            'https://picsum.photos/200',
            'https://picsum.photos/200',
            'https://picsum.photos/200',
          ],
          mediaType: 'photo',
          videoUrl: [],
          replies: [],
        },
        {
          // Tweet reply with no profile image (using default Twitter icon)
          name: 'Anonymous User',
          username: 'anon123',
          profileImageUrl: '',
          text: 'This is really interesting! Thanks for sharing.',
          createdAt: '2025-02-11T16:22:49Z',
          replyCount: 0,
          retweetCount: 1,
          likeCount: 8,
          mediaUrl: [],
          mediaType: null,
          videoUrl: [],
          replies: [],
          referencedTweetsUsername: 'johndoe',
        },
        {
          // Complex thread with multiple replies
          name: 'Tech Guru',
          username: 'techguru',
          profileImageUrl: 'https://randomuser.me/api/portraits/men/12.jpg',
          text: 'Thread: 5 predictions for web development in 2025 👇<br><br>1/5 WebAssembly will become mainstream, enabling high-performance web applications previously only possible in native code.',
          createdAt: '2025-02-10T14:15:20Z',
          replyCount: 28,
          retweetCount: 152,
          likeCount: 473,
          mediaUrl: [],
          mediaType: null,
          videoUrl: [],
          replies: [
            {
              name: 'Tech Guru',
              username: 'techguru',
              profileImageUrl: 'https://randomuser.me/api/portraits/men/12.jpg',
              text: '2/5 AI-assisted development will transform how we write code, with tools understanding natural language requests and generating functional components.',
              createdAt: '2025-02-10T14:16:30Z',
              replyCount: 12,
              retweetCount: 89,
              likeCount: 318,
              mediaUrl: [],
              mediaType: null,
              videoUrl: [],
              replies: [],
              referencedTweetsUsername: 'techguru',
            },
            {
              name: 'Tech Guru',
              username: 'techguru',
              profileImageUrl: 'https://randomuser.me/api/portraits/men/12.jpg',
              text: '3/5 Micro-frontends will continue to gain traction as organizations scale their development teams and need to work independently.',
              createdAt: '2025-02-10T14:17:45Z',
              replyCount: 9,
              retweetCount: 76,
              likeCount: 294,
              mediaUrl: [],
              mediaType: null,
              videoUrl: [],
              replies: [],
              referencedTweetsUsername: 'techguru',
            },
            {
              name: 'Tech Guru',
              username: 'techguru',
              profileImageUrl: 'https://randomuser.me/api/portraits/men/12.jpg',
              text: '4/5 Server components and partial hydration will become the standard approach for building performant web applications.',
              createdAt: '2025-02-10T14:19:12Z',
              replyCount: 15,
              retweetCount: 92,
              likeCount: 312,
              mediaUrl: [],
              mediaType: null,
              videoUrl: [],
              replies: [
                {
                  name: 'Web Dev Expert',
                  username: 'webdevexp',
                  profileImageUrl:
                    'https://randomuser.me/api/portraits/women/13.jpg',
                  text: "Agreed! We're already seeing this with Next.js, Astro, and other modern frameworks. The performance benefits are significant.",
                  createdAt: '2025-02-10T14:25:32Z',
                  replyCount: 3,
                  retweetCount: 12,
                  likeCount: 47,
                  mediaUrl: [],
                  mediaType: null,
                  videoUrl: [],
                  replies: [],
                  referencedTweetsUsername: 'techguru',
                },
              ],
              referencedTweetsUsername: 'techguru',
            },
            {
              name: 'Tech Guru',
              username: 'techguru',
              profileImageUrl: 'https://randomuser.me/api/portraits/men/12.jpg',
              text: '5/5 WebGPU will enable a new generation of graphics-intensive web applications that rival native desktop performance.',
              createdAt: '2025-02-10T14:21:30Z',
              replyCount: 22,
              retweetCount: 118,
              likeCount: 356,
              mediaUrl: [],
              mediaType: null,
              videoUrl: [],
              replies: [],
              referencedTweetsUsername: 'techguru',
            },
          ],
        },
      ],
      linkedinPosts: [
        {
          id: 'post1',
          name: 'John Smith',
          profilePic: '/social/profile-picture.png',
          createdAt: '2025-02-20T14:30:00Z',
          updatedAt: '2025-02-20T14:30:00Z',
          text: "Excited to announce that I've joined Acme Corp as a Senior Developer! Looking forward to this new challenge.",
          visibility: 'PUBLIC',
          category: 'TEXT',
          mediaUrls: [],
          likesCount: 42,
          aggregatedCommentsCount: 7,
          provider: 'LinkedIn',
        },
        {
          id: 'post3',
          name: 'David Wilson',
          profilePic: '/social/profile-picture.png',
          createdAt: '2025-02-26T16:45:00Z',
          updatedAt: '2025-02-26T16:45:00Z',
          text: 'Beautiful day at our company retreat! Building connections and sharing ideas.',
          visibility: 'PUBLIC',
          category: 'IMAGE',
          mediaUrls: ['https://picsum.photos/200'],
          likesCount: 76,
          aggregatedCommentsCount: 12,
          provider: 'LinkedIn',
        },
        {
          id: 'post4',
          name: 'Sarah Lee',
          profilePic: '/social/profile-picture.png',
          createdAt: '2025-02-24T12:30:00Z',
          updatedAt: '2025-02-24T12:30:00Z',
          text: 'Our booth at the tech conference was a huge success! Thanks to everyone who stopped by to chat.',
          visibility: 'PUBLIC',
          category: 'IMAGE',
          mediaUrls: ['https://picsum.photos/200', 'https://picsum.photos/200'],
          likesCount: 95,
          aggregatedCommentsCount: 8,
          provider: 'LinkedIn',
        },
        {
          id: 'post7',
          name: 'Robert Chen',
          profilePic: '/social/profile-picture.png',
          createdAt: '2025-02-21T11:10:00Z',
          updatedAt: '2025-02-21T11:10:00Z',
          text: "Sharing this only with my connections: I'm looking for recommendations for a good UX designer for a contract position.",
          visibility: 'CONNECTIONS',
          category: 'TEXT',
          mediaUrls: [],
          likesCount: 12,
          aggregatedCommentsCount: 8,
          provider: 'LinkedIn',
        },
        {
          id: 'post8',
          name: 'Emma Williams',
          profilePic: '/social/profile-picture.png',
          createdAt: '2025-02-10T08:00:00Z',
          updatedAt: '2025-02-10T08:00:00Z',
          text: "I'm curious about your work preferences post-pandemic:",
          visibility: 'PUBLIC',
          category: 'poll',
          mediaUrls: [],
          likesCount: 187,
          aggregatedCommentsCount: 58,
          provider: 'LinkedIn',
          poll: {
            question: "What's your preferred work arrangement?",
            options: [
              { text: 'Fully remote', voteCount: 342 },
              { text: 'Hybrid (2-3 days in office)', voteCount: 578 },
              { text: 'Full-time in office', voteCount: 124 },
              { text: 'Flexible, depending on needs', voteCount: 256 },
            ],
            voting_status: 'closed',
            settings: {
              duration: 'FOURTEEN_DAYS',
            },
          },
        },
        {
          id: 'post14',
          name: 'Sophie Martinez',
          profilePic: '/social/profile-picture.png',
          createdAt: '2025-02-24T13:45:00Z',
          updatedAt: '2025-02-24T13:45:00Z',
          text: "✨ Big announcement! ✨\n\nI'm thrilled to share that our startup has secured $2M in seed funding! 🚀\n\nThis wouldn't have been possible without our amazing team and supportive investors who believed in our vision.\n\nExcited for this next chapter! 💫\n\n#startup #funding #entrepreneurship",
          visibility: 'PUBLIC',
          category: 'TEXT',
          mediaUrls: [],
          likesCount: 418,
          aggregatedCommentsCount: 92,
          provider: 'LinkedIn',
        },
        {
          id: 390,
          provider: 'LinkedIn',
          socialId: 'urn:li:ugcPost:7027926038399619072',
          name: '',
          userName: '',
          profileImageUrl: '',
          createdAt: '2023-02-05T09:09:00Z',
          updatedAt: '2023-02-05T09:09:16Z',
          lifecycleState: 'PUBLISHED',
          visibility: 'PUBLIC',
          category: 'VIDEO',
          text: 'Testing Video',
          title: 'Testing Video',
          description: '',
          poll: null,
          likedByCurrentUser: false,
          likesCount: 0,
          firstLevelCommentsCount: 0,
          aggregatedCommentsCount: 0,
          sourceUrl: null,
          thumbnailUrl:
            'https://media.licdn.com/dms/image/v2/C5605AQEuh21pEU5ZTw/videocover-high/videocover-high/0/1675588145152?e=**********&v=beta&t=Hsf-CJ_HHpmA7HnBhRGnO0jlPY-U0EXO93Rx4avkkQY',
          mediaUrls: [
            'https://dms.licdn.com/playlist/vid/v2/C5605AQEuh21pEU5ZTw/mp4-720p-30fp-crf28/mp4-720p-30fp-crf28/0/1675588153048?e=**********&v=beta&t=wfFMUmGnAa4wws-uhbhJabb_-xwKQecBjtmVRe0-jGw',
          ],
        },
      ],
      tiktokPosts: [
        {
          id: 118,
          username: 'Sharparchive',
          profileImageUrl: '/social/profile-picture.png',
          provider: 'TikTok',
          socialId: '7473456749928860946',
          createdAt: '2025-02-20T11:09:08Z',
          title: 'Dessert 2',
          text: 'Dessert 2',
          duration: 13,
          height: 1080,
          width: 1920,
          likeCount: 1,
          commentCount: 0,
          shareCount: 0,
          viewCount: 0,
          embedHtml:
            '<blockquote class="tiktok-embed" cite="https://www.tiktok.com/@abul_karim/video/7473456631938813192?utm_campaign=tt4d_open_api&utm_source=awgw0j80gj5c772o" data-video-id="7473456631938813192" style="max-width: 605px;min-width: 325px;" > <section> <a target="_blank" title="@abul_karim" href="https://www.tiktok.com/@abul_karim">@abul_karim</a> <p>Dessert 1</p> <a target="_blank" title="♬  - " href="https://www.tiktok.com/">♬  - </a> </section> </blockquote> <script async src="https://www.tiktok.com/embed.js"></script>',
          embedLink:
            'https://www.tiktok.com/player/v1/7473456631938813192?music_info=1&description=1&autoplay=1&loop=1&utm_campaign=tt4d_open_api&utm_source=awgw0j80gj5c772o',
          coverImageUrl:
            'https://p16-sign-sg.tiktokcdn.com/tos-alisg-p-0037/o0hyIeCPDARQ2VrwSILleGBAuTOGbLRphofGiE~tplv-tiktokx-cropcenter-q:300:400:q72.jpeg?dr=14782&nonce=27800&refresh_token=5462345e173df78879ad28c377dd78a9&x-expires=1741233600&x-signature=xcKehhWic2%2FvKchjbHD%2Bkgn7YMs%3D&biz_tag=tt_video&idc=maliva&ps=933b5bde&s=TIKTOK_FOR_DEVELOPER&sc=cover&shcp=8aecc5ac&shp=d05b14bd&t=bacd0480',
          videoUrl:
            'https://www.tiktok.com/@abul_karim/video/7473456631938813192?utm_campaign=tt4d_open_api&utm_source=awgw0j80gj5c772o',
          archivedVideoUrl:
            'https://sharparchive-app.s3.amazonaws.com/dev/archive_data/3/TikTok/287/7473456631938813192.mp4?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=AKIAZ2LEYZ3552VOPMY4%2F20250305%2Fus-east-2%2Fs3%2Faws4_request&X-Amz-Date=20250305T044525Z&X-Amz-Expires=3600&X-Amz-SignedHeaders=host&X-Amz-Signature=8d43940be49a3598d056d095a2caff04040208fe9c6c71c76a24e94cd9e0fad5',
        },
        {
          id: 117,
          username: 'Sharparchive',
          profileImageUrl: '/social/profile-picture.png',
          provider: 'TikTok',
          socialId: '7473456631938813192',
          createdAt: '2025-02-20T11:08:43Z',
          title: 'Dessert 1',
          text: 'Dessert 1',
          duration: 18,
          height: 2160,
          width: 3840,
          likeCount: 0,
          commentCount: 0,
          shareCount: 0,
          viewCount: 0,
          embedHtml:
            '<blockquote class="tiktok-embed" cite="https://www.tiktok.com/@abul_karim/video/7473456631938813192?utm_campaign=tt4d_open_api&utm_source=awgw0j80gj5c772o" data-video-id="7473456631938813192" style="max-width: 605px;min-width: 325px;" > <section> <a target="_blank" title="@abul_karim" href="https://www.tiktok.com/@abul_karim">@abul_karim</a> <p>Dessert 1</p> <a target="_blank" title="♬  - " href="https://www.tiktok.com/">♬  - </a> </section> </blockquote> <script async src="https://www.tiktok.com/embed.js"></script>',
          embedLink:
            'https://www.tiktok.com/player/v1/7473456631938813192?music_info=1&description=1&autoplay=1&loop=1&utm_campaign=tt4d_open_api&utm_source=awgw0j80gj5c772o',
          coverImageUrl:
            'https://p16-sign-sg.tiktokcdn.com/tos-alisg-p-0037/o0hyIeCPDARQ2VrwSILleGBAuTOGbLRphofGiE~tplv-tiktokx-cropcenter-q:300:400:q72.jpeg?dr=14782&nonce=27800&refresh_token=5462345e173df78879ad28c377dd78a9&x-expires=1741233600&x-signature=xcKehhWic2%2FvKchjbHD%2Bkgn7YMs%3D&biz_tag=tt_video&idc=maliva&ps=933b5bde&s=TIKTOK_FOR_DEVELOPER&sc=cover&shcp=8aecc5ac&shp=d05b14bd&t=bacd0480',
          videoUrl:
            'https://www.tiktok.com/@abul_karim/video/7473456631938813192?utm_campaign=tt4d_open_api&utm_source=awgw0j80gj5c772o',
          archivedVideoUrl:
            'https://sharparchive-app.s3.amazonaws.com/dev/archive_data/3/TikTok/287/7473456631938813192.mp4?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=AKIAZ2LEYZ3552VOPMY4%2F20250305%2Fus-east-2%2Fs3%2Faws4_request&X-Amz-Date=20250305T044525Z&X-Amz-Expires=3600&X-Amz-SignedHeaders=host&X-Amz-Signature=8d43940be49a3598d056d095a2caff04040208fe9c6c71c76a24e94cd9e0fad5',
        },
      ],
    }
  },

  getters: {},

  mutations: {},

  actions: {},
}

export default browseStore
