import { useFetch } from '@vueuse/core'
import { useRuntimeConfig } from 'nuxt/app'
import {
  LATEST_ARCHIVE,
  SOCIAL_ARTICLE,
  SOCIAL_ARTICLE_LOAD_MORE,
  ALL_MESSAGE_PERSONS,
  SING<PERSON>_CONVERTIONS,
  GET_MORE_MESSAGES,
  YOUTUBE_PLAYLIST,
  YOUTUBE_PLAYLIST_ITEM,
  YOUTUBE_PLAYLIST_ITEM_MORE,
  SOCIAL_EARLIEST_DATE,
  WEBSITE_HTML,
  WEB_SEARCH,
  GET_ESTIMATED_COST,
} from '~/constants/urls'

const { fetch } = useFetched()

const homeStore = {
  namespaced: true,
  state() {
    return {
      latest: {
        th: [{ name: 'Username' }, { name: 'Source' }, { name: 'Download' }],
        tbody: [],
      },
      websiteLog: {
        th: [{ name: 'Date' }, { name: 'Page Name' }, { name: 'Type' }],
        tbody: [
          {
            id: 1,
            date: '4/13/2022',
            pageName: 'Blog',
            type: 'Add',
            expand: {
              link: 'https://greenstaradvisor.com/blog/04132022',
            },
            selected: false,
          },
          {
            id: 2,
            date: '4/13/2022',
            pageName: 'Contact',
            type: 'Change',
            selected: false,
          },
          {
            id: 3,
            date: '3/26/2021',
            pageName: 'Blog',
            type: 'Add',
            expand: {
              link: 'https://greenstaradvisor.com/blog/04132022',
              flag: 'Promissory Statement',
              title: '"dolor sit amet" in lexicon',
              description:
                'Lorem ipsum dolor sit amet, consectetuer adipiscing elit, sed diam nonummy nibh euismod tincidunt ut laoreet dolore magna aliquam erat volutpat.',
            },
            selected: false,
          },
          {
            id: 4,
            date: '3/26/2021',
            pageName: 'Home',
            type: 'Change',
            selected: false,
          },
        ],
      },
      searchMessagePeople: [],
      searchMessageText: [],
      articles: {
        id: 51,
        provider: 'TikTok',
        type: 'Account',
        name: 'sharparchive',
        username: 'sharparchive',
        profilePic:
          'https://dev-api.sharparchive.com/api/social/media/*****************.jpg?provider=Facebook',
        totalCount: 20,
        items: [
          {
            id: 163,
            provider: 'TikTok',
            name: 'sharparchive',
            profileImageUrl:
              'https://dev-api.sharparchive.com/api/social/media/*****************.jpg?provider=Facebook',
            createdAt: '2023-03-20T06:45:00Z',
            updatedAt: '2023-03-20T06:45:00Z',
            text: 'wasdsadas',
            description: '',
            privacy: 'Public',
            reactionsCount: 0,
            commentsCount: 0,
            sourceUrl:
              'https://dev-api.sharparchive.com/api/social/media/*****************_10150008890614409_1678240424.mp4?provider=Facebook&u=3&a=51&c=source_url',
            archivedSourceUrl:
              'https://dev-api.sharparchive.com/api/social/media/*****************_10150008890614409_1678240424.mp4?provider=Facebook&u=3&a=51&c=source_url',
          },
          {
            id: 162,
            provider: 'TikTok',
            name: 'sharparchive',
            profileImageUrl:
              'https://dev-api.sharparchive.com/api/social/media/*****************.jpg?provider=Facebook',
            createdAt: '2023-03-20T00:22:20Z',
            updatedAt: '2023-03-20T06:44:38Z',
            text: '20th post',
            description: '',
            privacy: 'Public',
            reactionsCount: 0,
            commentsCount: 2,
            sourceUrl:
              'https://dev-api.sharparchive.com/api/social/media/*****************_10150008890614409_1678240424.mp4?provider=Facebook&u=3&a=51&c=source_url',
            archivedSourceUrl:
              'https://dev-api.sharparchive.com/api/social/media/*****************_10150008890614409_1678240424.mp4?provider=Facebook&u=3&a=51&c=source_url',
          },
          {
            id: 161,
            provider: 'TikTok',
            name: 'sharparchive',
            profileImageUrl:
              'https://dev-api.sharparchive.com/api/social/media/*****************.jpg?provider=Facebook',
            createdAt: '2023-03-19T04:01:33Z',
            updatedAt: '2023-03-20T00:07:48Z',
            text: 'awdad',
            description: '',
            privacy: 'Public',
            reactionsCount: 0,
            commentsCount: 1,
            sourceUrl:
              'https://dev-api.sharparchive.com/api/social/media/*****************_10150008890614409_1678240424.mp4?provider=Facebook&u=3&a=51&c=source_url',
            archivedSourceUrl:
              'https://dev-api.sharparchive.com/api/social/media/*****************_10150008890614409_1678240424.mp4?provider=Facebook&u=3&a=51&c=source_url',
          },
          {
            id: 160,
            provider: 'TikTok',
            name: 'sharparchive',
            profileImageUrl:
              'https://dev-api.sharparchive.com/api/social/media/*****************.jpg?provider=Facebook',
            createdAt: '2023-03-17T23:18:05Z',
            updatedAt: '2023-03-17T23:18:05Z',
            description: '',
            privacy: 'Public',
            reactionsCount: 0,
            commentsCount: 0,
            sourceUrl:
              'https://dev-api.sharparchive.com/api/social/media/*****************_10150008890614409_1678240424.mp4?provider=Facebook&u=3&a=51&c=source_url',
            archivedSourceUrl:
              'https://dev-api.sharparchive.com/api/social/media/*****************_10150008890614409_1678240424.mp4?provider=Facebook&u=3&a=51&c=source_url',
          },
          {
            id: 159,
            provider: 'TikTok',
            name: 'sharparchive',
            profileImageUrl:
              'https://dev-api.sharparchive.com/api/social/media/*****************.jpg?provider=Facebook',
            createdAt: '2023-03-17T20:54:35Z',
            updatedAt: '2023-03-17T21:18:57Z',
            text: 'Fb test poster 1',
            description: '',
            privacy: 'Public',
            reactionsCount: 0,
            commentsCount: 2,
            sourceUrl:
              'https://dev-api.sharparchive.com/api/social/media/*****************_10150008890614409_1678240424.mp4?provider=Facebook&u=3&a=51&c=source_url',
            archivedSourceUrl:
              'https://dev-api.sharparchive.com/api/social/media/*****************_10150008890614409_1678240424.mp4?provider=Facebook&u=3&a=51&c=source_url',
          },
          {
            id: 158,
            provider: 'TikTok',
            name: 'sharparchive',
            profileImageUrl:
              'https://dev-api.sharparchive.com/api/social/media/*****************.jpg?provider=Facebook',
            createdAt: '2023-03-17T20:00:01Z',
            updatedAt: '2023-03-17T20:00:01Z',
            text: 'fb tester and ig tester',
            description: '',
            privacy: 'Public',
            reactionsCount: 0,
            commentsCount: 0,
            sourceUrl:
              'https://dev-api.sharparchive.com/api/social/media/*****************_10150008890614409_1678240424.mp4?provider=Facebook&u=3&a=51&c=source_url',
            archivedSourceUrl:
              'https://dev-api.sharparchive.com/api/social/media/*****************_10150008890614409_1678240424.mp4?provider=Facebook&u=3&a=51&c=source_url',
          },
          {
            id: 157,
            provider: 'Facebook',
            socialId: '*****************_10150009002051716',
            socialUid: '*****************',
            name: 'Polar Bears Against Violent Bananas',
            profileImageUrl:
              'https://dev-api.sharparchive.com/api/social/media/*****************.jpg?provider=Facebook',
            createdAt: '2023-03-16T17:28:38Z',
            updatedAt: '2023-03-17T07:48:43Z',
            parentId: '',
            type: '',
            statusType: 'mobile_status_update',
            placeName: '',
            placeCity: '',
            placeCountry: '',
            text: 'This is a fb tester post 1',
            link: '',
            linkName: '',
            caption: '',
            description: '',
            privacy: 'Public',
            sharesCount: 0,
            reactionsCount: 0,
            commentsCount: 2,
            isPublished: true,
            isExpired: false,
            isHidden: false,
            fullPicture: '',
            archivedFullPicture: '',
            sourceUrl:
              'https://dev-api.sharparchive.com/api/social/media/*****************_10150008890614409_1678240424.mp4?provider=Facebook&u=3&a=51&c=source_url',
            archivedSourceUrl:
              'https://dev-api.sharparchive.com/api/social/media/*****************_10150008890614409_1678240424.mp4?provider=Facebook&u=3&a=51&c=source_url',
            attachmentUrl: '',
            attachmentImages: '',
            archivedAttachmentImages: '',
            facebookUrl:
              'https://www.facebook.com/*****************/posts/10150009002051716/',
          },
          {
            id: 156,
            provider: 'Facebook',
            socialId: '*****************_10150008999372524',
            socialUid: '*****************',
            name: 'Polar Bears Against Violent Bananas',
            profileImageUrl:
              'https://dev-api.sharparchive.com/api/social/media/*****************.jpg?provider=Facebook',
            createdAt: '2023-03-16T13:24:52Z',
            updatedAt: '2023-03-16T13:24:52Z',
            parentId: '',
            type: '',
            statusType: 'added_photos',
            placeName: '',
            placeCity: '',
            placeCountry: '',
            text: 'عربى/کوردی\nاربيل: بی ام دبلیو سیریس 4 430i, 2021\nعدد الأميال 9,000 ميل\nلمزيد من المعلومات الرجاء الضغط على هذا الرابط \n👉 https://www.iqcars.net/redirect/117746?lang=ar\n*****************\nهەولێر: بی ئێم دەبڵیو 4 سیریەس 430i, 2021\nماوەی ڕۆیشتن 9,000 ميل\nبۆ زانیاری زیاتر تکایە کلیک لەم بەستەرە بکە  :\n👉 https://www.iqcars.net/redirect/117746?lang=ku\n#iQCars',
            link: '',
            linkName: '',
            caption: '',
            description: '',
            privacy: 'Public',
            sharesCount: 0,
            reactionsCount: 0,
            commentsCount: 0,
            isPublished: true,
            isExpired: false,
            isHidden: false,
            fullPicture:
              'https://scontent-ord5-2.xx.fbcdn.net/v/t39.30808-6/336672640_122043041977635336_4285505181103886553_n.jpg?stp=dst-jpg_p720x720&_nc_cat=108&ccb=1-7&_nc_sid=9267fe&_nc_ohc=ZKm2A_sTKfQAX8LQzEU&_nc_ht=scontent-ord5-2.xx&edm=AKIiGfEEAAAA&oh=00_AfDO5afJEj78pHWFFcrzcykEqbnyXVvVnHyS0VzFNpUQ6g&oe=641D24EA',
            archivedFullPicture: '',
            sourceUrl:
              'https://dev-api.sharparchive.com/api/social/media/*****************_10150008890614409_1678240424.mp4?provider=Facebook&u=3&a=51&c=source_url',
            archivedSourceUrl:
              'https://dev-api.sharparchive.com/api/social/media/*****************_10150008890614409_1678240424.mp4?provider=Facebook&u=3&a=51&c=source_url',
            attachmentUrl:
              'https://www.facebook.com/*****************/photos/a.10150005555250634/10150008999372524/?type=3',
            attachmentImages: '',
            archivedAttachmentImages: '',
            facebookUrl:
              'https://www.facebook.com/*****************/photos/a.10150005555250634/10150008999372524/?type=3',
          },
          {
            id: 155,
            provider: 'Facebook',
            socialId: '*****************_10150008990806693',
            socialUid: '*****************',
            name: 'Polar Bears Against Violent Bananas',
            profileImageUrl:
              'https://dev-api.sharparchive.com/api/social/media/*****************.jpg?provider=Facebook',
            createdAt: '2023-03-16T03:26:23Z',
            updatedAt: '2023-03-16T03:26:23Z',
            parentId: '',
            type: '',
            statusType: 'added_photos',
            placeName: '',
            placeCity: '',
            placeCountry: '',
            text: '16th halele',
            link: '',
            linkName: '',
            caption: '',
            description: '',
            privacy: 'Public',
            sharesCount: 0,
            reactionsCount: 0,
            commentsCount: 0,
            isPublished: true,
            isExpired: false,
            isHidden: false,
            fullPicture:
              'https://scontent-ord5-2.xx.fbcdn.net/v/t39.30808-6/335689664_90000703378011_4169358779246461382_n.jpg?_nc_cat=109&ccb=1-7&_nc_sid=9267fe&_nc_ohc=gqd45AlChGAAX8Z2S1F&_nc_ht=scontent-ord5-2.xx&edm=AKIiGfEEAAAA&oh=00_AfA5V-fxpAVVTN9saCfWDLnWiokj_p6YqJUMHyT6D7z4Og&oe=641C5878',
            archivedFullPicture:
              'https://dev-api.sharparchive.com/api/social/media/*****************_10150008990806693_1678937183.jpg?provider=Facebook&u=3&a=51&c=full_picture',
            sourceUrl:
              'https://dev-api.sharparchive.com/api/social/media/*****************_10150008890614409_1678240424.mp4?provider=Facebook&u=3&a=51&c=source_url',
            archivedSourceUrl:
              'https://dev-api.sharparchive.com/api/social/media/*****************_10150008890614409_1678240424.mp4?provider=Facebook&u=3&a=51&c=source_url',
            attachmentUrl:
              'https://www.facebook.com/*****************/photos/a.10150005555250634/10150008990806693/?type=3',
            attachmentImages: '',
            archivedAttachmentImages: '',
            facebookUrl:
              'https://www.facebook.com/*****************/photos/a.10150005555250634/10150008990806693/?type=3',
          },
        ],
      },
      newArticles: [],
      loadArticles: true,
      loadMoreArticlesSkelaton: false,
      loadMoreArticles: true,
      loadMorePlaylistVideo: true,
      showMobileMenu: false,
      currentHeader: 'RealTimeFeed',
      currentMessage: 'HomeMessageFeedAllMessagePerson',
      searchFromTextMessage: false,
      currentSocialComponent: {},
      bodyHeight: 0,
      bodyMobileHeight: 0,
      detailsExpand: false,
      showComp: false,
      words: {},
      twitterComment: [],
      previousTwitterComment: [],
      allMessagePersons: null,
      singleCovertions: null,
      pageId: '',
      participant: '',
      conversationOwner: null,
      conversationUser: null,
      newLoadMessages: [1],
      showMessageskeleton: false,
      showPersonSkeleton: true,
      showMoreMessageSkeleton: false,
      messageCount: 0,
      person: null,
      showOptionsOverAll: false,
      searchText: '',
      // Email states
      allExpanded: false,
      currentTab: 'All',
      facebookCurrentTab: 'all',
      currentYoutubeContent: 'Summary',
      emailDynamicComp: 'EmailContent',
      singleMessage: [],
      tempSingleMessage: [],
      index: 0,
      currentIndex: 0,
      expandFullImage: false,
      messageId: 0,
      attachment: null,
      selectedMessage: null,
      selectedFeed: null,
      homeCurrentComp: '',
      isShowMessage: false,
      selectAccessType: 'SelectedAccessType',
      feedsDropdown: false,
      youtubeComment: [],
      youtubeTranscript: [],
      Transcripts: [],
      youtubePlayLists: null,
      youtubePlayListItems: null,
      selectedPlayList: false,
      tempArticles: [],
      getOldJoiningDateTime: '',
      showSearchMessageDetails: false,
      showSearchMessageFromSearch: false,
      showSearchPersonSingle: false,
      showSearchMessageSingle: false,
      selectedMessageID: null,
      selectedMessageIDIndividual: null,
      stopSetScrollPosition: false,
      stopScrollEvent: false,
      websiteArticle: '',
      webSearch: {
        accountId: '',
        archiveDate: '',
        search: '',
      },
      webSeletedDate: '',
      webSearchResult: null,
      webSearching: false,
      webSearchClear: false,
      initialWebURL: '',
      selectedWebURL: '',
      initialWebArchiveDate: '',
      selectedWebArchiveDate: '',
      pinterestCurrentTab: 'HomeRealtimeFeedRssPinterestCreated',
      estimationCost: '',
      date: {
        startDate: '',
        endDate: '',
      },
      youtubeType: 'All',
      tempEmailBody: '',
      tempEmailTitle: '',
      showPdfPupUp: false,
    }
  },

  getters: {
    getSearchMessagePeople(state) {
      return state.searchMessagePeople
    },
    getSearchMessageText(state) {
      return state.searchMessageText
    },
    getLatestData(state) {
      return state.latest.tbody
    },
    getSocialArticle(state) {
      return state.articles
    },
    getNewArticles(state) {
      return state.newArticles
    },
    getCurrentArticles(state) {
      return state.articles[state.currentSocialComponent.id]
    },
    showMenu(state) {
      if (state.showMobileMenu) {
        document.querySelectorAll('video').forEach((vid) => vid.pause())
      }
      return state.showMobileMenu
    },
    currentHead(state) {
      return state.currentHeader
    },
    currentMess(state) {
      return state.currentMessage
    },
    searchFromTextMess(state) {
      return state.searchFromTextMessage
    },
    currentComp(state) {
      return state.currentSocialComponent.provider
    },
    conversations(state) {
      const userId = state.conversationUser && state.conversationUser.uid
      if (state.singleCovertions) {
        return state.singleCovertions.messages.reduce(
          (p, c) => {
            if (typeof p.fromUid === 'undefined' || p.fromUid !== c.fromUid) {
              p.fromUid = c.fromUid
              // p.groups.push([])
              p.groups.push({
                user: c.fromUid === userId,
                date: c.createdAt,
                messages: [],
              })
            }
            // p.groups[p.groups.length - 1].push(c);
            p.groups[p.groups.length - 1].messages.push(c)
            return p
          },
          { groups: [] },
        ).groups
      } else {
        return []
      }
    },
    allEmails(state) {
      if (state.articles.provider === 'Google' && state.articles) {
        const tempObj = JSON.parse(JSON.stringify(state.articles))
        if (state.currentTab === 'Incoming') {
          tempObj.items.forEach((item) => {
            if (state.selectedMessage && item.id === state.selectedMessage.id) {
              item.individualSelect = true
            } else {
              item.individualSelect = false
            }
          })

          const filteredArr = tempObj.items.reduce((acc, current) => {
            const x = acc.find(
              (item) =>
                item.threadId === current.threadId ||
                (item.historyId === current.historyId &&
                  item.labels.includes('SENT')) ||
                item.threadId === current.threadId ||
                item.historyId === current.historyId,
            )
            if (!x) {
              return acc.concat([current])
            } else {
              acc.forEach((item, index) => {
                if (
                  (item.threadId === current.threadId ||
                    item.historyId === current.historyId) &&
                  item.labels.includes('SENT')
                ) {
                  acc[index] = current
                }
              })
              return acc
            }
          }, [])

          if (filteredArr) {
            filteredArr.forEach((itemFull) => {
              let tempFrom = ''
              itemFull.TotalMessage = 0
              tempObj.items.forEach((item) => {
                if (
                  item.threadId === itemFull.threadId ||
                  (item.historyId === itemFull.historyId &&
                    item.labels.includes('SENT')) ||
                  item.threadId === itemFull.threadId ||
                  item.historyId === itemFull.historyId
                ) {
                  if (itemFull.TotalMessage > 0) {
                    if (
                      !tempFrom.includes(',') &&
                      !tempFrom.includes(item.from)
                    ) {
                      tempFrom =
                        tempFrom +
                        (tempFrom ? ', ' : '') +
                        (state.articles.name.includes(item.from.split(' ')[0])
                          ? 'me'
                          : item.from.split(' ')[0])
                    } else if (
                      !tempFrom.includes(
                        state.articles.name.includes(item.from.split(' ')[0])
                          ? 'me'
                          : item.from.split(' ')[0],
                      )
                    ) {
                      tempFrom =
                        tempFrom +
                        ', ' +
                        (state.articles.name.includes(item.from.split(' ')[0])
                          ? 'me'
                          : item.from.split(' ')[0])
                    }
                  } else {
                    if (state.articles.name.includes(item.from.split(' ')[0])) {
                      tempFrom = 'me'
                    } else {
                      tempFrom = item.from
                    }
                  }
                  itemFull.TotalMessage = itemFull.TotalMessage + 1
                }
              })
              itemFull.from = [...new Set(tempFrom.split(', '))]
                .reverse()
                .join(', ')
            })
          }

          tempObj.items = filteredArr

          return tempObj
        } else if (state.currentTab === 'Outgoing') {
          tempObj.items.forEach((item) => {
            if (state.selectedMessage && item.id === state.selectedMessage.id) {
              item.individualSelect = true
            } else {
              item.individualSelect = false
            }
          })

          const filteredArr = tempObj.items.reduce((acc, current) => {
            const x = acc.find(
              (item) =>
                item.threadId === current.threadId ||
                item.historyId === current.historyId,
            )
            if (!x) {
              return acc.concat([current])
            } else {
              return acc
            }
          }, [])

          if (filteredArr) {
            filteredArr.forEach((itemFull) => {
              let tempFrom = ''
              itemFull.TotalMessage = 0
              tempObj.items.forEach((item) => {
                if (
                  item.threadId === itemFull.threadId ||
                  (item.historyId === itemFull.historyId &&
                    item.labels.includes('SENT')) ||
                  item.threadId === itemFull.threadId ||
                  item.historyId === itemFull.historyId
                ) {
                  if (itemFull.TotalMessage > 0) {
                    if (
                      !tempFrom.includes(',') &&
                      !tempFrom.includes(item.from)
                    ) {
                      tempFrom =
                        tempFrom +
                        (tempFrom ? ', ' : '') +
                        (state.articles.name.includes(item.from.split(' ')[0])
                          ? 'me'
                          : item.from.split(' ')[0])
                    } else if (
                      !tempFrom.includes(
                        state.articles.name.includes(item.from.split(' ')[0])
                          ? 'me'
                          : item.from.split(' ')[0],
                      )
                    ) {
                      tempFrom =
                        tempFrom +
                        ', ' +
                        (state.articles.name.includes(item.from.split(' ')[0])
                          ? 'me'
                          : item.from.split(' ')[0])
                    }
                  } else {
                    if (state.articles.name.includes(item.from.split(' ')[0])) {
                      tempFrom = 'me'
                    } else {
                      tempFrom = item.from
                    }
                  }
                  itemFull.TotalMessage = itemFull.TotalMessage + 1
                }
              })
              itemFull.from = [...new Set(tempFrom.split(', '))]
                .reverse()
                .join(', ')
            })
          }

          tempObj.items = filteredArr

          return tempObj
        } else {
          const tempObj = JSON.parse(JSON.stringify(state.articles))
          tempObj.items.forEach((item) => {
            if (state.selectedMessage && item.id === state.selectedMessage.id) {
              item.individualSelect = true
            } else {
              item.individualSelect = false
            }
          })
          const filteredArr = tempObj.items.reduce((acc, current) => {
            const x = acc.find(
              (item) =>
                item.threadId === current.threadId ||
                (item.historyId === current.historyId &&
                  item.labels.includes('SENT')) ||
                item.threadId === current.threadId ||
                item.historyId === current.historyId,
            )
            if (!x) {
              return acc.concat([current])
            } else {
              acc.forEach((item, index) => {
                if (
                  (item.threadId === current.threadId ||
                    item.historyId === current.historyId) &&
                  item.labels.includes('SENT')
                ) {
                  acc[index] = current
                }
              })
              return acc
            }
          }, [])

          if (filteredArr) {
            filteredArr.forEach((itemFull) => {
              let tempFrom = ''
              itemFull.TotalMessage = 0
              tempObj.items.forEach((item) => {
                if (
                  item.threadId === itemFull.threadId ||
                  (item.historyId === itemFull.historyId &&
                    item.labels.includes('SENT')) ||
                  item.threadId === itemFull.threadId ||
                  item.historyId === itemFull.historyId
                ) {
                  if (itemFull.TotalMessage > 0) {
                    if (
                      !tempFrom.includes(',') &&
                      !tempFrom.includes(item.from)
                    ) {
                      tempFrom =
                        tempFrom +
                        (tempFrom ? ', ' : '') +
                        (state.articles.name.includes(item.from.split(' ')[0])
                          ? 'me'
                          : item.from.split(' ')[0])
                    } else if (
                      !tempFrom.includes(
                        state.articles.name.includes(item.from.split(' ')[0])
                          ? 'me'
                          : item.from.split(' ')[0],
                      )
                    ) {
                      tempFrom =
                        tempFrom +
                        ', ' +
                        (state.articles.name.includes(item.from.split(' ')[0])
                          ? 'me'
                          : item.from.split(' ')[0])
                    }
                  } else {
                    if (state.articles.name.includes(item.from.split(' ')[0])) {
                      tempFrom = 'me'
                    } else {
                      tempFrom = item.from
                    }
                  }
                  itemFull.TotalMessage = itemFull.TotalMessage + 1
                }
              })
              itemFull.from = [...new Set(tempFrom.split(', '))]
                .reverse()
                .join(', ')
            })
          }

          const temp12 = JSON.parse(JSON.stringify(tempObj.items))
          tempObj.items = filteredArr
          return tempObj
        }
      } else if (state.articles.provider === 'Microsoft' && state.articles) {
        const tempObj = JSON.parse(JSON.stringify(state.articles))
        if (state.currentTab === 'Incoming') {
          tempObj.items.forEach((item) => {
            if (state.selectedMessage && item.id === state.selectedMessage.id) {
              item.individualSelect = true
            } else {
              item.individualSelect = false
            }
          })

          const filteredArr = tempObj.items.reduce((acc, current) => {
            const x = acc.find(
              (item) =>
                (item.conversationId === current.conversationId &&
                  item.isDeliveryReceiptRequested === null) ||
                item.conversationId === current.conversationId,
            )
            if (!x) {
              return acc.concat([current])
            } else {
              acc.forEach((item, index) => {
                if (
                  item.conversationId === current.conversationId &&
                  item.isDeliveryReceiptRequested === null
                ) {
                  acc[index] = current
                }
              })
              return acc
            }
          }, [])

          if (filteredArr) {
            filteredArr.forEach((itemFull) => {
              let tempFrom = ''
              itemFull.TotalMessage = 0
              tempObj.items.forEach((item) => {
                if (
                  (item.conversationId === itemFull.conversationId &&
                    item.isDeliveryReceiptRequested === false) ||
                  item.conversationId === itemFull.conversationId
                ) {
                  if (itemFull.TotalMessage > 0) {
                    if (
                      !tempFrom.includes(',') &&
                      !tempFrom.includes(item.fromEmail.name)
                    ) {
                      tempFrom =
                        tempFrom +
                        (tempFrom ? ', ' : '') +
                        (state.articles.name.includes(
                          item.fromEmail.name.split(' ')[0],
                        )
                          ? 'me'
                          : item.fromEmail.name.split(' ')[0])
                    } else if (
                      !tempFrom.includes(
                        state.articles.name.includes(
                          item.fromEmail.name.split(' ')[0],
                        )
                          ? 'me'
                          : item.fromEmail.name.split(' ')[0],
                      )
                    ) {
                      tempFrom =
                        tempFrom +
                        ', ' +
                        (state.articles.name.includes(
                          item.fromEmail.name.split(' ')[0],
                        )
                          ? 'me'
                          : item.fromEmail.name.split(' ')[0])
                    }
                  } else {
                    if (
                      state.articles.name.includes(
                        item.fromEmail.name.split(' ')[0],
                      )
                    ) {
                      tempFrom = 'me'
                    } else {
                      tempFrom = item.fromEmail.name
                    }
                  }
                  itemFull.TotalMessage = itemFull.TotalMessage + 1
                }
              })
              itemFull.fromEmail.name = [...new Set(tempFrom.split(', '))]
                .reverse()
                .join(', ')
            })
          }

          tempObj.items = filteredArr

          return tempObj
        } else if (state.currentTab === 'Outgoing') {
          tempObj.items.forEach((item) => {
            if (state.selectedMessage && item.id === state.selectedMessage.id) {
              item.individualSelect = true
            } else {
              item.individualSelect = false
            }
          })

          const filteredArr = tempObj.items.reduce((acc, current) => {
            const x = acc.find(
              (item) => item.conversationId === current.conversationId,
            )
            if (!x) {
              return acc.concat([current])
            } else {
              return acc
            }
          }, [])

          if (filteredArr) {
            filteredArr.forEach((itemFull) => {
              let tempFrom = ''
              itemFull.TotalMessage = 0
              tempObj.items.forEach((item) => {
                if (item.conversationId === itemFull.conversationId) {
                  if (itemFull.TotalMessage > 0) {
                    if (
                      !tempFrom.includes(',') &&
                      !tempFrom.includes(item.fromEmail.name)
                    ) {
                      tempFrom =
                        tempFrom +
                        (tempFrom ? ', ' : '') +
                        (state.articles.name.includes(
                          item.fromEmail.name.split(' ')[0],
                        )
                          ? 'me'
                          : item.fromEmail.name.split(' ')[0])
                    } else if (
                      !tempFrom.includes(
                        state.articles.name.includes(
                          item.fromEmail.name.split(' ')[0],
                        )
                          ? 'me'
                          : item.fromEmail.name.split(' ')[0],
                      )
                    ) {
                      tempFrom =
                        tempFrom +
                        ', ' +
                        (state.articles.name.includes(
                          item.fromEmail.name.split(' ')[0],
                        )
                          ? 'me'
                          : item.fromEmail.name.split(' ')[0])
                    }
                  } else {
                    if (
                      state.articles.name.includes(
                        item.fromEmail.name.split(' ')[0],
                      )
                    ) {
                      tempFrom = 'me'
                    } else {
                      tempFrom = item.fromEmail.name
                    }
                  }
                  itemFull.TotalMessage = itemFull.TotalMessage + 1
                }
              })
              itemFull.fromEmail.name = [...new Set(tempFrom.split(', '))]
                .reverse()
                .join(', ')
            })
          }
          tempObj.items = filteredArr

          return tempObj
        } else {
          const tempObj = JSON.parse(JSON.stringify(state.articles))
          if (tempObj && tempObj.items) {
            tempObj.items.forEach((item) => {
              if (
                state.selectedMessage &&
                item.id === state.selectedMessage.id
              ) {
                item.individualSelect = true
              } else {
                item.individualSelect = false
              }
            })
          }
          let filteredArr
          if (tempObj && tempObj.items) {
            filteredArr = tempObj.items.reduce((acc, current) => {
              const x = acc.find(
                (item) =>
                  (item.conversationId === current.conversationId &&
                    item.isDeliveryReceiptRequested === false) ||
                  item.conversationId === current.conversationId,
              )
              if (!x) {
                return acc.concat([current])
              } else {
                acc.forEach((item, index) => {
                  if (
                    item.conversationId === current.conversationId &&
                    item.isDeliveryReceiptRequested === false
                  ) {
                    acc[index] = current
                  }
                })
                return acc
              }
            }, [])
          }

          if (filteredArr) {
            filteredArr.forEach((itemFull) => {
              let tempFrom = ''
              itemFull.TotalMessage = 0
              tempObj.items.forEach((item) => {
                if (
                  (item.conversationId === itemFull.conversationId &&
                    item.isDeliveryReceiptRequested === false) ||
                  item.conversationId === itemFull.conversationId
                ) {
                  if (itemFull.TotalMessage > 0) {
                    if (
                      !tempFrom.includes(',') &&
                      !tempFrom.includes(item.fromEmail.name)
                    ) {
                      tempFrom =
                        tempFrom +
                        (tempFrom ? ', ' : '') +
                        (state.articles.name.includes(
                          item.fromEmail.name.split(' ')[0],
                        )
                          ? 'me'
                          : item.fromEmail.name.split(' ')[0])
                    } else if (
                      !tempFrom.includes(
                        state.articles.name.includes(
                          item.fromEmail.name.split(' ')[0],
                        )
                          ? 'me'
                          : item.fromEmail.name.split(' ')[0],
                      )
                    ) {
                      tempFrom =
                        tempFrom +
                        ', ' +
                        (state.articles.name.includes(
                          item.fromEmail.name.split(' ')[0],
                        )
                          ? 'me'
                          : item.fromEmail.name.split(' ')[0])
                    }
                  } else {
                    if (
                      state.articles.name.includes(
                        item.fromEmail.name.split(' ')[0],
                      )
                    ) {
                      tempFrom = 'me'
                    } else {
                      tempFrom = item.fromEmail.name
                    }
                  }
                  itemFull.TotalMessage = itemFull.TotalMessage + 1
                }
              })
              itemFull.fromEmail.name = [...new Set(tempFrom.split(', '))]
                .reverse()
                .join(', ')
            })
          }

          tempObj.items = filteredArr

          return tempObj
        }
      }
    },
    singleMessageShow(state) {
      if (state.currentSocialComponent.provider === 'Google') {
        const tempObj = JSON.parse(JSON.stringify(state.articles))
        if (state.currentTab === 'Incoming') {
          let tempArray = tempObj.items

          tempArray = tempObj.items.filter(
            (element) =>
              state.selectedMessage &&
              (element.historyId === state.selectedMessage.historyId ||
                element.threadId === state.selectedMessage.threadId),
          )
          tempArray.forEach((item) => {
            if (!item.body) {
              item.body = ''
              item.height = 0
            } else if (item.height) {
              item.height = item.height
            }
            item.setSignatureColor = false
            item.showSignatureButton = false
            if (item.attachments) {
              item.attachments.forEach((attachment) => {
                attachment.showImage = false
              })
            }
          })
          tempObj.items = tempArray

          return tempObj.items.reverse()
        } else if (state.currentTab === 'Outgoing') {
          let tempArray = tempObj.items

          tempArray = tempObj.items.filter(
            (element) =>
              state.selectedMessage &&
              (element.historyId === state.selectedMessage.historyId ||
                element.threadId === state.selectedMessage.threadId),
          )
          tempArray.forEach((item) => {
            if (!item.body) {
              item.body = ''
              item.height = 0
            } else if (item.height) {
              item.height = item.height
            }

            item.setSignatureColor = false
            item.showSignatureButton = false
            if (item.attachments) {
              item.attachments.forEach((attachment) => {
                attachment.showImage = false
              })
            }
          })
          tempObj.items = tempArray

          return tempObj.items.reverse()
        } else {
          const tempObj = JSON.parse(JSON.stringify(state.articles))
          let tempArray = tempObj.items
          tempArray = tempObj.items.filter(
            (element) =>
              state.selectedMessage &&
              (element.historyId === state.selectedMessage.historyId ||
                element.threadId === state.selectedMessage.threadId),
          )
          tempArray.forEach((item) => {
            if (!item.body) {
              item.body = ''
              item.height = 0
            } else if (item.height) {
              item.height = item.height
            }

            item.setSignatureColor = false
            item.showSignatureButton = false
            if (item.attachments) {
              item.attachments.forEach((attachment) => {
                attachment.showImage = false
              })
            }
          })
          tempObj.items = tempArray

          return tempObj.items.reverse()
        }
      } else if (state.currentSocialComponent.provider === 'Microsoft') {
        const tempObj = JSON.parse(JSON.stringify(state.articles))
        if (state.currentTab === 'Incoming') {
          let tempArray = tempObj.items

          tempArray = tempObj.items.filter(
            (element) =>
              state.selectedMessage &&
              element.conversationId === state.selectedMessage.conversationId,
          )
          tempArray.forEach((item) => {
            if (!item.body) {
              item.body = ''
              item.height = 0
              item.signatureHeight = 0
            }
            // else if (item.height) {
            //   item.height = item.height
            //   item.signatureHeight = item.signatureHeight
            // }

            item.setSignatureColor = false
            item.showSignatureButton = false
          })
          tempObj.items = tempArray

          return tempObj.items.reverse()
        } else if (state.currentTab === 'Outgoing') {
          let tempArray = tempObj.items

          tempArray = tempObj.items.filter(
            (element) =>
              state.selectedMessage &&
              element.conversationId === state.selectedMessage.conversationId,
          )
          tempArray.forEach((item) => {
            if (!item.body) {
              item.body = ''
              item.height = 0
              item.signatureHeight = 0
            }
            // else if (item.height) {
            //   item.height = item.height
            //   item.signatureHeight = item.signatureHeight
            // }

            item.setSignatureColor = false
            item.showSignatureButton = false
          })
          tempObj.items = tempArray

          return tempObj.items.reverse()
        } else {
          const tempObj = JSON.parse(JSON.stringify(state.articles))
          let tempArray = tempObj.items
          tempArray = tempObj.items.filter(
            (element) =>
              state.selectedMessage &&
              element.conversationId === state.selectedMessage.conversationId,
          )
          tempArray.forEach((item) => {
            if (!item.body) {
              item.body = ''
              item.height = 0
              item.signatureHeight = 0
            }
            // else if (item.height) {
            //   item.height = item.height
            //   item.signatureHeight = item.signatureHeight
            // }

            item.setSignatureColor = false
            item.showSignatureButton = false
          })
          tempObj.items = tempArray

          return tempObj.items.reverse()
        }
      }
    },
    selectedFeed(state) {
      return state.selectedFeed
    },
    showSearchFullMessage(state) {
      return state.showSearchMessageDetails
    },
    getShowSearchMessageFromSearch(state) {
      return state.showSearchMessageFromSearch
    },
    getSearchPersonSingle(state) {
      return state.showSearchPersonSingle
    },
    getSearchMessageSingle(state) {
      return state.showSearchMessageSingle
    },
    getSelectedMessageId(state) {
      return state.selectedMessageID
    },
    getSelectedMessageIdIndividual(state) {
      return state.selectedMessageIDIndividual
    },
    getSelectAccessType(state) {
      return state.selectAccessType
    },
    getCurrentArticlesId(state) {
      return state.currentSocialComponent.id
    },
    getPinterestCurrentTab(state) {
      return state.pinterestCurrentTab
    },
  },

  mutations: {
    SET_FACEBOOK_CURRENT_TAB(state, payload) {
      state.facebookCurrentTab = payload
    },
    SET_YOUTUBE_TYPE(state, payload) {
      state.youtubeType = payload
    },
    SET_PINTEREST_CURRENT_TAB(state, payload) {
      state.pinterestCurrentTab = payload
    },
    SET_WEB_SEARCH(state, payload) {
      state.webSeletedDate = payload.selectedDate
        ? payload.selectedDate
        : state.webSeletedDate
          ? state.webSeletedDate
          : ''
      state.webSearch.accountId = payload.accountId
        ? payload.accountId
        : state.webSearch.accountId
          ? state.webSearch.accountId
          : ''
      state.webSearch.archiveDate = payload.archiveDate
        ? payload.archiveDate
        : state.webSearch.archiveDate
          ? state.webSearch.archiveDate
          : ''
      state.webSearch.search = payload.search
        ? payload.search
        : state.webSearch.search
          ? state.webSearch.search
          : ''
      if (state.webSearch.search) {
        state.webSearchClear = true
      }
      if (state.webSearch.archiveDate) {
        state.selectedWebArchiveDate = state.webSearch.archiveDate
      }
      if (
        state.articles.items &&
        state.articles.items.length > 0 &&
        !state.webSearchResult
      ) {
        state.articles.items.forEach((item) => {
          if (
            item.archiveDate === payload.archiveDate ||
            item.archiveDate === state.webSearch.archiveDate
          ) {
            state.selectedWebURL = item.url
            state.selectedWebArchiveDate = item.archiveDate
          }
        })
      }
      this.dispatch('home/getWebSearchResult', state.webSearch)
    },
    SET_WEB_SEARCH_FROM_USER_INFO(state, payload) {
      state.selectedWebArchiveDate = payload.archiveDate
      if (
        state.articles.items &&
        state.articles.items.length > 0 &&
        !state.webSearchResult
      ) {
        state.articles.items.forEach((item) => {
          if (payload.archiveDate && payload.archiveDate.includes('T')) {
            const dateTimeParts = payload.archiveDate
              ? payload.archiveDate.split('T')
              : ''
            const datePart = dateTimeParts ? dateTimeParts[0] : ''
            if (item.archiveDate === datePart) {
              state.selectedWebURL = item.url
              state.selectedWebArchiveDate = item.archiveDate
            }
          } else if (
            payload.archiveDate &&
            !payload.archiveDate.includes('T')
          ) {
            if (item.archiveDate === payload.archiveDate) {
              state.selectedWebURL = item.url
              state.selectedWebArchiveDate = item.archiveDate
            }
          }
        })
      }
    },
    RESET_WEB_SEARCH(state) {
      state.selectedWebURL = ''
      state.selectedWebArchiveDate = ''
      state.webSearchClear = false
      state.webSearchResult = null
      state.webSeletedDate = ''
      state.webSearch.accountId = ''
      state.webSearch.archiveDate = ''
      state.webSearch.search = ''
    },
    SET_WEB_SEARCH_RESULT(state, payload) {
      state.webSearchResult = payload
    },
    SET_CLIENT_HEIGHT(state, payload) {
      const temp = state.singleCovertions.messages.map((item) => {
        if (item.id === payload.id) {
          item.clientHeight = payload.height
        }
        return item
      })
      state.singleCovertions.messages = temp
    },
    SET_INITIAL_FULL_MESSAGE_FEED(state) {
      // set all searching value default
      state.currentMessage = 'HomeMessageFeedAllMessagePerson'
      state.searchMessagePeople = []
      state.searchMessageText = []
      // state.singleCovertions = null
      state.searchText = ''
      // state.messageId = 0
      state.showSearchMessageDetails = false
      state.showSearchPersonSingle = false
      state.showSearchMessageSingle = false
      state.selectedMessageID = null
      state.selectedMessageIDIndividual = null
      state.searchFromTextMessage = false
    },
    RESET_SEARCH_PERSON(state) {
      state.searchMessagePeople = []
    },
    SET_SHOW_SEARCH_MESSAGE_FROM_SEARCH(state, payload) {
      state.showSearchMessageFromSearch = payload
    },
    SET_SHOW_SEARCH_MESSAGE_DETAILS(state, payload) {
      state.showSearchMessageDetails = payload
    },
    SET_SHOW_SEARCH_PERSON_SINGLE(state, payload) {
      state.showSearchPersonSingle = payload
    },
    SET_SHOW_SEARCH_MESSAGE_SINGLE(state, payload) {
      state.showSearchMessageSingle = payload
    },
    SET_SELECTED_PlayList(state, payload) {
      state.selectedPlayList = payload
    },
    SET_YOUTUBE_COMMENT(state, payload) {
      state.youtubeComment = payload
    },
    SET_YOUTUBE_TRANSCRIPT(state, payload) {
      const array = []
      state.youtubeTranscript = payload
      state.youtubeTranscript.forEach((item) => {
        const str = item.text.split('\n')
        let index = 0
        str.forEach((value) => {
          if (value) {
            // eslint-disable-next-line
            const specialCharacter = /[ `!@#$%^&*()_+\-=\[\]{};'"\\|<>\/?~]/
            const numberSpecialCharacter = /[0-9\s]/
            const comaCharacter = /[:,.]/
            if (/^[a-zA-Z\s]+$/.test(value) || specialCharacter.test(value)) {
              array[index] = value
              index = index + 1
            } else if (
              numberSpecialCharacter.test(value) &&
              comaCharacter.test(value)
            ) {
              const str1 = value.split(',')[0].slice(0, -4).slice(-4)
              array[index] = str1
              index = index + 1
            } else {
              array[index] = value
              index = index + 1
            }
          }
        })
        const tempArray = []
        while (array.length > 0) {
          tempArray.push(array.splice(0, 2))
        }
        state.Transcripts = tempArray
      })
    },
    SET_YOUTUBE_PLAYLIST(state, payload) {
      state.youtubePlayLists = payload
    },
    SET_YOUTUBE_PLAYLIST_ITEMS(state, payload) {
      state.youtubePlayListItems = payload
    },
    SET_MORE_YOUTUBE_PLAYLIST_ITEMS(state, payload) {
      payload.forEach((item) => {
        state.youtubePlayListItems.items.push(item)
      })
    },
    SET_HOME_CURRENT_COMP(state, payload) {
      state.homeCurrentComp = payload
    },
    SET_IS_SHOW_MESSAGE(state, payload) {
      state.isShowMessage = payload
    },
    SET_SELECT_ACCESS_TYPE(state, payload) {
      state.selectAccessType = payload
    },
    SET_TEMP_ARRAY(state, payload) {
      const filteredArr = payload.reduce((acc, current) => {
        const x = acc.find((item) => item.id === current.id)
        if (!x) {
          return acc.concat([current])
        } else {
          return acc
        }
      }, [])
      state.tempSingleMessage = filteredArr
      if (state.tempSingleMessage.length > 0) {
        state.tempSingleMessage[state.tempSingleMessage.length - 1].selected =
          true
        if (state.tempSingleMessage.every((item) => item.selected === true)) {
          state.allExpanded = true
        }
      }
    },
    SET_CURRENT_CONTENT_HEIGHT(state, payload) {
      state.tempSingleMessage.forEach((item, i) => {
        if (
          item.id === payload.id &&
          (item.height === 0 || state.tempSingleMessage.length === 1)
        ) {
          item.height = payload.height
        }
      })
      state.articles.items.forEach((item, i) => {
        if (
          item.id === payload.id &&
          (item.height === 0 || state.tempSingleMessage.length === 1)
        ) {
          item.height = payload.height
        }
      })
    },
    SET_CURRENT_CONTENT_SIGNATURE(state, payload) {
      state.tempSingleMessage.forEach((item) => {
        if (item.id === payload.id && !payload.setSignatureColor) {
          item.height = payload.height
          item.setSignatureColor = payload.setSignatureColor
        } else if (item.id === payload.id && payload.setSignatureColor) {
          item.height = payload.height
          item.setSignatureColor = payload.setSignatureColor
        }
      })
    },
    SET_SHOW_SIGNATURE_BUTTON(state, payload) {
      state.tempSingleMessage.forEach((item) => {
        if (item.id === payload.id) {
          item.showSignatureButton = payload.showSignatureButton
          item.signatureHeight = payload.signatureHeight
        }
        // else {
        //   item.showSignatureButton = false
        // }
      })
    },
    SET_SINGLE_MESSAGE_SHOW(state, payload) {
      state.selectedMessage = payload.singleEmail
      state.allExpanded = false
    },
    SET_CURRENT_YOUTUBE_CONTENT(state, payload) {
      state.currentYoutubeContent = payload
    },
    SET_YOUTUBE_VIDEO_DURATION(state, payload) {
      const shortDuration = payload.split(',')[0]
      const selectedVideo = payload.split(',')[1]
      const fullFormDuration = shortDuration.split(':')
      if (fullFormDuration.length === 3) {
        const fullDuration = `${fullFormDuration[0]} hour ${fullFormDuration[1]} min ${fullFormDuration[2]} sec`
        for (let i = 0; i < state.articles.items.length; i++) {
          // eslint-disable-next-line eqeqeq
          if (state.articles.items[i].id === selectedVideo) {
            state.articles.items[i].fullDuration = fullDuration
          }
        }
        for (let i = 0; i < state.tempArticles.length; i++) {
          // eslint-disable-next-line eqeqeq
          if (state.tempArticles[i].id === selectedVideo) {
            state.tempArticles[i].fullDuration = fullDuration
          }
        }
      } else if (fullFormDuration.length === 2) {
        const fullDuration = `${fullFormDuration[0]} min ${fullFormDuration[1]} sec`
        for (let i = 0; i < state.articles.items.length; i++) {
          // eslint-disable-next-line eqeqeq
          if (state.articles.items[i].id == selectedVideo) {
            state.articles.items[i].fullDuration = fullDuration
          }
        }
        for (let i = 0; i < state.tempArticles.length; i++) {
          // eslint-disable-next-line eqeqeq
          if (state.tempArticles[i].id == selectedVideo) {
            state.tempArticles[i].fullDuration = fullDuration
          }
        }
      } else {
        const fullDuration = `${fullFormDuration[0]} sec`
        for (let i = 0; i < state.articles.items.length; i++) {
          // eslint-disable-next-line eqeqeq
          if (state.articles.items[i].id == selectedVideo) {
            state.articles.items[i].fullDuration = fullDuration
          }
        }
        for (let i = 0; i < state.tempArticles.length; i++) {
          // eslint-disable-next-line eqeqeq
          if (state.tempArticles[i].id == selectedVideo) {
            state.tempArticles[i].fullDuration = fullDuration
          }
        }
      }
      for (let i = 0; i < state.articles.items.length; i++) {
        // eslint-disable-next-line eqeqeq
        if (state.articles.items[i].id == selectedVideo) {
          state.articles.items[i].shortDuration = shortDuration
        }
      }
      for (let i = 0; i < state.tempArticles.length; i++) {
        // eslint-disable-next-line eqeqeq
        if (state.tempArticles[i].id == selectedVideo) {
          state.tempArticles[i].shortDuration = shortDuration
        }
      }
    },
    SET_CURRENT_TAB(state, payload) {
      state.currentTab = payload
      state.allExpanded = false
      state.emailDynamicComp = 'EmailContent'
      if (state.currentTab !== 'All') {
        setTimeout(() => {
          this.dispatch('home/getAllSocialArticle', {
            id: state.currentSocialComponent.id,
          })
        }, 500)
      }
      const tempObj = JSON.parse(JSON.stringify(state.articles))
      if (state.currentSocialComponent.provider === 'Google') {
        if (state.currentTab === 'Incoming') {
          let tempArray = tempObj.items
          tempArray = tempArray.filter((element) =>
            element.labels.includes('INBOX'),
          )
          tempObj.items = tempArray
          state.selectedMessage = tempObj.items[0]
        } else if (state.currentTab === 'Outgoing') {
          let tempArray = tempObj.items
          tempArray = tempArray.filter((element) =>
            element.labels.includes('SENT'),
          )
          tempObj.items = tempArray
          state.selectedMessage = tempObj.items[0]
        } else {
          const tempObj = JSON.parse(JSON.stringify(state.articles))
          for (let i = 0; i < tempObj.items.length; i++) {
            for (let j = i + 1; j < tempObj.items.length; j++) {
              if (
                (tempObj.items[i].historyId === tempObj.items[j].historyId ||
                  tempObj.items[i].threadId === tempObj.items[j].threadId) &&
                tempObj.items[i].labels.includes('SENT')
              ) {
                tempObj.items.splice(i, 1)
              }
            }
          }
          state.selectedMessage = tempObj.items[0]
        }
      } else if (state.currentSocialComponent.provider === 'Microsoft') {
        if (state.currentTab === 'Incoming') {
          let tempArray = tempObj.items
          tempArray = tempArray.filter(
            (element) => element.isDeliveryReceiptRequested === null,
          )
          tempObj.items = tempArray
          state.selectedMessage = tempObj.items[0]
        } else if (state.currentTab === 'Outgoing') {
          let tempArray = tempObj.items
          tempArray = tempArray.filter(
            (element) => element.isDeliveryReceiptRequested === false,
          )
          tempObj.items = tempArray
          state.selectedMessage = tempObj.items[0]
        } else {
          const tempObj = JSON.parse(JSON.stringify(state.articles))
          for (let i = 0; i < tempObj.items.length; i++) {
            for (let j = i + 1; j < tempObj.items.length; j++) {
              if (
                tempObj.items[i].conversationId ===
                  tempObj.items[j].conversationId &&
                tempObj.items[i].isDeliveryReceiptRequested === false
              ) {
                tempObj.items.splice(i, 1)
              }
            }
          }
          state.selectedMessage = tempObj.items[0]
        }
      }
    },
    SET_EXPAND_ALL(state, payload) {
      state.allExpanded = payload
    },
    SHOW_CURRENT_CONTENT(state, payload) {
      state.tempSingleMessage.forEach((item) => {
        if (item.id === payload) {
          item.selected = !item.selected
          // item.height = 0
          if (item.setSignatureColor) {
            item.setSignatureColor = true
          } else if (!item.setSignatureColor) {
            item.setSignatureColor = false
          }

          // item.individualSelect = !item.individualSelect
          // if (item.individualSelect) {
          //   item.selected = true
          // } else {
          //   item.selected = false
          // }
        }
        // else {
        //   item.individualSelect = false
        // }
      })

      const value = state.tempSingleMessage.every(
        (item) => item.selected === true,
      )
      if (value === true) {
        state.allExpanded = true
      }
      const secondValue = state.tempSingleMessage.every(
        (item) => item.selected === false,
      )
      if (secondValue === true) {
        state.allExpanded = false
      }
    },
    EXPAND_All(state) {
      if (state.allExpanded === true) {
        state.allExpanded = false
        state.tempSingleMessage.forEach((item) => {
          item.selected = false
          // item.height = 0
          if (item.setSignatureColor) {
            item.setSignatureColor = true
          } else if (!item.setSignatureColor) {
            item.setSignatureColor = false
          }
        })
      } else if (state.allExpanded === false) {
        state.allExpanded = true
        state.tempSingleMessage.forEach((item) => {
          item.selected = true
          if (!item.body) {
            this.dispatch('home/getCurrentSocialArticle', {
              accountId: state.currentSocialComponent.id,
              id: item.id,
            })
          }
        })
      }
    },
    SET_EMAIL_DYNAMIC_COMP(state, payload) {
      state.emailDynamicComp = payload.comp
      state.singleMessage = payload.attachments
      state.index = payload.attachmentIndex
      state.currentIndex = payload.currentIndex
      state.messageId = payload.messageId
      state.attachment = payload.attachment
    },
    SET_SINGLE_MESSAGE(state, payload) {
      state.singleMessage = payload
    },
    SET_CURRENT_PAGE_NUMBER(state, payload) {
      state.currentIndex = payload.currentIndex
    },
    EXPAND_FULL_IMAGE(state, payload) {
      state.expandFullImage = payload
    },
    SET_ALL_LATEST_DATA(state, payload) {
      state.latest.tbody = payload
    },
    SET_VIDEO_DURATION(state, payload) {
      state.tempArticles = payload
      if (payload) {
        payload.map((item) => {
          let fullDuration
          if (item.duration && item.duration.includes('PT')) {
            fullDuration = item.duration.split('PT')
          }
          if (
            fullDuration &&
            fullDuration.length > 2 &&
            fullDuration[1].includes('H')
          ) {
            const totalHour = fullDuration[1].split('H')[0]
            const totalMinute = fullDuration[1].split('H')[1].split('M')[0]
            const totalSecond = fullDuration[1]
              .split('H')[1]
              .split('M')[1]
              .split('S')[0]
            const timeAndId =
              totalSecond.length > 1
                ? `${totalHour}:${totalMinute}:${totalSecond},${item.id}`
                : `${totalHour}:${totalMinute}:0${totalSecond},${item.id}`
            this.commit('home/SET_YOUTUBE_VIDEO_DURATION', timeAndId)
          } else if (
            fullDuration &&
            fullDuration.length > 2 &&
            fullDuration[1].includes('M')
          ) {
            const totalMinute = fullDuration[1].split('M')[0]
            const totalSecond = fullDuration[1].split('M')[1].split('S')[0]
            const timeAndId =
              totalSecond.length > 1
                ? `${totalMinute}:${totalSecond},${item.id}`
                : `${totalMinute}:0${totalSecond},${item.id}`
            this.commit('home/SET_YOUTUBE_VIDEO_DURATION', timeAndId)
          } else if (fullDuration && fullDuration.length > 2) {
            const totalSecond = fullDuration[1].split('S')[0]
            const timeAndId =
              totalSecond.length > 1
                ? `0:${totalSecond},${item.id}`
                : `0:0${totalSecond},${item.id}`
            this.commit('home/SET_YOUTUBE_VIDEO_DURATION', timeAndId)
          }
          return 0
        })
      }
      state.tempArticles = payload
    },
    SET_ALL_SOCIAL_ARTICLE(state, payload) {
      if (
        state.currentSocialComponent.provider !== 'Twitter' &&
        state.currentSocialComponent.provider !== 'Google' &&
        state.currentSocialComponent.provider !== 'Microsoft' &&
        state.currentSocialComponent.provider !== 'YouTube'
      ) {
        state.articles = payload
        if (state.currentSocialComponent.provider === 'Web') {
          state.initialWebURL =
            state.articles.items && state.articles.items.length > 0
              ? state.articles.items[0].url
              : ''
          state.initialWebArchiveDate =
            state.articles.items && state.articles.items.length > 0
              ? state.articles.items[0].archiveDate
              : ''
        }
        if (state.currentSocialComponent.provider === 'Reddit') {
          state.articles.pinnedItems = []
          state.articles.items.forEach((element) => {
            element.selected = false
          })
          state.articles.items.forEach((element, index) => {
            if (element.pinned) {
              state.articles.pinnedItems.push(element)
            }
          })
          state.articles.items.forEach((element, index) => {
            if (element.pinned) {
              state.articles.items.splice(index, 1)
            }
          })
        }
      } else if (state.currentSocialComponent.provider === 'Google') {
        payload.items.forEach((element) => {
          element.selected = false
          element.individualSelect = false
          if (element.from) {
            if (element.from.includes('<')) {
              const array = element.from.split('<')
              element.from = array[0]
              element.email = '(' + array[1].replace(/>/g, ')')
            } else {
              element.from = ''
              element.email = element.from
            }
          }
        })
        state.articles = payload
        state.selectedMessage = state.articles.items[0]
      } else if (state.currentSocialComponent.provider === 'Microsoft') {
        payload.items.forEach((element) => {
          element.selected = false
          element.individualSelect = false
        })
        state.articles = payload
        state.selectedMessage = state.articles.items[0]
      } else if (state.currentSocialComponent.provider === 'YouTube') {
        payload.items.forEach((item) => {
          if (item.transcript && typeof item.transcript === 'string') {
            const lines = item.transcript.trim().split('\n')
            const newArray = []
            for (let i = 0; i < lines.length; i++) {
              // Ensure the element is a string and contains a comma
              if (typeof lines[i] === 'string' && lines[i].includes(',')) {
                const timeParts = lines[i].split(',')[0] // Extract start time
                const text =
                  lines[i + 1] && typeof lines[i + 1] === 'string'
                    ? lines[i + 1].trim()
                    : '' // Safely access next element
                if (text) {
                  // Convert time to MM:SS format (ignore milliseconds)
                  const timeSegments = timeParts.split(':')
                  if (timeSegments.length === 3) {
                    const hours = parseInt(timeSegments[0], 10) // Convert hours to integer to remove leading zero
                    const minutes = parseInt(timeSegments[1], 10) // Convert minutes to integer to remove leading zero
                    const seconds = timeSegments[2].split('.')[0] // Extract seconds
                    const formattedTime =
                      hours > 0
                        ? `${hours}:${minutes}:${seconds}`
                        : `${minutes}:${seconds}`
                    newArray.push({
                      start: formattedTime,
                      text: text,
                    })
                  }
                }
              }
            }
            item.transcript = newArray
          }
        })
        state.articles = payload
      } else {
        payload.items.forEach((item) => {
          if (item.entities.urls !== undefined) {
            item.entities.urls.forEach((value) => {
              if (item.text.includes(value.url)) {
                if ('value.url' in state.words === false) {
                  state.words[value.url] =
                    '<a rel="noopener noreferrer" class="text-blue-600" href="' +
                    value.url +
                    '" target="_blank">' +
                    value.display_url +
                    '</a>'
                }
              }
            })
            Object.keys(state.words).forEach((key) => {
              item.text = item.text.replaceAll(key, state.words[key])
            })
          }
          return item.text
        })
        state.articles = payload
      }
    },
    SET_WEB_ARTICLES(state, payload) {
      state.selectedWebURL = ''
      setTimeout(() => {
        this.commit('home/SET_FINAL_WEB_ARTICLES', payload)
      })
    },
    SET_FINAL_WEB_ARTICLES(state, payload) {
      state.selectedWebURL = payload
    },
    SET_LOADING_ARTICLE(state, payload) {
      state.loadArticles = payload
    },
    SET_LOADING_MORE_ARTICLE_SKELATON(state, payload) {
      state.loadMoreArticlesSkelaton = payload
    },
    SET_LOADING_MORE_ARTICLE(state, payload) {
      state.loadMoreArticles = payload
    },
    SET_LOADING_MORE_PLAYLIST_VIDEO(state, payload) {
      state.loadMorePlaylistVideo = payload
    },
    SET_CURRENT_HEADER(state, payload) {
      state.currentHeader = payload
    },
    SET_CURRENT_MESSAGE(state, payload) {
      state.currentMessage = payload
    },
    SET_SEARCH_FROM_TEXT_MESSAGE(state, payload) {
      state.searchFromTextMessage = payload
    },
    SET_SHOW_MOBILE_MENU(state, payload) {
      state.showMobileMenu = payload
    },
    SET_CURRENT_SOCIAL_COMPONENT(state, payload) {
      if (payload.from && state.showSearchMessageFromSearch) {
        state.currentSocialComponent.provider = payload.provider
        state.allMessagePersons.provider = payload.provider
      } else {
        state.currentSocialComponent = payload
        state.selectedFeed = payload.selectedFeed
        // if (((state.currentSocialComponent.provider === 'Google') || (state.currentSocialComponent.provider === 'Microsoft') || (state.currentSocialComponent.provider === 'Calls') || (state.currentSocialComponent.provider === 'Faxes') || (state.currentSocialComponent.provider === 'Texts') || (state.currentSocialComponent.provider === 'Websites') || (state.currentSocialComponent.provider === 'Youtube')) && state.currentHeader !== 'LatestArchive') {
        //   state.currentHeader = "RealTimeFeed"
        // }
        setTimeout(() => {
          this.dispatch('home/getAllSocialArticle', {
            id: payload.id,
            archiveDate: payload.archiveDate,
          })
          this.dispatch('home/getAllMessagePersons', payload.id)
          if (state.currentSocialComponent.provider === 'YouTube') {
            this.dispatch('home/getYoutubePlaylist', payload.id)
          }
        }, 500)
      }
    },
    SET_LOAD_MORE_ARTICLES(state, payload) {
      if (
        state.currentSocialComponent.provider !== 'Twitter' &&
        state.currentSocialComponent.provider !== 'Google' &&
        state.currentSocialComponent.provider !== 'Microsoft' &&
        state.currentSocialComponent.provider !== 'YouTube' &&
        state.currentSocialComponent.provider === 'Pinterest' &&
        state.currentSocialComponent.provider !== 'Reddit'
      ) {
        payload.items.forEach((value) => {
          state.articles.items.push(value)
        })
      } else if (state.currentSocialComponent.provider === 'Reddit') {
        payload.items.forEach((value) => {
          state.articles.items.push(value)
        })
        if (!state.articles.pinnedItems) {
          state.articles.pinnedItems = []
        }
        state.articles.items.forEach((element) => {
          element.selected = false
        })
        state.articles.items.forEach((element) => {
          if (element.pinned) {
            state.articles.pinnedItems.push(element)
          }
        })
        state.articles.items.forEach((element, index) => {
          if (element.pinned) {
            state.articles.items.splice(index, 1)
          }
        })
      } else if (state.currentSocialComponent.provider === 'Google') {
        payload.items.forEach((element) => {
          // if (state.allExpanded) {
          //   element.selected = true
          // } else {
          element.selected = false
          // }
          element.individualSelect = false
          if (
            element.from &&
            element.from.includes('<') &&
            element.from.includes('>')
          ) {
            const array = element.from.split(' <')
            element.from = array[0]
            element.email = '(' + array[1].replace(/>/g, ')')
          }
        })
        payload.items.forEach((value) => {
          state.articles.items.push(value)
        })
      } else if (state.currentSocialComponent.provider === 'Microsoft') {
        payload.items.forEach((element) => {
          // if (state.allExpanded) {
          //   element.selected = true
          // } else {
          element.selected = false
          // }
          element.individualSelect = false
        })
        payload.items.forEach((value) => {
          state.articles.items.push(value)
        })
      } else if (state.currentSocialComponent.provider === 'YouTube') {
        payload.items.forEach((item) => {
          if (item.transcript && typeof item.transcript === 'string') {
            const lines = item.transcript.trim().split('\n')
            const newArray = []
            for (let i = 0; i < lines.length; i++) {
              // Ensure the element is a string and contains a comma
              if (typeof lines[i] === 'string' && lines[i].includes(',')) {
                const timeParts = lines[i].split(',')[0] // Extract start time
                const text =
                  lines[i + 1] && typeof lines[i + 1] === 'string'
                    ? lines[i + 1].trim()
                    : '' // Safely access next element
                if (text) {
                  // Convert time to MM:SS format (ignore milliseconds)
                  const timeSegments = timeParts.split(':')
                  if (timeSegments.length === 3) {
                    const hours = parseInt(timeSegments[0], 10) // Convert hours to integer to remove leading zero
                    const minutes = parseInt(timeSegments[1], 10) // Convert minutes to integer to remove leading zero
                    const seconds = timeSegments[2].split('.')[0] // Extract seconds
                    const formattedTime =
                      hours > 0
                        ? `${hours}:${minutes}:${seconds}`
                        : `${minutes}:${seconds}`
                    newArray.push({
                      start: formattedTime,
                      text: text,
                    })
                  }
                }
              }
            }
            item.transcript = newArray
          }
          state.articles.items.push(item)
        })
      } else {
        payload.items.forEach((value) => {
          if (value.entities && value.entities.urls !== undefined) {
            value.entities.urls.forEach((item) => {
              if (value.text.includes(item.url)) {
                if ('item.url' in state.words === false) {
                  state.words[item.url] =
                    '<a rel="noopener noreferrer" class="text-blue-600" href="' +
                    item.url +
                    '" target="_blank">' +
                    item.display_url +
                    '</a>'
                }
              }
            })
            Object.keys(state.words).forEach((key) => {
              value.text = value.text.replaceAll(key, state.words[key])
            })
          }
          state.articles.items.push(value)
        })
      }

      if (state.currentSocialComponent.provider === 'YouTube') {
        this.commit('home/SET_VIDEO_DURATION', state.articles.items)
      }
    },
    SET_HEIGHT(state, payload) {
      state.bodyHeight = payload
    },
    SET_MOBILE_HEIGHT(state, payload) {
      state.bodyMobileHeight = payload
    },
    SET_EXPAND(state, payload) {
      state.detailsExpand = payload
    },
    SET_SHOW_COMP(state, payload) {
      state.showComp = payload
    },
    SET_SELECTED_ITEM(state, payload) {
      state.websiteLog.tbody.forEach((item) => {
        if (item.id === payload) {
          item.selected = !item.selected
        }
      })
    },
    SET_TWITTER_COMMENT(state, payload) {
      // payload.items.forEach((item) => {
      //   if (item.entities.urls !== undefined) {
      //     item.entities.urls.forEach((value) => {
      //       if (item.text.includes(value.url)) {
      //         if ('value.url' in state.words === false) {
      //           state.words[value.url] =
      //             '<a rel="noopener noreferrer" class="text-blue-600" href="' +
      //             value.url +
      //             '" target="_blank">' +
      //             value.display_url +
      //             '</a>'
      //         }
      //       }
      //     })
      //     Object.keys(state.words).forEach((key) => {
      //       item.text = item.text.replaceAll(key, state.words[key])
      //     })
      //   }
      //   return item.text
      // })
      state.twitterComment = payload
    },
    SET_PREVIOUS_TWITTER_COMMENT(state, payload) {
      state.previousTwitterComment.unshift(payload)
    },
    BACK_TO_PREVIOUS_COMMENT(state, payload) {
      state.twitterComment = payload
      state.previousTwitterComment.splice(0, 1)
    },
    RESET_TWITTER_COMMENTS(state) {
      state.twitterComment = []
    },
    RESET_PREVIOUS_TWITTER_COMMENTS(state) {
      state.previousTwitterComment = []
    },
    // set response of person when search in full message feed
    SET_SEARCH_PERSON(state, payload) {
      if (payload.response.length > 0) {
        const searchPeopleResponse = payload.response
        if (state.currentSocialComponent.provider === 'Instagram') {
          searchPeopleResponse.forEach((item, index) => {
            if (payload.searchText !== '') {
              const index = item.name
                .toLowerCase()
                .indexOf(payload.searchText.toLowerCase())
              const markAbleTextLastString = item.name
                .substring(index, index + payload.searchText.length)
                .slice(-1)

              if (markAbleTextLastString === '&') {
                item.name =
                  item.name.substring(0, index) +
                  "<span class='highlight font-bold text-yellow-primary'>" +
                  item.name.substring(
                    index,
                    index + payload.searchText.length,
                  ) +
                  '</span>' +
                  item.name
                    .substring(index + payload.searchText.length)
                    .substring(4)
              } else {
                item.name =
                  item.name.substring(0, index) +
                  "<span class='highlight font-bold text-yellow-primary'>" +
                  item.name.substring(
                    index,
                    index + payload.searchText.length,
                  ) +
                  '</span>' +
                  item.name.substring(index + payload.searchText.length)
              }

              item.participants.forEach((people) => {
                const indexParticipant = people.username
                  .toLowerCase()
                  .indexOf(payload.searchText.toLowerCase())
                const markAbleTextLastString = people.username
                  .substring(
                    indexParticipant,
                    indexParticipant + payload.searchText.length,
                  )
                  .slice(-1)

                if (markAbleTextLastString === '&') {
                  people.username =
                    people.username.substring(0, indexParticipant) +
                    "<span class='highlight font-bold text-yellow-primary'>" +
                    people.username.substring(
                      indexParticipant,
                      indexParticipant + payload.searchText.length,
                    ) +
                    '</span>' +
                    people.username
                      .substring(indexParticipant + payload.searchText.length)
                      .substring(4)
                } else {
                  people.username =
                    people.username.substring(0, indexParticipant) +
                    "<span class='highlight font-bold text-yellow-primary'>" +
                    people.username.substring(
                      indexParticipant,
                      indexParticipant + payload.searchText.length,
                    ) +
                    '</span>' +
                    people.username.substring(
                      indexParticipant + payload.searchText.length,
                    )
                }
              })
              item.selected = false
              if (index === 0) {
                item.selected = true
              }
            }
          })
        } else {
          searchPeopleResponse.forEach((item, index) => {
            item.participants.forEach((people, index) => {
              // mark and highlight the search text
              if (payload.searchText !== '') {
                const index = people.name
                  .toLowerCase()
                  .indexOf(payload.searchText.toLowerCase())
                const markAbleTextLastString = people.name
                  .substring(index, index + payload.searchText.length)
                  .slice(-1)

                if (markAbleTextLastString === '&') {
                  people.name =
                    people.name.substring(0, index) +
                    "<span class='highlight font-bold text-yellow-primary'>" +
                    people.name.substring(
                      index,
                      index + payload.searchText.length,
                    ) +
                    '</span>' +
                    people.name
                      .substring(index + payload.searchText.length)
                      .substring(4)
                } else {
                  people.name =
                    people.name.substring(0, index) +
                    "<span class='highlight font-bold text-yellow-primary'>" +
                    people.name.substring(
                      index,
                      index + payload.searchText.length,
                    ) +
                    '</span>' +
                    people.name.substring(index + payload.searchText.length)
                }
              }
            })
            item.selected = false
            if (index === 0) {
              item.selected = true
            }
          })
        }
        state.searchMessagePeople = searchPeopleResponse
      } else {
        state.searchMessagePeople = []
      }
    },
    // set response of message when search in full message feed
    SET_SEARCH_MESSAGE(state, payload) {
      if (payload.response.length > 0) {
        state.searchMessageText = []
        // For full view functionality
        if (!state.searchFromTextMessage) {
          const searchMessageResponse = payload.response
          searchMessageResponse.forEach((item, index) => {
            item.messages.forEach((message, index) => {
              // mark and highlight the search text
              if (payload.searchText !== '') {
                const index = message.text
                  .toLowerCase()
                  .indexOf(payload.searchText.toLowerCase())
                const markAbleTextLastString = message.text
                  .substring(index, index + payload.searchText.length)
                  .slice(-1)

                if (markAbleTextLastString === '&') {
                  message.text =
                    message.text.substring(0, index) +
                    "<span class='highlight font-bold text-yellow-primary'>" +
                    message.text.substring(
                      index,
                      index + payload.searchText.length,
                    ) +
                    '</span>' +
                    message.text
                      .substring(index + payload.searchText.length)
                      .substring(4)
                } else {
                  message.text =
                    message.text.substring(0, index) +
                    "<span class='highlight font-bold text-yellow-primary'>" +
                    message.text.substring(
                      index,
                      index + payload.searchText.length,
                    ) +
                    '</span>' +
                    message.text.substring(index + payload.searchText.length)
                }
              }
              message.selected = false
            })
          })
          state.searchMessageText = searchMessageResponse
        }
        // For single view functionality
        else if (state.searchFromTextMessage) {
          const updateSearchMessageResponse = payload.response
          updateSearchMessageResponse.forEach((item, index) => {
            if (item.id === state.selectedMessageID) {
              const alsoUpdate = item
              alsoUpdate.messages.forEach((message, index) => {
                // mark and highlight the search text
                if (payload.searchText !== '') {
                  const index = message.text
                    .toLowerCase()
                    .indexOf(payload.searchText.toLowerCase())
                  const markAbleTextLastString = message.text
                    .substring(index, index + payload.searchText.length)
                    .slice(-1)

                  if (markAbleTextLastString === '&') {
                    message.text =
                      message.text.substring(0, index) +
                      "<span class='highlight font-bold text-yellow-primary'>" +
                      message.text.substring(
                        index,
                        index + payload.searchText.length,
                      ) +
                      '</span>' +
                      message.text
                        .substring(index + payload.searchText.length)
                        .substring(4)
                  } else {
                    message.text =
                      message.text.substring(0, index) +
                      "<span class='highlight font-bold text-yellow-primary'>" +
                      message.text.substring(
                        index,
                        index + payload.searchText.length,
                      ) +
                      '</span>' +
                      message.text.substring(index + payload.searchText.length)
                  }
                }
                message.selected = false
              })
              state.searchMessageText = [alsoUpdate]
            }
          })
        }
      } else {
        state.searchMessageText = []
      }
    },
    SET_ALL_MESSAGE_PERSONS(state, payload) {
      if (payload.items.length > 0) {
        payload.items.forEach((item) => {
          item.selected = false
          if (
            item.participants.length === 1 &&
            payload.provider === 'Instagram'
          ) {
            item.participants[1] = {
              profileImageUrl: '',
              username: 'Instagram User',
            }
          }
        })
        payload.items[0].selected = true
      }
      state.allMessagePersons = payload
      // state.allMessagePersons = state.socialChat
    },
    SET_MESSAGE_SKELETON(state, payload) {
      state.showMessageskeleton = payload
    },
    SET_PERSON_SKELETON(state, payload) {
      state.showPersonSkeleton = payload
    },
    SET_MORE_MESSAGE_SKELETON(state, payload) {
      state.showMoreMessageSkeleton = payload
    },
    SET_SINGLE_CONVERTIONS(state, payload) {
      state.singleCovertions = payload
      if (state.singleCovertions) {
        state.singleCovertions.messages = payload.messages.reverse()
        state.allMessagePersons.items.forEach((element) => {
          if (element.id === payload.id) {
            element.messageCount = payload.messageCount
            element.snippet = payload.snippet
            element.unreadCount = payload.unreadCount
            element.updatedAt = payload.updatedAt
            element.selected = true
          }
          if (element.id !== payload.id) {
            element.selected = false
          }
        })
        // select the select people of message
        state.searchMessagePeople.forEach((element) => {
          if (element.id === payload.id) {
            element.messageCount = payload.messageCount
            element.snippet = payload.snippet
            element.unreadCount = payload.unreadCount
            element.updatedAt = payload.updatedAt
            element.selected = true
          }
          if (element.id !== payload.id) {
            element.selected = false
          }
        })

        state.searchMessageText.forEach((element) => {
          element.messages.forEach((message, index) => {
            if (message.messageId === state.selectedMessageIDIndividual) {
              message.selected = true
            } else {
              message.selected = false
            }
          })
        })
      }
    },
    RESET_SINGLE_CONVERTIONS(state) {
      state.singleCovertions = null
    },
    SET_SELECTED_MESSAGE_ID(state, payload) {
      state.selectedMessageID = payload
    },
    SET_SELECTED_MESSAGE_ID_INDIVIDUAL(state, payload) {
      state.selectedMessageIDIndividual = payload
    },
    SET_STOP_SCROLL_POSITION(state, payload) {
      state.stopSetScrollPosition = payload
    },
    SET_PAGE_ID(state, payload) {
      state.pageId = payload
    },
    SET_PARTICIPANT(state, payload) {
      state.participant = payload
    },
    SET_CONVERSATION_USER(state, payload) {
      state.person = payload
      if (
        payload.participants !== null &&
        state.currentSocialComponent.provider === 'Facebook'
      ) {
        state.conversationOwner = payload.participants[1]
        state.conversationUser = payload.participants[0]
      } else if (
        payload.participants !== null &&
        state.currentSocialComponent.provider === 'Instagram'
      ) {
        state.conversationOwner = payload.participants[0]
        state.conversationUser = payload.participants[1]
        state.conversationUser.name = payload.name
      } else {
        state.conversationOwner = null
        state.conversationUser = null
      }
    },
    SET_SEE_MORE_MESSAGES(state, payload) {
      state.newLoadMessages = payload
      payload.forEach((item) => {
        state.singleCovertions.messages.unshift(item)
      })
    },
    SET_SEE_MORE_MESSAGES_BOTTOM(state, payload) {
      const bottomLoading = JSON.parse(JSON.stringify(payload))
      bottomLoading.reverse().forEach((item) => {
        state.singleCovertions.messages.push(item)
      })
    },
    RESET_LOAD_MORE_MESSAGES(state) {
      state.newLoadMessages = [1]
    },
    SET_MESSAGE_COUNT(state, payload) {
      state.messageCount = payload
    },
    SET_SHOW_OPTIONS(state, payload) {
      state.showOptionsOverAll = payload
    },
    SET_SEARCH_TEXT(state, payload) {
      state.searchText = payload
    },
    SET_FEEDS_DROPDOWN(state, payload) {
      state.feedsDropdown = payload
    },
    SET_OLDEST_JOINING_DATE_TIME(state, payload) {
      state.getOldJoiningDateTime = payload
    },
    STOP_SCROLL_EVENT(state, payload) {
      state.stopScrollEvent = payload
    },
    SET_WEB_HTML(state, payload) {
      state.websiteArticle = payload
    },
    SHOW_REDDIT_BOTTOM_MENU(state, payload) {
      const temp = JSON.parse(JSON.stringify(state.articles))
      temp.items.forEach((item) => {
        if (item.id === payload) {
          item.selected = !item.selected
        } else {
          item.selected = false
        }
      })
      temp.pinnedItems.forEach((item) => {
        if (item.id === payload) {
          item.selected = !item.selected
        } else {
          item.selected = false
        }
      })
      state.articles = temp
    },
    SET_EMAIL_BODY(state, payload) {
      state.tempSingleMessage.forEach((article) => {
        if (article.id === payload.items[0].id) {
          article.body = payload.items[0].body
        }
      })
      state.articles.items.forEach((item) => {
        if (item.id === payload.items[0].id) {
          item.body = payload.items[0].body
        }
      })
    },
    SET_ESTIMATION_COST(state, payload) {
      state.estimationCost = payload
    },
    SET_START_END_DATE(state, payload) {
      state.date.startDate = payload.startDate
      state.date.endDate = payload.endDate
    },
    RESET_START_END_DATE(state, payload) {
      state.date.startDate = ''
      state.date.endDate = ''
    },
    CLEAR_SET_TEMP_EMAIL_BODY(state, payload) {
      state.tempEmailBody = ''
      state.tempEmailTitle = ''
    },
    SET_TEMP_EMAIL_BODY(state, payload) {
      if (typeof payload.body === 'string') {
        state.tempEmailBody = payload.body
        state.tempEmailTitle = payload.title
      } else {
        state.tempEmailBody = payload.body.join('')
        state.tempEmailTitle = payload.title
      }
    },
    SHOW_PDF_POPUP(state, payload) {
      state.showPdfPupUp = payload
    },
  },

  actions: {
    async getWebSearchResult({ commit }, payload) {
      if (payload.search && payload.archiveDate) {
        try {
          this.webSearching = true
          const res = await fetch(WEB_SEARCH, {
            method: 'POST',
            body: payload,
          })
          if (res.success) {
            useNuxtApp().$toast('clear')
            if (res.results.length > 0) {
              useNuxtApp().$toast('success', {
                message: 'Successfully Loaded',
                className: 'toasted-bg-archive',
              })
            } else {
              useNuxtApp().$toast('success', {
                message: 'No data found',
                className: 'toasted-bg-alert',
              })
            }
            commit('SET_CURRENT_HEADER', 'LatestArchive')
            this.webSearching = false
            commit('SET_WEB_SEARCH_RESULT', res.results)
          } else {
            this.webSearching = false
          }
        } catch (error) {
          this.webSearching = false
          console.log(error)
        }
      } else if (!payload.archiveDate) {
        useNuxtApp().$toast('clear')
        useNuxtApp().$toast('error', {
          message: 'The Archive Dates field is required',
          className: 'toasted-bg-alert',
        })
      }
    },
    async getAllLatestData({ commit }) {
      try {
        const response = await fetch(LATEST_ARCHIVE)
        commit('SET_ALL_LATEST_DATA', response.data)
      } catch (err) {
        console.log(err)
      }
    },
    async getAllSocialArticle({ commit, state }, payload) {
      commit('SET_LOADING_ARTICLE', true)
      try {
        const response = await fetch(SOCIAL_ARTICLE, {
          params:
            state.currentTab === 'Incoming' || state.currentTab === 'Outgoing'
              ? {
                  accountId: payload.id,
                  messageType: state.currentTab,
                }
              : state.currentSocialComponent.provider === 'Facebook' ||
                  state.currentSocialComponent.provider === 'YouTube'
                ? { accountId: payload.id, type: payload.type }
                : { accountId: payload.id },
        })
        if (response.success || response.status === 200) {
          commit('SET_LOADING_ARTICLE', false)
          commit('SET_LOADING_MORE_ARTICLE', true)
          commit('SET_ALL_SOCIAL_ARTICLE', response.data)
          if (response.data.provider === 'Web') {
            this.dispatch('home/getWebHtml', response.data)
            if (payload.archiveDate) {
              commit('SET_WEB_SEARCH_FROM_USER_INFO', {
                archiveDate: payload.archiveDate,
              })
            }
          }
          commit('SET_EXPAND_ALL', false)
        }
      } catch (err) {
        console.log(err)
        commit('SET_LOADING_ARTICLE', false)
      }
    },
    async getAllEmailArticle({ commit, state }, payload) {
      commit('SET_START_END_DATE', payload)
      commit('SET_LOADING_ARTICLE', true)
      try {
        const response = await fetch(SOCIAL_ARTICLE, {
          params:
            state.currentTab === 'Incoming' || state.currentTab === 'Outgoing'
              ? {
                  accountId: payload.accountId,
                  messageType: state.currentTab,
                  startDate: state.date.startDate,
                  endDate: state.date.endDate,
                }
              : state.currentSocialComponent.provider === 'Facebook' ||
                  state.currentSocialComponent.provider === 'YouTube'
                ? {
                    accountId: payload.accountId,
                    startDate: state.date.startDate,
                    endDate: state.date.endDate,
                    type: state.facebookCurrentTab,
                  }
                : {
                    accountId: payload.accountId,
                    startDate: state.date.startDate,
                    endDate: state.date.endDate,
                  },
        })
        if (response.success || response.status === 200) {
          commit('SET_LOADING_ARTICLE', false)
          commit('SET_LOADING_MORE_ARTICLE', true)
          commit('SET_ALL_SOCIAL_ARTICLE', response.data)
          if (response.data.provider === 'Web') {
            this.dispatch('home/getWebHtml', response.data)
          }
          commit('SET_EXPAND_ALL', false)
        }
      } catch (err) {
        console.log(err)
        commit('SET_LOADING_ARTICLE', false)
      }
    },
    async getCurrentSocialArticle({ commit, state }, payload) {
      try {
        const response = await fetch(SOCIAL_ARTICLE, {
          params:
            state.currentTab === 'Incoming' || state.currentTab === 'Outgoing'
              ? {
                  accountId: payload.accountId,
                  messageType: state.currentTab,
                  id: payload.id,
                }
              : {
                  accountId: payload.accountId,
                  id: payload.id,
                },
        })
        if (response.success || response.status === 200) {
          commit('SET_EMAIL_BODY', response.data)
        }
      } catch (err) {
        console.log(err)
      }
    },
    async loadMoreArticles({ commit, state, getters }) {
      useNuxtApp().$toast('clear')
      const accountId1 = state.currentSocialComponent.id
      const lastItemId1 = state.articles.items
        ? state.articles.items[state.articles.items.length - 1].id
        : ''
      commit('SET_LOADING_MORE_ARTICLE_SKELATON', true)
      try {
        const { status, success, data } = await fetch(
          SOCIAL_ARTICLE_LOAD_MORE,
          {
            params:
              state.currentTab === 'Incoming' || state.currentTab === 'Outgoing'
                ? {
                    accountId: accountId1,
                    lastItemId: lastItemId1,
                    messageType: state.currentTab,
                  }
                : state.currentSocialComponent.provider === 'Facebook' ||
                    state.currentSocialComponent.provider === 'YouTube'
                  ? {
                      accountId: accountId1,
                      lastItemId: lastItemId1,
                      type: state.facebookCurrentTab,
                    }
                  : { accountId: accountId1, lastItemId: lastItemId1 },
          },
        )
        if (success || status === 200) {
          if (data.items.length > 0) {
            commit('SET_LOAD_MORE_ARTICLES', data)
          } else if (data.items.length === 0) {
            commit('SET_LOADING_MORE_ARTICLE_SKELATON', false)
            commit('SET_LOADING_MORE_ARTICLE', false)
            if (!state.loadMoreArticlesSkelaton) {
              if (
                state.currentSocialComponent.provider === 'Facebook' ||
                state.currentSocialComponent.provider === 'LinkedIn' ||
                state.currentSocialComponent.provider === 'Instagram' ||
                state.currentSocialComponent.provider === 'Twitter'
              ) {
                useNuxtApp().$toast('success', {
                  message: 'No More Posts',
                  className: 'toasted-bg-archive',
                })
              } else if (
                state.currentSocialComponent.provider === 'Google' ||
                state.currentSocialComponent.provider === 'Microsoft'
              ) {
                useNuxtApp().$toast('success', {
                  message: 'No More Emails',
                  className: 'toasted-bg-archive',
                })
              } else if (state.currentSocialComponent.provider === 'YouTube') {
                useNuxtApp().$toast('success', {
                  message: 'No More Videos',
                  className: 'toasted-bg-archive',
                })
              } else {
                useNuxtApp().$toast('success', {
                  message: 'No More Data',
                  className: 'toasted-bg-archive',
                })
              }
            }
          }
        }
      } catch (error) {
        console.log(error)
      } finally {
        commit('SET_LOADING_MORE_ARTICLE_SKELATON', false)
      }
    },
    async loadMoreEmailArticles({ commit, state, getters }) {
      useNuxtApp().$toast('clear')
      const accountId1 = state.currentSocialComponent.id
      const lastItemId1 = state.articles.items
        ? state.articles.items[state.articles.items.length - 1].id
        : ''
      commit('SET_LOADING_MORE_ARTICLE_SKELATON', true)
      try {
        const { status, success, data } = await fetch(
          SOCIAL_ARTICLE_LOAD_MORE,
          {
            params:
              state.currentTab === 'Incoming' || state.currentTab === 'Outgoing'
                ? {
                    accountId: accountId1,
                    lastItemId: lastItemId1,
                    messageType: state.currentTab,
                    startDate: state.date.startDate,
                    endDate: state.date.endDate,
                  }
                : state.currentSocialComponent.provider === 'Facebook' ||
                    state.currentSocialComponent.provider === 'YouTube'
                  ? {
                      accountId: accountId1,
                      lastItemId: lastItemId1,
                      startDate: state.date.startDate,
                      endDate: state.date.endDate,
                      type: state.facebookCurrentTab,
                    }
                  : {
                      accountId: accountId1,
                      lastItemId: lastItemId1,
                      startDate: state.date.startDate,
                      endDate: state.date.endDate,
                    },
          },
        )
        if (success || status === 200) {
          if (data.items.length > 0) {
            commit('SET_LOAD_MORE_ARTICLES', data)
          } else if (data.items.length === 0) {
            commit('SET_LOADING_MORE_ARTICLE_SKELATON', false)
            commit('SET_LOADING_MORE_ARTICLE', false)
            if (!state.loadMoreArticlesSkelaton) {
              if (
                state.currentSocialComponent.provider === 'Facebook' ||
                state.currentSocialComponent.provider === 'LinkedIn' ||
                state.currentSocialComponent.provider === 'Instagram' ||
                state.currentSocialComponent.provider === 'Twitter'
              ) {
                useNuxtApp().$toast('success', {
                  message: 'No More Posts',
                  className: 'toasted-bg-archive',
                })
              } else if (
                state.currentSocialComponent.provider === 'Google' ||
                state.currentSocialComponent.provider === 'Microsoft'
              ) {
                useNuxtApp().$toast('success', {
                  message: 'No More Emails',
                  className: 'toasted-bg-archive',
                })
              } else if (state.currentSocialComponent.provider === 'YouTube') {
                useNuxtApp().$toast('success', {
                  message: 'No More Videos',
                  className: 'toasted-bg-archive',
                })
              } else {
                useNuxtApp().$toast('success', {
                  message: 'No More Data',
                  className: 'toasted-bg-archive',
                })
              }
            }
          }
        }
      } catch (error) {
        console.log(error)
      } finally {
        commit('SET_LOADING_MORE_ARTICLE_SKELATON', false)
      }
    },
    async getAllMessagePersons({ commit }, payload) {
      commit('SET_PERSON_SKELETON', true)
      try {
        const response = await fetch(ALL_MESSAGE_PERSONS, {
          params: {
            accountId: payload,
          },
        })
        if (response.success || response.status === 200) {
          commit('SET_PERSON_SKELETON', false)
          commit('SET_ALL_MESSAGE_PERSONS', response.data)
          if (response.data.items.length > 0) {
            this.dispatch('home/getSingleConvertions', {
              provider: response.data.provider,
              person: response.data.items[0],
            })
          } else {
            commit('SET_PERSON_SKELETON', false)
            commit('SET_SINGLE_CONVERTIONS', null)
            commit('SET_CONVERSATION_USER', { participants: null })
          }
        }
      } catch (err) {
        console.log(err)
      }
    },
    async getSingleConvertions({ commit, state }, payload) {
      if (payload.person.messageCount) {
        commit('SET_MESSAGE_COUNT', payload.person.messageCount)
      }
      commit('SET_MESSAGE_SKELETON', true)
      if (payload.person.participants) {
        commit('SET_CONVERSATION_USER', payload.person)
      }
      if (payload.person.pageId) {
        commit('SET_PAGE_ID', payload.person.pageId)
      }
      try {
        if (payload.messageID) {
          const response = await fetch(SINGLE_CONVERTIONS, {
            params: {
              provider: payload.provider,
              chatId: payload.person.id,
              messageId: payload.messageID,
            },
          })
          if (response.success || response.status === 200) {
            if (payload.fromSearch && state.showSearchMessageFromSearch) {
              const providerFromSearch = {
                provider: payload.provider,
                from: 'fromSearch',
              }
              commit('SET_CURRENT_SOCIAL_COMPONENT', providerFromSearch)
              commit('SET_MESSAGE_COUNT', response.data.messageCount)
              commit('SET_CONVERSATION_USER', response.data)
              commit('SET_PAGE_ID', response.data.pageId)
            }
            commit('SET_MESSAGE_SKELETON', false)
            commit('SET_SELECTED_MESSAGE_ID_INDIVIDUAL', payload.messageID)
            commit('SET_SINGLE_CONVERTIONS', response.data)
            commit('RESET_LOAD_MORE_MESSAGES')
            this.dispatch('home/getMoreMessages', {
              provider: payload.provider,
              chatId: response.data.id,
              lastMessageId:
                response.data.messages[response.data.messages.length - 1].id,
              isFromSearch: payload.fromSearch,
            })
            setTimeout(() => {
              if (!state.stopSetScrollPosition) {
                // when loading data set the scroll position on selected message
                if (payload.fromSearch && state.showSearchMessageFromSearch) {
                  if (document.getElementById('message_Wrapper')) {
                    document.getElementById('message_Wrapper').scrollTop =
                      document.getElementById(state.selectedMessageIDIndividual)
                        .offsetTop - 200
                  }
                } else if (
                  state.currentMessage === 'HomeMessageFeedAllMessagePerson'
                ) {
                  if (document.getElementById('message_wrapper')) {
                    document.getElementById('message_wrapper').scrollTop =
                      document.getElementById(state.selectedMessageIDIndividual)
                        .offsetTop - 200
                  }
                } else if (
                  state.currentMessage !== 'HomeMessageFeedAllMessagePerson'
                ) {
                  if (document.getElementById('message_Wrapper')) {
                    document.getElementById('message_Wrapper').scrollTop =
                      document.getElementById(state.selectedMessageIDIndividual)
                        .offsetTop - 100
                  }
                }
              }
            }, 400)
          }
        } else {
          const response = await fetch(SINGLE_CONVERTIONS, {
            params: {
              provider: payload.provider,
              chatId: payload.person.id,
            },
          })
          if (response.success || response.status === 200) {
            commit('SET_MESSAGE_SKELETON', false)
            commit('SET_SINGLE_CONVERTIONS', response.data)
            commit('RESET_LOAD_MORE_MESSAGES')
          }
        }
      } catch (err) {
        console.log(err)
      }
    },
    async getMoreMessages({ commit, state }, payload) {
      try {
        if (payload.lastMessageId) {
          const response = await fetch(GET_MORE_MESSAGES, {
            params: {
              provider: payload.provider,
              chatId: payload.chatId,
              lastItemId: payload.lastMessageId,
            },
          })
          if (response.success || response.status === 200) {
            commit('SET_MORE_MESSAGE_SKELETON', false)
            commit('SET_SEE_MORE_MESSAGES_BOTTOM', response.data)
            if (response.data.length === 0) {
              commit('SET_MORE_MESSAGE_SKELETON', false)
              commit('STOP_SCROLL_EVENT', false)
            }
            // recursive the function till the last hitting of api response message data length are zero
            if (response.data.length > 0) {
              this.dispatch('home/getMoreMessages', {
                provider: payload.provider,
                chatId: payload.chatId,
                lastMessageId: response.data[0].id,
              })
              setTimeout(() => {
                if (!state.stopSetScrollPosition) {
                  // when loading data set the scroll position on selected message
                  if (
                    payload.isFromSearch &&
                    state.showSearchMessageFromSearch
                  ) {
                    if (document.getElementById('message_Wrapper')) {
                      document.getElementById('message_Wrapper').scrollTop =
                        document.getElementById(
                          state.selectedMessageIDIndividual,
                        ).offsetTop - 200
                    }
                  } else if (
                    state.currentMessage === 'HomeMessageFeedAllMessagePerson'
                  ) {
                    if (document.getElementById('message_wrapper')) {
                      document.getElementById('message_wrapper').scrollTop =
                        document.getElementById(
                          state.selectedMessageIDIndividual,
                        ).offsetTop - 200
                    }
                  } else if (
                    state.currentMessage !== 'HomeMessageFeedAllMessagePerson'
                  ) {
                    if (document.getElementById('message_Wrapper')) {
                      document.getElementById('message_Wrapper').scrollTop =
                        document.getElementById(
                          state.selectedMessageIDIndividual,
                        ).offsetTop - 100
                    }
                  }
                }
              }, 50)
            }
          }
        } else {
          const response = await fetch(GET_MORE_MESSAGES, {
            params: {
              provider: payload.provider,
              chatId: payload.chatId,
              firstItemId: payload.id,
            },
          })
          if (response.success || response.status === 200) {
            commit('SET_MORE_MESSAGE_SKELETON', false)
            commit('SET_SEE_MORE_MESSAGES', response.data)
            if (response.data.length === 0) {
              useNuxtApp().$toast('clear')
              commit('SET_MORE_MESSAGE_SKELETON', false)
              setTimeout(() => {
                useNuxtApp().$toast('success', {
                  message: 'No more message to be loaded',
                  className: 'toasted-bg-archive',
                })
              }, 50)
            }
          }
        }
      } catch (err) {
        console.log(err)
      }
    },
    async getSearchPerson({ commit, state }, payload) {
      try {
        const response = await fetch(ALL_MESSAGE_PERSONS, {
          params: {
            accountId: state.currentSocialComponent.id,
            searchText: payload,
          },
        })
        if (response.success || response.status === 200) {
          commit('SET_ALL_MESSAGE_PERSONS', response.data)
        }
      } catch (err) {
        console.log(err)
      }
    },
    async getSearchMessage({ commit, state }, payload) {
      try {
        const response = await fetch(SINGLE_CONVERTIONS, {
          params: {
            provider: state.currentSocialComponent.provider,
            chatId: state.person.id,
            searchText: payload,
          },
        })
        if (response.success) {
          commit('SET_SINGLE_CONVERTIONS', response.data)
        }
      } catch (err) {
        console.log(err)
      }
    },

    // get message and people by search keyword --> full message feed
    async getSearchPersonAndMessage({ commit, state }, payload) {
      commit('SET_PERSON_SKELETON', true)
      try {
        const response = await fetch(ALL_MESSAGE_PERSONS, {
          method: 'POST',
          body: {
            accountId: state.currentSocialComponent.id,
            searchText: payload,
          },
        })
        if (response.success || response.status === 200) {
          if (document.getElementById('leftColumn')) {
            document.getElementById('leftColumn').scrollTop = 0
          }
          commit('SET_PERSON_SKELETON', false)
          const fullResponsePeople = {
            response: response.data.people,
            searchText: payload,
          }
          commit('SET_SEARCH_PERSON', fullResponsePeople)
          const fullResponseMessage = {
            response: response.data.messages,
            searchText: payload,
          }
          commit('SET_SEARCH_MESSAGE', fullResponseMessage)
        }
      } catch (err) {
        console.log(err)
      }
    },
    async getYoutubePlaylist({ commit, state }, payload) {
      try {
        const response = await fetch(YOUTUBE_PLAYLIST, {
          params: {
            accountId: payload,
          },
        })
        if (response.success || response.status === 200) {
          commit('SET_YOUTUBE_PLAYLIST', response.data)
          if (response.data.playlists.length > 0) {
            this.dispatch('home/getYoutubePlaylistItem', {
              provider: response.data.provider,
              playlist: response.data.playlists[0],
            })
          }
        }
      } catch (err) {
        console.log(err)
      }
    },
    async getYoutubePlaylistItem({ commit, state }, payload) {
      const accountId1 = state.currentSocialComponent.id
      const lastItemId1 =
        state.articles.items && state.articles.items.length > 0
          ? state.articles.items[state.articles.items.length - 1].id
          : ''
      let moreArticles = state.articles.items
        ? JSON.parse(JSON.stringify(state.articles.items))
        : ''
      try {
        const { status, success, data } = await fetch(
          SOCIAL_ARTICLE_LOAD_MORE,
          {
            params: {
              accountId: accountId1,
              lastItemId: lastItemId1,
            },
          },
        )
        if (success || status === 200) {
          if (data.items.length > 0) {
            data.items.forEach((item) => {
              moreArticles.push(item)
            })
          }
        }
      } catch (error) {
        console.log(error)
      }
      commit('SET_VIDEO_DURATION', moreArticles)
      moreArticles = state.tempArticles
      try {
        const response = await fetch(YOUTUBE_PLAYLIST_ITEM, {
          params: {
            provider: payload.provider,
            playlistId: payload.playlist.id,
          },
        })
        if (response.success || response.status === 200) {
          // response.data.items = response.data.items.reverse()
          response.data.items.forEach((item) => {
            if (moreArticles) {
              moreArticles.forEach((value) => {
                if (value.transcript && typeof value.transcript === 'string') {
                  const lines = value.transcript.trim().split('\n')
                  const newArray = []
                  for (let i = 0; i < lines.length; i++) {
                    // Ensure the element is a string and contains a comma
                    if (
                      typeof lines[i] === 'string' &&
                      lines[i].includes(',')
                    ) {
                      const timeParts = lines[i].split(',')[0] // Extract start time
                      const text =
                        lines[i + 1] && typeof lines[i + 1] === 'string'
                          ? lines[i + 1].trim()
                          : '' // Safely access next element
                      if (text) {
                        // Convert time to MM:SS format (ignore milliseconds)
                        const timeSegments = timeParts.split(':')
                        if (timeSegments.length === 3) {
                          const hours = parseInt(timeSegments[0], 10) // Convert hours to integer to remove leading zero
                          const minutes = parseInt(timeSegments[1], 10) // Convert minutes to integer to remove leading zero
                          const seconds = timeSegments[2].split('.')[0] // Extract seconds
                          const formattedTime =
                            hours > 0
                              ? `${hours}:${minutes}:${seconds}`
                              : `${minutes}:${seconds}`
                          newArray.push({
                            start: formattedTime,
                            text: text,
                          })
                        }
                      }
                    }
                  }
                  value.transcript = newArray
                }
                if (item.videoId === value.socialId) {
                  item.embedHtml = value.embedHtml
                  item.fullDuration = value.fullDuration
                  item.commentCount = value.commentCount
                  item.dislikeCount = value.dislikeCount
                  item.likeCount = value.likeCount
                  item.viewCount = value.viewCount
                  item.mainVideoId = value.id
                  item.shortDuration = value.shortDuration
                  item.privacy = value.privacy
                  item.thumbnail = value.thumbnail
                  item.title = value.title
                  item.description = value.description
                  item.transcript = value.transcript
                }
              })
            }
          })
          commit('SET_YOUTUBE_PLAYLIST_ITEMS', response.data)
          if (!state.selectedPlayList && response.data) {
            setTimeout(() => {
              commit('SET_SELECTED_PlayList', true)
            })
          }
        }
      } catch (err) {
        console.log(err)
      }
    },
    async loadMorePlaylistItems({ commit, state }, payload) {
      const accountId1 = state.currentSocialComponent.id
      const lastItemId1 =
        state.articles.items[state.articles.items.length - 1].id
      let moreArticles = JSON.parse(JSON.stringify(state.articles.items))
      try {
        const { status, success, data } = await fetch(
          SOCIAL_ARTICLE_LOAD_MORE,
          {
            params: {
              accountId: accountId1,
              lastItemId: lastItemId1,
            },
          },
        )
        if (success || status === 200) {
          if (data.items.length > 0) {
            data.items.forEach((item) => {
              moreArticles.push(item)
            })
          }
        }
      } catch (error) {
        console.log(error)
      }
      commit('SET_VIDEO_DURATION', moreArticles)
      moreArticles = state.tempArticles
      const lastPlayListItemId =
        state.youtubePlayListItems.items[
          state.youtubePlayListItems.items.length - 1
        ].id
      try {
        const { status, success, data } = await fetch(
          YOUTUBE_PLAYLIST_ITEM_MORE,
          {
            params: {
              provider: state.currentSocialComponent.provider,
              playlistId: state.youtubePlayListItems.id,
              lastItemId: lastPlayListItemId,
            },
          },
        )
        if (success || status === 200) {
          useNuxtApp().$toast('clear')
          if (data.length > 0) {
            data.forEach((item) => {
              moreArticles.forEach((value) => {
                if (item.videoId === value.socialId) {
                  item.embedHtml = value.embedHtml
                  item.fullDuration = value.fullDuration
                  item.commentCount = value.commentCount
                  item.dislikeCount = value.dislikeCount
                  item.likeCount = value.likeCount
                  item.viewCount = value.viewCount
                  item.mainVideoId = value.id
                  item.shortDuration = value.shortDuration
                  item.privacy = value.privacy
                  item.thumbnail = value.thumbnail
                  item.title = value.title
                  item.description = value.description
                  item.transcript = value.transcript
                }
              })
            })
            commit('SET_MORE_YOUTUBE_PLAYLIST_ITEMS', data)
          } else {
            commit('SET_LOADING_MORE_PLAYLIST_VIDEO', false)
            useNuxtApp().$toast('success', {
              message: 'No More Videos',
              className: 'toasted-bg-archive',
            })
          }
        }
      } catch (error) {
        console.log(error)
      }
    },
    getHeight({ commit }, payload) {
      commit('SET_HEIGHT', payload)
    },
    getMobileHeight({ commit }, payload) {
      commit('SET_MOBILE_HEIGHT', payload)
    },
    setTwitterComments({ commit, state }, payload) {
      const words = {}
      payload.data.forEach((item) => {
        if (item.entities.urls !== undefined) {
          item.entities.urls.forEach((value) => {
            if (item.text.includes(value.url)) {
              if ('value.url' in words === false) {
                words[value.url] =
                  '<a rel="noopener noreferrer" class="text-blue-600" href="' +
                  value.url +
                  '" target="_blank">' +
                  value.display_url +
                  '</a>'
              }
            }
          })
          Object.keys(words).forEach((key) => {
            item.text = item.text.replaceAll(key, words[key])
          })
        }
        return item.text
      })
      const mainReplies = []
      payload.data.forEach((item) => {
        if (
          item.referencedTweetsId === payload.socialId ||
          (item.referencedTweetsId === payload.referencedTweetsId &&
            payload.referencedTweetsType === 'retweeted')
        ) {
          mainReplies.push(item)
        }
      })
      this.dispatch('home/setTwitterReplies', {
        mainReplies,
        data: payload.data,
      })
    },
    setTwitterReplies({ commit }, payload) {
      payload.mainReplies.forEach((item) => {
        item.replies = []
        payload.data.forEach((value) => {
          if (item.socialId === value.referencedTweetsId) {
            item.replies.push(value)
          }
        })
      })
      payload.mainReplies.forEach((item) => {
        if (item.replyCount > 0) {
          this.dispatch('home/setTwitterReplies', {
            mainReplies: item.replies,
            data: payload.data,
          })
        }
      })
      commit('SET_TWITTER_COMMENT', payload.mainReplies)
    },
    setTwitterPreviousComment({ commit }, payload) {
      commit('SET_PREVIOUS_TWITTER_COMMENT', payload.comments)
      commit('SET_TWITTER_COMMENT', payload.replies)
    },
    async getOldestJoiningDateTime({ commit }, payload) {
      try {
        const data = await fetch(SOCIAL_EARLIEST_DATE)
        if (data.success) {
          commit('SET_OLDEST_JOINING_DATE_TIME', data.date)
        }
      } catch (error) {
        console.log(error)
      }
    },
    async getWebHtml({ commit }, payload) {
      const { tokenCookie } = useAuth()
      if (payload.items && payload.items.length > 0) {
        try {
          const response = await fetch(
            `${WEBSITE_HTML}${payload.id}/${payload.items[0].archiveDate}/${payload.username}`,
          )
          // const response = await useFetch(
          //   `api/web/${payload.id}/${payload.items[0].archiveDate}/${payload.username}`,
          //   {
          //     headers: {
          //       Authorization: `Bearer ${tokenCookie.value}`,
          //       'X-CSRF-Token': tokenCookie.value,
          //       'Content-Type': 'application/json',
          //       'Cache-Control': 'no-cache',
          //       Accept: 'application/json',
          //     },
          //   },
          // )
          // if (response.data.value) {
          //   commit('SET_WEB_HTML', response.data.value)
          // }
          if (response) {
            commit('SET_WEB_HTML', response)
          }
        } catch (error) {
          console.log(error)
        }
      }
    },
    async getAllEmailBody({ commit, state }, payload) {
      const tempHtml = []
      const title = state.tempSingleMessage[0].subject
      const singleMessages = JSON.parse(JSON.stringify(state.tempSingleMessage))
      this.commit('archive/SET_DOWNLOAD_LOADER', true)
      for (const item of singleMessages) {
        if (!item.body) {
          try {
            const response = await fetch(SOCIAL_ARTICLE, {
              params:
                state.currentTab === 'Incoming' ||
                state.currentTab === 'Outgoing'
                  ? {
                      accountId: payload,
                      messageType: state.currentTab,
                      id: item.id,
                    }
                  : {
                      accountId: payload,
                      id: item.id,
                    },
            })
            if (response.success || response.status === 200) {
              if (item.updatedAt) {
                const date = new Date(item.updatedAt)
                const formattedDate = date.toLocaleString('en-US', {
                  year: 'numeric',
                  month: 'long',
                  day: '2-digit',
                  hour: '2-digit',
                  minute: '2-digit',
                  second: '2-digit',
                  hour12: false, // Use 'true' for 12-hour format with AM/PM
                })
                item.updatedAt = formattedDate
              }
              if (item.to && item.to.length > 0) {
                item.to = item.to
                  .map((email) =>
                    email.replace(/</g, '&lt;').replace(/>/g, '&gt;'),
                  )
                  .join(', ')
              }
              if (item.sentAt) {
                const date = new Date(item.sentAt)
                const formattedDate = date.toLocaleString('en-US', {
                  year: 'numeric',
                  month: 'long',
                  day: '2-digit',
                  hour: '2-digit',
                  minute: '2-digit',
                  second: '2-digit',
                  hour12: false, // Use 'true' for 12-hour format with AM/PM
                })
                item.sentAt = formattedDate
              }
              if (item.toEmails && item.toEmails.length > 0) {
                item.toEmails = item.toEmails
                  .map((item) => item.emailAddress.address)
                  .join(', ')
              }
              if (item.ccRecipients && item.ccRecipients.length > 0) {
                item.ccRecipients = item.ccRecipients
                  .map((item) => item.emailAddress.address)
                  .join(', ')
              }
              if (item.bccRecipients && item.bccRecipients.length > 0) {
                item.bccRecipients = item.bccRecipients
                  .map((item) => item.emailAddress.address)
                  .join(', ')
              }
              const htmlContent = `
              <table style="width:100%; border-collapse: collapse;">
                <tr>
                  <td colspan="2" style="padding: 10px; ">
                    <table style="width:100%;">
                      <tr>
                        <td style="width:70%;">
                          <p style="margin: 0;">
                            <strong>Subject:</strong> ${item.subject}
                          </p>
                        </td>
                        <td style="text-align: right; width:30%; color: #666;">
                          <p style="margin: 0;">
                            ${item.updatedAt ? item.updatedAt : item.sentAt ? item.sentAt : ''}
                          </p>
                        </td>
                      </tr>
                    </table>
                  </td>
                </tr>
                <tr>
                  <td colspan="2" style="padding: 10px; ">
                    <p style="margin: 0;"><strong>From:</strong> ${
                      item.from
                        ? item.from
                        : item.fromEmail.name
                          ? item.fromEmail.name
                          : ''
                    } ${
                      item.email
                        ? item.email
                        : item.fromEmail.address
                          ? `(${item.fromEmail.address})`
                          : ''
                    }</p>
                  </td>
                </tr>
                <tr>
                  <td colspan="2" style="padding: 10px; ">
                    <p style="margin: 0;"><strong>To:</strong> ${item.to ? item.to : item.toEmails}</p>
                  </td>
                </tr>
                ${
                  item.ccRecipients
                    ? `
                <tr>
                  <td colspan="2" style="padding: 10px; ">
                    <p style="margin: 0;"><strong>Cc:</strong> ${item.ccRecipients}</p>
                  </td>
                </tr>
                `
                    : ''
                }
                ${
                  item.bccRecipients
                    ? `
                <tr>
                  <td colspan="2" style="padding: 10px; ">
                    <p style="margin: 0;"><strong>Bcc:</strong> ${item.bccRecipients}</p>
                  </td>
                </tr>
                `
                    : ''
                }
                <tr>
                  <td colspan="2" style="padding: 10px; ">
                    ${response.data.items[0].body}
                  </td>
                </tr>
            </table>
            <hr>
              `
              tempHtml.push(htmlContent)
            }
          } catch (error) {
            console.error(error)
          }
        } else {
          if (item.updatedAt) {
            const date = new Date(item.updatedAt)
            const formattedDate = date.toLocaleString('en-US', {
              year: 'numeric',
              month: 'long',
              day: '2-digit',
              hour: '2-digit',
              minute: '2-digit',
              second: '2-digit',
              hour12: false, // Use 'true' for 12-hour format with AM/PM
            })
            item.updatedAt = formattedDate
          }
          if (item.to && item.to.length > 0) {
            // Join the array and escape special characters
            item.to = item.to
              .map((email) => email.replace(/</g, '&lt;').replace(/>/g, '&gt;'))
              .join(', ')
          }
          if (item.sentAt) {
            const date = new Date(item.sentAt)
            const formattedDate = date.toLocaleString('en-US', {
              year: 'numeric',
              month: 'long',
              day: '2-digit',
              hour: '2-digit',
              minute: '2-digit',
              second: '2-digit',
              hour12: false, // Use 'true' for 12-hour format with AM/PM
            })
            item.sentAt = formattedDate
          }
          if (item.toEmails && item.toEmails.length > 0) {
            item.toEmails = item.toEmails
              .map((item) => item.emailAddress.address)
              .join(', ')
          }
          const htmlContent = `
          <table style="width:100%; border-collapse: collapse;">
            <tr>
              <td colspan="2" style="padding: 10px; ">
                <table style="width:100%;">
                  <tr>
                    <td style="width:70%;">
                      <p style="margin: 0;">
                        <strong>Subject:</strong> ${item.subject}
                      </p>
                    </td>
                    <td style="text-align: right; width:30%; color: #666;">
                      <p style="margin: 0;">
                        ${item.updatedAt ? item.updatedAt : item.sentAt ? item.sentAt : ''}
                      </p>
                    </td>
                  </tr>
                </table>
              </td>
            </tr>
            <tr>
              <td colspan="2" style="padding: 10px; ">
                <p style="margin: 0;"><strong>From:</strong> ${
                  item.from
                    ? item.from
                    : item.fromEmail.name
                      ? item.fromEmail.name
                      : ''
                } ${
                  item.email
                    ? item.email
                    : item.fromEmail.address
                      ? `(${item.fromEmail.address})`
                      : ''
                }</p>
              </td>
            </tr>
            <tr>
              <td colspan="2" style="padding: 10px; ">
                <p style="margin: 0;"><strong>To:</strong> ${item.to ? item.to : item.toEmails}</p>
              </td>
            </tr>
            ${
              item.ccRecipients
                ? `
            <tr>
              <td colspan="2" style="padding: 10px; ">
                <p style="margin: 0;"><strong>Cc:</strong> ${item.ccRecipients}</p>
              </td>
            </tr>
            `
                : ''
            }
            ${
              item.bccRecipients
                ? `
            <tr>
              <td colspan="2" style="padding: 10px; ">
                <p style="margin: 0;"><strong>Bcc:</strong> ${item.bccRecipients}</p>
              </td>
            </tr>
            `
                : ''
            }
            <tr>
              <td colspan="2" style="padding: 10px; ">
                ${item.body}
              </td>
            </tr>
          </table>
          <hr>
`
          tempHtml.push(htmlContent)
        }
      }
      commit('SET_TEMP_EMAIL_BODY', { body: tempHtml, title: title })
      // commit('SHOW_PDF_POPUP', true)
      this.dispatch('home/startDownloadPDF')
    },
    async startDownloadPDF({ commit, state }, payload) {
      const vm = this
      const config = useRuntimeConfig()
      let newWindow
      // const randomNumber = Math.floor(
      //   100000 + Math.random() * 900000,
      // ).toString()
      // newWindow = window.open(
      //   `${config.public.siteUrl}/print/v1=${randomNumber}`,
      //   '_blank',
      // )
      // setTimeout(async () => {
      // newWindow.document.write(`
      //   <html>
      //     <head>
      //       <title>${state.tempEmailTitle}</title> <!-- This sets the title for the new tab -->
      //       <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
      //       <style>
      //         body {
      //           -webkit-print-color-adjust: exact;
      //           print-color-adjust: exact;
      //           padding: 20px;
      //         }
      //         table > tbody > tr > td[style="line-height:1.2;padding:0.01px 0.01px 12px"] > div {
      //           display: flex;
      //           flex-wrap: nowrap;
      //         }
      //         table > tbody > tr > td > div > font[face="arial, sans-serif"]{
      //           display: flex;
      //           flex-wrap: nowrap;
      //         }
      //       </style>
      //     </head>
      //     <body>
      //       <div class="flex flex-col space-y-6">
      //       ${state.tempEmailBody}
      //       </div>
      //     </body>
      //   </html>
      // `)
      // newWindow.document.close() // Necessary to complete loading of the document
      // newWindow.onload = function () {
      //   // alert(newWindow.document)
      //     vm.commit('archive/SET_DOWNLOAD_LOADER', false)
      //     // vm.commit('home/SHOW_PDF_POPUP', false)
      //     newWindow.print() // Automatically trigger the print dialog
      //   }
      // Set up the document content without using document.write
      // const doc = newWindow.document

      // Create the structure of the HTML in the new window
      const newContent = `
    <html>
      <head>
        <title>${state.tempEmailTitle}</title>
        <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
        <style>
          @import url('https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&display=swap');
          body {
            font-family: 'Roboto', sans-serif;
            -webkit-print-color-adjust: exact;
            print-color-adjust: exact;
            padding: 20px;
          }
          table > tbody > tr > td[style="line-height:1.2;padding:0.01px 0.01px 12px"] > div {
            display: flex;
            flex-wrap: nowrap;
          }
          table > tbody > tr > td > div > font[face="arial, sans-serif"] {
            display: flex;
            flex-wrap: nowrap;
          }
          table[style="width:65%;background-color:#fff"] > tbody > tr > td > h3 {
            margin-top: 18px;
            margin-bottom: 18px
          }
          table[style="width:65%;background-color:#fff"] > tbody > tr > td > p {
            margin-top: 18px;
            margin-bottom: 18px
          }
        </style>
      </head>
      <body>
        <div class="flex flex-col space-y-6">
          ${state.tempEmailBody}
        </div>
      </body>
    </html>
  `
      const response = await $fetch(
        'https://pdf-generator.sharparchive.com/upload-html',
        {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            html: [encodeURIComponent(newContent)],
          }),
        },
      )
      const { pdfKey } = response

      // Poll for the PDF to be ready
      let isReady = false
      let pdfUrl = ''
      const pollInterval = 5000

      while (!isReady) {
        const statusResponse = await $fetch(
          'https://pdf-generator.sharparchive.com/check-pdf',
          {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ pdfKey }),
          },
        )
        const { status, pdfUrl: url } = await statusResponse

        if (status === 'ready') {
          isReady = true
          pdfUrl = url
        } else {
          await new Promise((resolve) => setTimeout(resolve, pollInterval))
        }
      }

      // Download the PDF
      const pdfBlob = await $fetch(pdfUrl, { responseType: 'blob' })
      const downloadUrl = window.URL.createObjectURL(pdfBlob)
      const link = document.createElement('a')
      link.href = downloadUrl
      const finalEmailTitle = state.tempEmailTitle.replace(/\./g, '') // Removes all dots
      link.setAttribute('download', `${finalEmailTitle}.pdf`)
      document.body.appendChild(link)
      link.click()
      link.remove()
      await $fetch('https://pdf-generator.sharparchive.com/delete-pdf', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ pdfKey }),
      })
      this.commit('archive/SET_DOWNLOAD_LOADER', false)
      // this.deletePdf(pdfKey)
      // // Use innerHTML to inject the content directly
      // doc.documentElement.innerHTML = newContent

      // // Close the document to indicate content is fully loaded
      // newWindow.document.close()

      // // Ensure the window is focused and ready to print
      // newWindow.focus()

      // // Use a short delay to ensure rendering is complete before printing
      // setTimeout(() => {
      //   newWindow.print()
      // }, 500) // Adjust the delay time if needed
      // }, 5000)
    },
    async getEstimationCost({ commit }, payload) {
      try {
        const response = await $fetch(GET_ESTIMATED_COST, {
          method: 'GET',
        })
        if (response.success) {
          commit('SET_ESTIMATION_COST', response.data)
        }
      } catch (error) {
        console.log(error)
      }
    },
  },
}

export default homeStore
