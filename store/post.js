const postStore = {
  namespaced: true,
  state() {
    return {
      posts: [],
      postList: [],
      postEvents: [],
      isSearchExpanded: false,
      isPostPreviewModal: false,
      isPostDeleteModal: false,
    }
  },

  getters: {},

  mutations: {
    SET_POSTS(state, payload) {
      state.posts = payload
    },
    SET_POST_LIST(state, payload) {
      state.postList = payload
    },
    SET_POST_EVENTS(state, payload) {
      state.postEvents = payload
    },
    SET_IS_SEARCH_EXPANDED(state, payload) {
      state.isSearchExpanded = payload
    },
    SET_IS_POST_PREVIEW_MODAL(state, payload) {
      state.isPostPreviewModal = payload
    },
    SET_IS_POST_DELETE_MODAL(state, payload) {
      state.isPostDeleteModal = payload
    },
  },

  actions: {},
}

export default postStore
