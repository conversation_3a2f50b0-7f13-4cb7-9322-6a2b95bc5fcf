const insightsStore = {
  namespaced: true,
  state() {
    return {
      currentDateRange: '1 Year',
      startDate: '',
      endDate: '',
      currentHeaderTab: 'Cumulative',
    }
  },

  getters: {},

  mutations: {
    SET_CURRENT_DATE_RANGE(state, payload) {
      state.currentDateRange = payload
    },
    SET_START_DATE(state, payload) {
      state.startDate = payload
    },
    SET_END_DATE(state, payload) {
      state.endDate = payload
    },
    SET_CURRENT_HEADER_TAB(state, payload) {
      state.currentHeaderTab = payload
    },
  },

  actions: {},
}

export default insightsStore
