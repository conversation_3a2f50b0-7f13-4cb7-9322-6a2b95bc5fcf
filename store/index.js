// import { USER_PROFILE } from "~/constants/urls"

// import NEW_SYSTEM_ALERT from "./modules/new-system-alert";
const TOGGLE_LOGIN_MODAL = 'TOGGLE_LOGIN_MODAL'
const EXPAND_STARTER_MODAL = 'EXPAND_STARTER_MODAL'
const COLLAPSE_STARTER_MODAL = 'COLLAPSE_STARTER_MODAL'
const SHOW_SIGN_UP = 'SHOW_SIGN_UP'
const SET_STICKY = 'SET_STICKY'
const SET_HEADER_WIDTH = 'HEADER_WIDTH'
const MAXIMIZE_STARTER_MODAL = 'MAXIMIZE_STARTER_MODAL'
const STARTER_ACCOUNT_MAXIMIZED = 'STARTER_ACCOUNT_MAXIMIZED'
// const TOGGLE_SIDEBAR_MODAL = 'TOGGLE_SIDEBAR_MODAL'
const UPDATE_SETUP_CONTNET = 'UPDATE_SETUP_CONTNET'
const SET_STORAGE_FORMAT_SELECTED = 'SET_STORAGE_FORMAT_SELECTED'
const SET_STORAGE_FORM_SUBMIT = 'SET_STORAGE_FORM_SUBMIT'

const FORGET_PASSWORD_MODAL = 'FORGET_PASSWORD_MODAL'
const SET_EMAIL_VERIFY_MODAL = 'SET_EMAIL_VERIFY_MODAL'
const SET_GLOBAL_COLOR_PANEL = 'SET_GLOBAL_COLOR_PANEL'
const SET_PASSWORD_CONFIRM_EMAIL_VERIFY_MODAL =
  'SET_PASSWORD_CONFIRM_EMAIL_VERIFY_MODAL'

import { createStore } from 'vuex'
import accountStore from './account'
import agentBookStore from './agentBook'
import alertStore from './alert'
import archiveStore from './archive'
import authStore from './auth'
import browseStore from './browse'
import communityStore from './community'
import confirmStore from './confirm'
import createPostStore from './createPost'
import emailsStore from './emails'
import feedsStore from './feeds'
import feedsDropdownStore from './feedsDropdown'
import guestStore from './guest'
import mobileHeaderStore from './header'
import homeStore from './home'
import insightsStore from './insights'
import loginAnimationStore from './loginAnimation'
import notificationsStore from './notifications'
import postStore from './post'
import profileStore from './profile'
import searchStore from './search'
import settingStore from './setting'
import socialStore from './social'
import socialFeedStore from './socialFeed'
import systemStore from './system'
import userStore from './user'
import flagProblematic from '/images/landing/flagProblematic.webp'
import reduceCommunication from '/images/landing/reduceCommunication.webp'
import satisfyArchive from '/images/landing/satisfyArchive.webp'

export default createStore({
  state() {
    return {
      loginModal: false,
      starterExpanded: false,
      starterCollapse: false,
      showSignUp: false,
      isSticky: false,
      headerWidth: false,
      starterMaximized: false,
      starterAccountMaximized: false,
      // sidebarModal: false,
      setupContent: 'SetupStarterButton',
      isStorageFormatSelected: false,
      isStorageFormSubmit: false,
      socialProviders: [],
      socialProvidersInitial: [],
      pastMonths: [],
      savedSearches: [],
      archiveSettings: false,
      betaVersion: false,
      lockScreen: false,
      showForgetPasswordModal: false,
      emailVerifyModal: false,
      adminMail: 'mailto:<EMAIL>',
      globalColorPanel: {
        backgroundColor: '#e4801d',
      },
      landingLoader: true, // true
      defaultImg: `/images/default-avatar.jpg`,
      showSideBar: true,
      isShowSideBar: true,
      passwordConfirmEmailVerifyModal: false,
      currentPayMethod: 'Automatic',
      currentPaymentSystem: 'SetupAccountPaymentOption',
      socialFeeds: [
        {
          id: 'all_feeds',
          name: 'All Feeds',
          mediaValue: true,
          media: [
            {
              id: 'type',
              mediaName: 'Type',
              mediaValue: true,
              mediaHeight: false,
              showIndividualMediaType: false,
              feedHeight: 0,
              individualMediaType: [
                {
                  id: 'social_media',
                  value: 'social_media',
                  text: 'Social Media',
                  mediaValue: true,
                  individualMediaTypeHeight: false,
                  showIndividualMedia: false,
                  feedHeight: 0,
                  individualMedia: [],
                },
                {
                  id: 'email',
                  value: 'email',
                  text: 'Email',
                  mediaValue: true,
                  individualMediaTypeHeight: false,
                  showIndividualMedia: false,
                  feedHeight: 0,
                  individualMedia: [],
                },
                {
                  id: 'web',
                  value: 'web',
                  text: 'Websites',
                  mediaValue: true,
                  individualMediaTypeHeight: false,
                  showIndividualMedia: false,
                  feedHeight: 0,
                  individualMedia: [],
                },
              ],
            },
            {
              id: 'group',
              mediaName: 'Groups',
              mediaValue: true,
              mediaHeight: false,
              showIndividualMediaType: false,
              feedHeight: 0,
              individualMediaType: [],
            },
            {
              id: 'name',
              mediaName: 'Individual Accounts',
              mediaValue: true,
              mediaHeight: false,
              showIndividualMediaType: false,
              feedHeight: 0,
              individualMediaType: [],
            },
          ],
        },
      ],
      dataSearch: false,
      dataSaveSearch: false,
      showSubMenu: false,
      redictToLanding: false,
      showPricingPage: false,
      fromStartFreeTrial: false,
      showPartnerVerifyModal: false,
      existEmail: true,
      contactIconStore: true,
      keyFeatures: null,
      newFeatures: [
        {
          color: '#8DB230',
          image: satisfyArchive,
          title: 'Satisfy Archiving Requirements',
        },
        {
          color: '#7D80BD',
          image: flagProblematic,
          title: 'Flag Problematic Communication',
        },
        {
          color: '#A22A2A',
          image: reduceCommunication,
          title: 'Reduce Communication Risk',
        },
      ],
      cookiesShow: true,
    }
  },
  getters: {
    getStorageFormatSelected(state) {
      return state.isStorageFormatSelected
    },
    getForgetPasswordModal(state) {
      return state.showForgetPasswordModal
    },
    currentPayment(state) {
      return state.currentPaymentSystem
    },
    socialFeeds(state) {
      return state.socialFeeds
    },
    isShowPricingPage(state) {
      return state.showPricingPage
    },
  },

  mutations: {
    SET_KEY_FEATURES(state, payload) {
      state.keyFeatures = payload
      state.keyFeatures.features = state.newFeatures
    },
    SET_COOKIES_SHOW(state, payload) {
      state.cookiesShow = payload
    },
    SET_CONTACT_ICON(state, payload) {
      state.contactIconStore = payload
    },
    SET_EMAIL_EXIST(state, payload) {
      state.existEmail = payload
    },
    SET_SHOW_PARTNER_VERIFY_MODAL(state, payload) {
      state.showPartnerVerifyModal = payload
    },
    SET_FROM_START_FREE_TRIAL(state, payload) {
      state.fromStartFreeTrial = payload
    },
    SET_SHOW_PRICING_PAGE(state, payload) {
      state.showPricingPage = payload
    },
    SET_REDICT_TO_LANDING(state, payload) {
      state.redictToLanding = payload
    },
    SET_SHOW_SUB_MENU(state, payload) {
      state.showSubMenu = payload
    },
    SET_DATA_SEARCH(state, payload) {
      state.dataSearch = payload
    },
    SET_DATA_SAVE_SEARCH(state, payload) {
      state.dataSaveSearch = payload
    },
    [TOGGLE_LOGIN_MODAL](state, payload) {
      state.loginModal = payload
    },
    [SET_GLOBAL_COLOR_PANEL](state, payload) {
      state.globalColorPanel.backgroundColor = payload.backgroundColor
    },
    [EXPAND_STARTER_MODAL](state, payload) {
      state.starterExpanded = payload
    },
    [COLLAPSE_STARTER_MODAL](state, payload) {
      state.starterCollapse = payload
    },
    [SHOW_SIGN_UP](state, payload) {
      state.showSignUp = payload
    },
    [SET_STICKY](state, payload) {
      state.isSticky = payload
    },
    [SET_HEADER_WIDTH](state, payload) {
      state.headerWidth = payload
    },
    [MAXIMIZE_STARTER_MODAL](state, payload) {
      state.starterMaximized = payload
    },
    [STARTER_ACCOUNT_MAXIMIZED](state, payload) {
      state.starterAccountMaximized = payload
    },
    [UPDATE_SETUP_CONTNET](state, payload) {
      state.setupContent = payload
    },
    [SET_STORAGE_FORMAT_SELECTED](state, payload) {
      state.isStorageFormatSelected = payload
    },
    [SET_STORAGE_FORM_SUBMIT](state, payload) {
      state.isStorageFormSubmit = payload
    },
    SET_PAST_MONTHS(state, payload) {
      state.pastMonths = payload
    },
    SET_SAVED_SEARCH(state, payload) {
      state.savedSearches = payload.map((item) => {
        return {
          id: item.id,
          text: item.name,
          value: item.id,
        }
      })
    },
    SET_SEARCH_ITEM_STORE(state, payload) {
      state.savedSearches.unshift(payload)
    },
    SET_SAVED_SEARCH_UPDATE(state, payload) {
      state.savedSearches = state.savedSearches.map((item) => {
        if (Number(payload.id) === Number(item.id)) {
          return {
            id: payload.id,
            text: payload.text,
            value: payload.value,
          }
        }
        return item
      })
    },
    SET_SAVED_SEARCH_REMOVE(state, id) {
      state.savedSearches = state.savedSearches.filter((item) => {
        return Number(item.id) !== Number(id)
      })
    },
    SET_SOCIAL_PROVIDERS(state, payload) {
      state.socialProviders = JSON.parse(JSON.stringify(payload))
      state.socialProviders.forEach((item) => {
        item.mediaValue = true
        item.individualMediaHeight = false
        item.showIndividualAccounts = false
        item.feedHeight = 0
      })
      state.socialFeeds.forEach((element) => {
        element.media.forEach((item) => {
          if (item.id === 'type') {
            item.individualMediaType.forEach((childItem) => {
              state.socialProviders.forEach((provider) => {
                if (childItem.id === 'social_media') {
                  if (
                    provider.text !== 'Microsoft' &&
                    provider.text !== 'Google' &&
                    provider.text !== 'Web'
                  ) {
                    childItem.individualMedia.push(provider)
                    childItem.individualMedia = [
                      ...new Map(
                        childItem.individualMedia.map((item) => [
                          item.value,
                          item,
                        ]),
                      ).values(),
                    ]
                    childItem.individualMedia.forEach((individualMediaItem) => {
                      individualMediaItem.individualMediaHeight = false
                      individualMediaItem.individualAccounts = []
                    })
                  }
                } else if (childItem.id === 'email') {
                  if (
                    provider.text === 'Microsoft' ||
                    provider.text === 'Google'
                  ) {
                    childItem.individualMedia.push(provider)
                    childItem.individualMedia = [
                      ...new Map(
                        childItem.individualMedia.map((item) => [
                          item.value,
                          item,
                        ]),
                      ).values(),
                    ]
                    childItem.individualMedia.forEach((individualMediaItem) => {
                      individualMediaItem.individualMediaHeight = false
                      individualMediaItem.individualAccounts = []
                    })
                  }
                } else if (childItem.id === 'web') {
                  if (provider.text === 'Web') {
                    childItem.individualMedia.push(provider)
                    childItem.individualMedia = [
                      ...new Map(
                        childItem.individualMedia.map((item) => [
                          item.value,
                          item,
                        ]),
                      ).values(),
                    ]
                    childItem.individualMedia.forEach((individualMediaItem) => {
                      individualMediaItem.individualMediaHeight = false
                      individualMediaItem.individualAccounts = []
                    })
                  }
                }
              })
            })
          }
        })
      })
    },
    SET_SOCIAL_PROVIDERS_INITIAL(state, payload) {
      state.socialProvidersInitial = JSON.parse(JSON.stringify(payload))
      state.socialProvidersInitial.unshift({
        id: state.socialProvidersInitial.length + 1,
        text: 'All Feeds',
        value: 'all',
      })
    },
    SET_ALL_FEEDS_VALUE(state, payload) {
      state.socialFeeds.forEach((element) => {
        element.media.forEach((item) => {
          if (item.id === 'group') {
            item.individualMediaType = payload.allGroups
          }
          if (item.id === 'name') {
            item.individualMediaType = payload.allNames
            state.socialFeeds[0].media[0].individualMediaType.forEach(
              (individualMediaTypeItem) => {
                if (individualMediaTypeItem.id === 'social_media') {
                  individualMediaTypeItem.individualMedia.forEach(
                    (individualMediaItem) => {
                      item.individualMediaType.forEach((nameItem) => {
                        if (nameItem.provider === individualMediaItem.text) {
                          individualMediaItem.individualAccounts.push(nameItem)
                        }
                      })
                      individualMediaItem.individualAccounts = [
                        ...new Map(
                          individualMediaItem.individualAccounts.map((item) => [
                            item.value,
                            item,
                          ]),
                        ).values(),
                      ]
                    },
                  )
                } else if (individualMediaTypeItem.id === 'email') {
                  individualMediaTypeItem.individualMedia.forEach(
                    (individualMediaItem) => {
                      item.individualMediaType.forEach((nameItem) => {
                        if (nameItem.provider === individualMediaItem.text) {
                          individualMediaItem.individualAccounts.push(nameItem)
                        }
                      })
                      individualMediaItem.individualAccounts = [
                        ...new Map(
                          individualMediaItem.individualAccounts.map((item) => [
                            item.value,
                            item,
                          ]),
                        ).values(),
                      ]
                    },
                  )
                } else if (individualMediaTypeItem.id === 'web') {
                  individualMediaTypeItem.individualMedia.forEach(
                    (individualMediaItem) => {
                      item.individualMediaType.forEach((nameItem) => {
                        if (nameItem.provider === individualMediaItem.text) {
                          individualMediaItem.individualAccounts.push(nameItem)
                        }
                      })
                      individualMediaItem.individualAccounts = [
                        ...new Map(
                          individualMediaItem.individualAccounts.map((item) => [
                            item.value,
                            item,
                          ]),
                        ).values(),
                      ]
                      if (individualMediaItem.individualAccounts.length === 0) {
                        individualMediaTypeItem.individualMedia = []
                      }
                    },
                  )
                }
              },
            )
          }
        })
      })
    },
    ARCHIVE_SETTINGS_VISIBLITY(state, payload) {
      state.archiveSettings = payload
    },
    SET_LOCK_SCREEN(state, payload) {
      state.lockScreen = payload
    },
    SET_LANDING_LOADER(state, payload) {
      state.landingLoader = payload
    },
    [FORGET_PASSWORD_MODAL](state, payload) {
      state.showForgetPasswordModal = payload
    },
    [SET_EMAIL_VERIFY_MODAL](state, payload) {
      state.emailVerifyModal = payload
    },
    SET_SHOW_SIDE_BAR(state, payload) {
      state.showSideBar = payload
    },
    SET_IS_SHOW_SIDE_BAR(state, payload) {
      state.isShowSideBar = payload
    },
    SET_PASSWORD_CONFIRM_EMAIL_VERIFY_MODAL(state, payload) {
      state.passwordConfirmEmailVerifyModal = payload
    },
    SET_CURRENT_PAYMENT_SYSTEM(state, payload) {
      if (
        state.currentPayMethod === 'Automatic' &&
        (payload === 'PayWithCreditCard' ||
          payload === 'SetupAccountPaymentOption')
      ) {
        state.currentPaymentSystem = payload
      } else if (
        state.currentPayMethod === 'Invoice' &&
        (payload === 'PayWithApprovalCode' || payload === 'PayWithPromoCode')
      ) {
        state.currentPaymentSystem = payload
      } else if (
        state.currentPayMethod === 'Automatic' &&
        payload === 'PayWithPromoCode'
      ) {
        state.currentPayMethod = 'Invoice'
        state.currentPaymentSystem = payload
      }
    },
    SET_CURRENT_PAYMENT_SYSTEM_METHOD(state, payload) {
      state.currentPaymentSystem = payload
    },
  },

  actions: {
    async getFeatures({ commit }, payload) {
      const resKeyFeatures = await $fetch(`/api/landing/key-features`)
      if (resKeyFeatures.success) {
        commit('SET_KEY_FEATURES', resKeyFeatures.data)
        commit('SET_LANDING_LOADER', false)
      } else {
        commit('SET_LANDING_LOADER', false)
      }
    },
    toggle_login_modal({ commit }, payload) {
      commit(TOGGLE_LOGIN_MODAL, payload)
    },
    expand_starter_modal({ commit }, payload) {
      commit(EXPAND_STARTER_MODAL, payload)
    },
    collapse_starter_modal({ commit }, payload) {
      commit(COLLAPSE_STARTER_MODAL, payload)
    },
    show_sign_up({ commit }, payload) {
      commit(SHOW_SIGN_UP, payload)
    },
    set_sticky({ commit }, payload) {
      commit(SET_STICKY, payload)
    },
    set_header_width({ commit }, payload) {
      commit(SET_HEADER_WIDTH, payload)
    },
    maximize_starter_modal({ commit }, payload) {
      commit(MAXIMIZE_STARTER_MODAL, payload)
    },
    starter_account_maximized({ commit }, payload) {
      commit(STARTER_ACCOUNT_MAXIMIZED, payload)
    },
    updateSetupContent({ commit }, payload) {
      commit(UPDATE_SETUP_CONTNET, payload)
    },
    setStorageFormatSelect({ commit }, payload) {
      commit(SET_STORAGE_FORMAT_SELECTED, payload)
    },
    setStorageFormSubmit({ commit }, payload) {
      commit(SET_STORAGE_FORM_SUBMIT, payload)
    },
    setPastMonths({ commit }, payload) {
      commit('SET_PAST_MONTHS', payload)
    },
    setSaveSearches({ commit }, payload) {
      commit('SET_SAVED_SEARCH', payload)
    },
    storeSearch({ commit }, payload) {
      commit('SET_SEARCH_ITEM_STORE', payload)
    },
    updateSavedSearch({ commit }, payload) {
      commit('SET_SAVED_SEARCH_UPDATE', payload)
    },
    removeSavedSearch({ commit }, payload) {
      commit('SET_SAVED_SEARCH_REMOVE', payload)
    },
    setAllSocialProviders({ commit }, payload) {
      commit('SET_SOCIAL_PROVIDERS', payload)
      commit('SET_SOCIAL_PROVIDERS_INITIAL', payload)
    },
    showArchiveSettings({ commit }, payload) {
      commit('ARCHIVE_SETTINGS_VISIBLITY', payload)
    },
    showForgetPassword({ commit }, payload) {
      commit(FORGET_PASSWORD_MODAL, payload)
    },
    showEmailVerifyModal({ commit }, payload) {
      commit(SET_EMAIL_VERIFY_MODAL, payload)
    },
    showPasswordConfirmEmailVerifyModal({ commit }, payload) {
      commit(SET_PASSWORD_CONFIRM_EMAIL_VERIFY_MODAL, payload)
    },
  },

  modules: {
    home: homeStore,
    user: userStore,
    header: mobileHeaderStore,
    account: accountStore,
    alert: alertStore,
    archive: archiveStore,
    confirm: confirmStore,
    feeds: feedsStore,
    feedsDropdown: feedsDropdownStore,
    guest: guestStore,
    loginAnimation: loginAnimationStore,
    notifications: notificationsStore,
    profile: profileStore,
    search: searchStore,
    setting: settingStore,
    socialFeed: socialFeedStore,
    system: systemStore,
    auth: authStore,
    community: communityStore,
    agentBook: agentBookStore,
    social: socialStore,
    emails: emailsStore,
    post: postStore,
    browse: browseStore,
    createPost: createPostStore,
    insights: insightsStore,
  },
})
