const emailsStore = {
  namespaced: true,
  state() {
    return {
      emailMessages: [
        {
          id: 1,
          profileUrl: '/social/<EMAIL>',
          from: 'Game On Pro',
          subject: `<p><span>"Level Up: Must-Play Games of the Month"</span></p>`,
          snippet: `<p class="text-[#707070]"> - Dear <PERSON>, explore these exciting games to take your gaming experience to</p>`,
          description: `<p class="text-[#707070]"><PERSON>, explore these exciting games to take your gaming experience to</p>`,
          createdAt: '10:45PM',
          read: false,
          checked: false,
          favourite: false,
          starred: false,
          selected: false,
        },
        {
          id: 2,
          profileUrl: '/social/outlook-mail-1.png',
          from: 'Travel Visionary',
          subject: `<p><span>Plan Your Dream Getaway ✈️</span></p>`,
          snippet: `<p class="text-[#707070]"> - Here’s how to turn your travel dreams into reality.</p>`,
          description: `<p class="text-[#707070]">Here’s how to turn your travel dreams into reality.</p>`,
          createdAt: '8:15PM',
          read: false,
          checked: false,
          favourite: false,
          starred: false,
          selected: false,
        },
        {
          id: 3,
          profileUrl: '/social/outlook-mail-2.png',
          from: 'Foodie Adventures',
          subject: `<p><span>Delicious Recipes to Try Tonight 🍴</span></p>`,
          snippet: `<p class="text-[#707070]"> - Treat yourself to these mouth-watering dishes!</p>`,
          description: `<p class="text-[#707070]">Treat yourself to these mouth-watering dishes!</p>`,
          createdAt: '7:30PM',
          read: true,
          checked: false,
          favourite: false,
          starred: true,
          selected: false,
        },
        {
          id: 4,
          profileUrl: '/social/outlook-mail-3.png',
          from: 'Book HavenX',
          subject: `<p><span>5 Books That Will Change Your Perspective</span></p>`,
          snippet: `<p class="text-[#707070]"> - These books will inspire you and open your mind to new ideas.</p>`,
          description: `<p class="text-[#707070]">These books will inspire you and open your mind to new ideas.</p>`,
          createdAt: '6:30PM',
          read: true,
          checked: false,
          favourite: false,
          starred: true,
          selected: false,
        },
        {
          id: 5,
          profileUrl: '/social/<EMAIL>',
          from: 'Photo Enthusiast',
          subject: `<p><span>Capture the Moment: Photography Tips</span></p>`,
          snippet: `<p class="text-[#707070]"> - Enhance your skills and take stunning shots with these techniques.</p>`,
          description: `<p class="text-[#707070]">Enhance your skills and take stunning shots with these techniques.</p>`,
          createdAt: '5:15PM',
          read: true,
          checked: false,
          favourite: false,
          starred: false,
          selected: false,
        },
        {
          id: 6,
          profileUrl: '/social/<EMAIL>',
          from: 'Success Navigator',
          subject: `<p><span>Navigate Your Way to Success</span></p>`,
          snippet: `<p class="text-[#707070]"> - Map out a plan that ensures you achieve your goals.</p>`,
          description: `<p class="text-[#707070]">Map out a plan that ensures you achieve your goals.</p>`,
          createdAt: '3:30PM',
          read: true,
          checked: false,
          favourite: false,
          starred: false,
          selected: false,
        },
        {
          id: 7,
          profileUrl: '/social/<EMAIL>',
          from: 'InspiredJourneyHQ',
          subject: `<p><span>Inspiration Awaits: Take the First Step</span></p>`,
          snippet: `<p class="text-[#707070]"> - Hello Larry, everything great starts with one small step—make yours today.</p>`,
          description: `<p class="text-[#707070]">Hello Larry, everything great starts with one small step—make yours today.</p>`,
          createdAt: '1:00PM',
          read: true,
          checked: false,
          favourite: false,
          starred: false,
        },
        {
          id: 8,
          profileUrl: '/social/<EMAIL>',
          from: 'Global Forum',
          subject: `<p><span>2024 Global Forum: Registration Open Now</span></p>`,
          snippet: `<p class="text-[#707070]"> - Don’t miss out on a chance to engage with world leaders and innovators.</p>`,
          description: `<p class="text-[#707070]">Don’t miss out on a chance to engage with world leaders and innovators.</p>`,
          createdAt: '11:45AM',
          read: true,
          checked: false,
          favourite: false,
          starred: false,
          selected: false,
        },
        {
          id: 9,
          profileUrl: '/social/<EMAIL>',
          from: 'Workshop Wizard',
          subject: `<p><span>Hands-On Workshop: Learn from the Best</span></p>`,
          snippet: `<p class="text-[#707070]"> - Gain practical insights and skills at our interactive session.</p>`,
          description: `<p class="text-[#707070]">Gain practical insights and skills at our interactive session.</p>`,
          createdAt: '10:21AM',
          read: true,
          checked: false,
          favourite: false,
          starred: false,
          selected: false,
        },
        {
          id: 10,
          profileUrl: '/social/<EMAIL>',
          from: 'Launch Party Pro',
          subject: `<p><span>You're Invited to Our Product Launch 🎉</span></p>`,
          snippet: `<p class="text-[#707070]"> - Hello Larry, its a good news for you. be among the first to witness our newest</p>`,
          description: `<p class="text-[#707070]">Hello Larry, its a good news for you. be among the first to witness our newest...</p>`,
          createdAt: '8:25AM',
          read: true,
          checked: false,
          favourite: false,
          starred: false,
          selected: false,
        },
        {
          id: 11,
          profileUrl: '/social/<EMAIL>',
          from: 'Tech Summit',
          subject: `<p><span>Tech Innovations Conference 2024: Register Now</span></p>`,
          snippet: `<p class="text-[#707070]"> - Join industry leaders for groundbreaking discussions and networking.</p>`,
          description: `<p class="text-[#707070]">Join industry leaders for groundbreaking discussions and networking.</p>`,
          createdAt: '3:14AM',
          read: true,
          checked: false,
          favourite: false,
          starred: false,
          selected: false,
        },
        {
          id: 12,
          profileUrl: '/social/<EMAIL>',
          from: 'Shop Smart Now',
          subject: `<p><span>Shop Smarter, Save More 2024: Register Now</span></p>`,
          snippet: `<p class="text-[#707070]"> - Here’s how you can get the most value out of your purchases.</p>`,
          description: `<p class="text-[#707070]">Here’s how you can get the most value out of your purchases.</p>`,
          createdAt: 'Nov 22',
          read: true,
          checked: false,
          favourite: false,
          starred: false,
          selected: false,
        },
        {
          id: 13,
          profileUrl: '/social/<EMAIL>',
          from: 'Exclusive Deals',
          subject: `<p><span>An Exclusive Offer for Our Valued Partners</span></p>`,
          snippet: `<p class="text-[#707070]"> - As a token of appreciation, we’re offering you first access to this deal.</p>`,
          description: `<p class="text-[#707070]">As a token of appreciation, we’re offering you first access to this deal.</p>`,
          createdAt: 'Nov 22',
          read: true,
          checked: false,
          favourite: false,
          starred: false,
          selected: false,
        },
        {
          id: 14,
          profileUrl: '/social/<EMAIL>',
          from: 'Meeting Master',
          subject: `<p><span>Meeting Follow-Up: Key Takeaways</span></p>`,
          snippet: `<p class="text-[#707070]"> - Thank you for your time earlier—here are the highlights of our discussion.</p>`,
          description: `<p class="text-[#707070]">Thank you for your time earlier—here are the highlights of our discussion.</p>`,
          createdAt: 'Nov 22',
          read: true,
          checked: false,
          favourite: false,
          starred: false,
          selected: false,
        },
        {
          id: 15,
          profileUrl: '/social/<EMAIL>',
          from: 'Time Optimizer',
          subject: `<p><span>Maximize Your Productivity Today</span></p>`,
          snippet: `<p class="text-[#707070]"> - Here are a few simple yet effective strategies to optimize your workflow.</p>`,
          description: `<p class="text-[#707070]">Here are a few simple yet effective strategies to optimize your workflow.</p>`,
          createdAt: 'Nov 21',
          read: true,
          checked: false,
          favourite: false,
          starred: false,
          selected: false,
        },
      ],
      selectedEmailMessage: null,
      showComposeSection: false,
      composeArray: [],
      numberOfCompose: 0,
      showConfidentialModeModal: false,
      showDriveInsertFileModal: false,
      showInsertPhotoFileModal: false,
      showAddRecipients: false,
      selectedColor: '',
      fileChanged: false,
      fileUrl: '',
    }
  },

  getters: {},

  mutations: {
    SET_CHECKED_ALL_EMAIL_MESSAGES(state, payload) {
      state.emailMessages.forEach((emailMessage) => {
        emailMessage.checked = !emailMessage.checked
      })
    },
    SET_UNCHECKED_ALL_EMAIL_MESSAGES(state, payload) {
      state.emailMessages.forEach((emailMessage) => {
        emailMessage.checked = false
      })
    },
    SET_FAVOURITE_MESSAGE(state, payload) {
      const seletedOne = state.emailMessages.find(
        (emailMessage) => emailMessage.id === payload,
      )
      if (seletedOne) {
        seletedOne.favourite = !seletedOne.favourite
      }
    },
    READ_A_SPECIFIC_MESSAGE(state, payload) {
      const selectedOne = state.emailMessages.find(
        (emailMessage) => emailMessage.id === payload,
      )
      console.log(selectedOne, 'selectedOne')
      if (selectedOne) {
        selectedOne.read = true
      }
      state.emailMessages.forEach((emailMessage) => {
        if (emailMessage.id === payload) {
          emailMessage.selected = true
        } else {
          emailMessage.selected = false
        }
      })
      console.log(selectedOne, 'selectedOne')
    },
    READ_UNREAD_A_SPECIFIC_MESSAGE(state, payload) {
      const seletedOne = state.emailMessages.find(
        (emailMessage) => emailMessage.id === payload,
      )
      if (seletedOne) {
        seletedOne.read = !seletedOne.read
      }
    },
    READ_ALL_MESSAGE_WITH_ALL_UNCHECK(state, payload) {
      state.emailMessages.forEach((emailMessage) => {
        emailMessage.read = true
      })
    },
    READ_ALL_MESSAGE(state, payload) {
      state.emailMessages.forEach((emailMessage) => {
        if (emailMessage.checked) {
          emailMessage.read = true
        }
      })
      const unCheckAllItem = state.emailMessages.every(
        (emailMessage) => !emailMessage.checked,
      )
      if (unCheckAllItem) {
        state.emailMessages.forEach((emailMessage) => {
          emailMessage.read = true
        })
      }
    },
    UNREAD_ALL_MESSAGE(state, payload) {
      state.emailMessages.forEach((emailMessage) => {
        if (emailMessage.checked) {
          emailMessage.read = false
        }
      })
      const unCheckAllItem = state.emailMessages.every(
        (emailMessage) => !emailMessage.checked,
      )
      if (unCheckAllItem) {
        state.emailMessages.forEach((emailMessage) => {
          emailMessage.read = false
        })
      }
    },
    SET_CHECKED_READ_EMAIL_MESSAGES(state, payload) {
      switch (payload.title) {
        case 'All':
          state.emailMessages.forEach((emailMessage) => {
            emailMessage.checked = true
          })
          break
        case 'None':
          state.emailMessages.forEach((emailMessage) => {
            emailMessage.checked = false
          })
          break
        case 'Read':
          state.emailMessages.forEach((emailMessage) => {
            emailMessage.checked = emailMessage.read
          })
          break
        case 'Unread':
          state.emailMessages.forEach((emailMessage) => {
            emailMessage.checked = !emailMessage.read
          })
          break
        case 'Starred':
          state.emailMessages.forEach((emailMessage) => {
            emailMessage.checked = emailMessage.starred
          })
          break
        case 'Unstarred':
          state.emailMessages.forEach((emailMessage) => {
            emailMessage.checked = !emailMessage.starred
          })
          break
      }
      // state.emailMessages.forEach((emailMessage) => {
      //   emailMessage.checked = emailMessage.read
      // })
    },
    SET_CHECK_SINGLE_MESSAGE(state, payload) {
      state.emailMessages.forEach((emailMessage) => {
        if (emailMessage.id === payload) {
          emailMessage.checked = true
        } else {
          emailMessage.checked = false
        }
      })
    },
    SET_SELECTED_EMAIL_MESSAGE(state, payload) {
      state.selectedEmailMessage = payload
    },
    SET_SHOW_COMPOSE_SECTION(state, payload) {
      state.showComposeSection = true
      state.numberOfCompose = state.numberOfCompose + 1
      state.composeArray.push({
        id: state.numberOfCompose,
        active: true,
      })
      state.composeArray.forEach((item) => {
        if (state.numberOfCompose !== item.id) {
          item.active = false
        }
      })
    },
    RESET_SHOW_COMPOSE_SECTION(state, payload) {
      state.showComposeSection = false
      state.composeArray = []
      state.numberOfCompose = 0
    },
    SET_ACTIVE_COMPOSE_MESSAGE(state, payload) {
      state.composeArray.forEach((item) => {
        if (payload === item.id) {
          item.active = true
        } else {
          item.active = false
        }
      })
    },
    DELETE_A_SPECIFIC_COMPOSE_MESSAGE(state, payload) {
      console.log(state.composeArray.length, 'state.composeArray.length')
      if (state.composeArray.length > 0) {
        state.composeArray.forEach((item, index) => {
          if (item.id === payload) {
            state.composeArray.splice(index, 1)
          }
        })
        if (state.composeArray.length > 0) {
          state.composeArray[state.composeArray.length - 1].active = true
        }
      }
      console.log(state.composeArray.length, 'state.composeArray.length')
      if (state.composeArray.length === 0) {
        this.commit('emails/RESET_SHOW_COMPOSE_SECTION')
      }
    },
    SET_CONFIDENTIAL_MODE_MODAL(state, payload) {
      state.showConfidentialModeModal = payload
    },
    SET_DRIVE_INSERT_FILE_MODAL(state, payload) {
      state.showDriveInsertFileModal = payload
    },
    SET_PHOTO_INSERT_FILE_MODAL(state, payload) {
      state.showInsertPhotoFileModal = payload
    },
    SET_SHOW_ADD_RECIPIENTS(state, payload) {
      state.showAddRecipients = payload
    },
    SET_SELECTED_COLOR(state, payload) {
      state.selectedColor = payload
    },
    SET_FILE_CHANGED(state, payload) {
      state.fileChanged = payload.fileChanged
      state.fileUrl = payload.fileUrl
    }
  },

  actions: {},
}

export default emailsStore
