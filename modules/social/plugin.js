import {
  decodeValue,
  encodeQuery,
  encodeValue,
  generateRandomString,
  isUnset,
  parseQuery,
  _base64UrlEncode,
  _sha256,
} from '~/modules/social/utils'

export default defineNuxtPlugin((nuxtApp) => {
  const route = useRoute()
  const $config = useRuntimeConfig()

  const social = {
    // redirect to social provider
    redirect: async (name, state = generateRandomString(), params = {}) => {
      const cookie1 = useCookie(name + '.pkce_state')
      const cookie2 = useCookie(name + '.pkce_code_verifier')
      const cookie3 = useCookie(name + '.state', {
        path: '/',
      })
      const provider = {
        clientId: $config.public.social[name].clientId,
        clientKey: $config.public.social[name].clientKey,
        nonce: $config.public.social[name].nonce,
        prompt: $config.public.social[name].prompt,
        accessType: $config.public.social[name].accessType,
        responseType: $config.public.social[name].responseType,
        duration: $config.public.social[name].duration,
        redirectUri: $config.public.social[name].redirectUri,
        scope: $config.public.social[name].scope,
        authorization: $config.public.social[name].authorization,
        codeChallengeMethod: $config.public.social[name].codeChallengeMethod,
      }
      const scope = Array.isArray(provider.scope)
        ? provider.scope.join(',')
        : name != 'instagram'
          ? provider.scope
          : provider.scope.replace(/ /g, ',')

      const queryParams = {
        protocol: 'oauth2',
        response_type: provider.responseType,
        duration: provider.duration,
        client_id: provider.clientId,
        client_key: provider.clientKey,
        nonce: provider.nonce,
        prompt: provider.prompt,
        access_type: provider.accessType,
        redirect_uri: provider.redirectUri,
        scope,
        state,
        code_challenge_method: provider.codeChallengeMethod,
        ...params,
      }

      const intagramQueryParams = {
        client_id: provider.clientId,
        redirect_uri: provider.redirectUri,
        response_type: provider.responseType,
        force_authentication: 1,
        scope,
        state,
      }

      if (name === 'facebook') {
        queryParams.auth_type = 'rerequest'
      }

      if (queryParams.code_challenge_method) {
        switch (queryParams.code_challenge_method) {
          case 'plain':
            queryParams.code_challenge = 'ILoveSharpArchive'
            break
          case 'S256':
            {
              const state = generateRandomString()
              // social.setCookie(name + '.pkce_state', state)
              cookie1.value = state
              const codeVerifier = generateRandomString()
              // social.setCookie(
              //   name + '.pkce_code_verifier',
              //   codeVerifier
              // )
              cookie2.value = codeVerifier
              const codeChallenge = await social.pkceChallengeFromVerifier(
                codeVerifier,
                queryParams.code_challenge_method === 'S256',
              )
              queryParams.code_challenge =
                window.encodeURIComponent(codeChallenge)
            }
            break
          case 'implicit':
          default:
            break
        }
      }

      // social.setCookie(name + '.state', queryParams.state, { path: '/' })
      cookie3.value = queryParams.state

      const url =
        name != 'instagram'
          ? provider.authorization + '?' + encodeQuery(queryParams)
          : provider.authorization + '?' + encodeQuery(intagramQueryParams)

      window.location.replace(url)
    },

    // handle redirect
    handleRedirect: (name) => {
      // Callback flow is not supported in server side
      const cookie1 = useCookie(name + '.state')
      const cookie2 = useCookie(name + '.pkce_code_verifier')
      if (process.server) {
        return
      }

      const hash = parseQuery(route.hash.substr(1))
      const parsedQuery = Object.assign({}, route.query, hash)
      // Validate state
      // const state = social.getCookie(name + '.state')
      const state = cookie1.value
      // social.setCookie(name + '.state', null)
      cookie1.value = null
      if (state && parsedQuery.state !== state && name !== 'twitter') {
        return
      }
      let codeVerifier

      const codeChallengeMethod =
        $config.public.social[name].codeChallengeMethod
      if (codeChallengeMethod !== 'implicit') {
        // codeVerifier = social.getCookie(
        //   name + '.pkce_code_verifier'
        // )
        codeVerifier = cookie2.value
        // social.setCookie(
        //   name + '.pkce_code_verifier',
        //   null
        // )
        cookie2.value = null
      }

      return {
        provider: name,
        code: parsedQuery.code,
        code_verifier: codeVerifier,
        state: parsedQuery.state,
      }
    },

    async pkceChallengeFromVerifier(v, hashValue) {
      if (hashValue) {
        const hashed = await _sha256(v)
        return _base64UrlEncode(hashed)
      }
      return v // plain is plain - url-encoded by default
    },

    // setCookie(
    //   key,
    //   value,
    //   options = {}
    // ) {
    //   const _prefix = 'social'
    //   const _key = _prefix + key
    //   const _options = Object.assign({}, options)
    //   const _value = encodeValue(value)

    //   // Unset null, undefined
    //   if (isUnset(value)) {
    //     _options.maxAge = -1
    //   }

    //   // Accept expires as a number for js-cookie compatibility
    //   if (typeof _options.expires === 'number') {
    //     _options.expires = new Date(Date.now() + _options.expires * 864e5)
    //   }

    //   const serializedCookie = cookie.serialize(_key, _value, _options)

    //   if (process.client) {
    //     // Set in browser
    //     document.cookie = serializedCookie
    //   } else if (process.server && ctx.res) {
    //     // Send Set-Cookie header from server side
    //     const cookies = (ctx.res.getHeader('Set-Cookie')) || []
    //     cookies.unshift(serializedCookie)
    //     ctx.res.setHeader(
    //       'Set-Cookie',
    //       cookies.filter(
    //         (v, i, arr) =>
    //           arr.findIndex((val) =>
    //             val.startsWith(v.substr(0, v.indexOf('=')))
    //           ) === i
    //       )
    //     )
    //   }

    //   return value
    // },

    // getCookies() {
    //   const cookieStr = process.client
    //     ? document.cookie
    //     : ctx.req.headers.cookie

    //   return cookie.parse(cookieStr || '') || {}
    // },

    // getCookie(key) {
    //   if (process.server && !ctx.req) {
    //     return
    //   }

    //   const _key = 'social' + key

    //   const cookies = social.getCookies()

    //   const value = cookies[_key]
    //     ? decodeURIComponent(cookies[_key])
    //     : undefined

    //   return decodeValue(value)
    // },

    // removeCookie(key, options) {
    //   social.setCookie(key, undefined, options)
    // }
  }
  // Inject social to the context as $social
  // ctx.$social = social
  nuxtApp.provide('social', social)
})
