<script setup>
import { useStore } from 'vuex'

const store = useStore()

const props = defineProps({
  applyColor: Function,
  title: String,
  selectedColorCode: String, // Function to apply the color
})

const colors = [
  [
    '#000000',
    '#444444',
    '#666666',
    '#999999',
    '#CCCCCC',
    '#EEEEEE',
    '#F3F3F3',
    '#FFFFFF',
  ],
  [
    '#FF0000',
    '#FF9900',
    '#FFFF00',
    '#00FF00',
    '#00FFFF',
    '#0000FF',
    '#9900FF',
    '#FF00FF',
  ],
  [
    '#F4CCCC',
    '#FCE5CD',
    '#FFF2CC',
    '#D9EAD3',
    '#D0E0E3',
    '#CFE2F3',
    '#D9D2E9',
    '#EAD1DC',
  ],
  [
    '#EA9999',
    '#F9CB9C',
    '#FFE599',
    '#B6D7A8',
    '#A2C4C9',
    '#9FC5E8',
    '#B4A7D6',
    '#D5A6BD',
  ],
  [
    '#E06666',
    '#F6B26B',
    '#FFD966',
    '#93C47D',
    '#76A5AF',
    '#6FA8DC',
    '#8E7CC3',
    '#C27BA0',
  ],
  [
    '#CC0000',
    '#E69138',
    '#F1C232',
    '#6AA84F',
    '#45818E',
    '#3D85C6',
    '#674EA7',
    '#A64D79',
  ],
  [
    '#990000',
    '#B45F06',
    '#BF9000',
    '#38761D',
    '#134F5C',
    '#0B5394',
    '#351C75',
    '#741B47',
  ],
  [
    '#660000',
    '#783F04',
    '#7F6000',
    '#274E13',
    '#0C343D',
    '#073763',
    '#20124D',
    '#4C1130',
  ],
]

// const selectedColor = computed(() => store.state.emails.selectedColor)
const selectedColor = ref('')
const setColor = (color) => {
  // store.commit('emails/SET_SELECTED_COLOR', color)
  selectedColor.value = color
  props.applyColor(color) // Apply the selected color
}
const isDarkColor = (hex) => {
  // Convert hex to RGB
  const c = hex.substring(1) // remove '#'
  const rgb = parseInt(c, 16)
  const r = (rgb >> 16) & 0xff
  const g = (rgb >> 8) & 0xff
  const b = (rgb >> 0) & 0xff
  // Calculate brightness
  const brightness = (r * 299 + g * 587 + b * 114) / 1000
  return brightness < 128 // Threshold: lower means dark
}
</script>

<template>
  <div class="relative group">
    <!-- Current color button -->
    <!-- <button
      class="w-8 h-8 border rounded"
      :style="{ backgroundColor: selectedColor }"
    ></button> -->

    <!-- Dropdown color picker -->
    <div class="grid-cols-8 gap-x-[0px] gap-y-[2px] grid">
      <!-- <p class="col-span-8 text-xs font-bold mb-1 text-gray-600">{{ title }}</p> -->

      <!-- Render color buttons with checkboxes -->
      <template v-for="(row, rowIndex) in colors" :key="index">
        <button
          v-for="(color, index) in row"
          :key="color"
          class="w-5 h-5 relative"
          :class="{
            'border border-[#525252]':
              selectedColor === color || (selectedColorCode === color && !selectedColor),
            '!mb-2':
              (rowIndex === 0 && index === 0) ||
              (rowIndex === 1 && index === 0),
          }"
          :style="{ backgroundColor: color }"
          @click="setColor(color)"
        >
          <span
            v-if="selectedColor === color || (selectedColorCode === color && !selectedColor)"
            class="absolute inset-0 flex items-center justify-center"
          >
            <fa
              :icon="['fa', 'check']"
              :style="{ color: isDarkColor(color) ? 'white' : 'black' }"
            />
          </span>
        </button>
      </template>
    </div>
  </div>
</template>

<style scoped>
button {
  cursor: pointer;
}
</style>
