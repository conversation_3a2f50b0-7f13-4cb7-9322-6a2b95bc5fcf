<script setup lang="ts">
import { useStore } from 'vuex'
import DeleteIcon from '~/components/shared/icon/hub/microsoft/sidebar/DeleteItemsIcon.vue'
import IgnoreIcon from '~/components/shared/icon/hub/microsoft/IgnoreIcon.vue'
import ArchiveIcon from '~/components/shared/icon/hub/emails/ArchiveIcon.vue'
import ReportIcon from '~/components/shared/icon/hub/microsoft/ReportIcon.vue'
import BlockIcon from '~/components/shared/icon/hub/microsoft/BlockIcon.vue'
import ReplyIcon from '~/components/shared/icon/hub/emails/Reply.vue'
import ReplyAllIcon from '~/components/shared/icon/hub/microsoft/ReplyAllIcon.vue'
import ForwardIcon from '~/components/shared/icon/hub/emails/Forward.vue'

const store = useStore()
const route = useRoute()
const router = useRouter()

interface EmailMessage {
  id: number
  subject: string
  snippet: string
  description: string
  createdAt: string
  read: boolean
  checked: boolean
  favourite: boolean
}

const emailMessages = computed(() => store.state.emails.emailMessages)
const showComposeSection = computed(() => store.state.emails.showComposeSection)

const checkedAll = ref<boolean>(false)
const showMinus = ref<boolean>(false)
const toggleCheckedAll = ($event: any) => {
  if (!showMinus.value) {
    checkedAll.value = !checkedAll.value
    store.commit('emails/SET_CHECKED_ALL_EMAIL_MESSAGES')
  } else {
    showMinus.value = false
    checkedAll.value = false
    store.commit('emails/SET_UNCHECKED_ALL_EMAIL_MESSAGES')
  }
}
const toggleSingleCheckBox = () => {
  showMinus.value = emailMessages.value.some(
    (emailMessage: EmailMessage) => emailMessage.checked,
  )
  if (showMinus.value) {
    checkedAll.value = true
  }
  const allChecked = emailMessages.value.every(
    (emailMessage: EmailMessage) => emailMessage.checked,
  )
  if (allChecked) {
    showMinus.value = false
    checkedAll.value = true
  }
  const allUnchecked = emailMessages.value.every(
    (emailMessage: EmailMessage) => !emailMessage.checked,
  )
  if (allUnchecked) {
    showMinus.value = false
    checkedAll.value = false
  }
}
const toggleDeleteMenu = ref<boolean>(false)
const deleteMenuOptions = ref([
  {
    id: 1,
    title: 'Delete',
    image: markRaw(DeleteIcon),
    border: false,
  },
  {
    id: 2,
    title: 'Ignore',
    image: markRaw(IgnoreIcon),
    border: false,
  },
])
const toggleMoveMenu = ref<boolean>(false)
const moveMenuOptions = ref([
  {
    id: 1,
    title: 'Delete Items',
    image: markRaw(DeleteIcon),
    border: false,
  },
  {
    id: 2,
    title: 'Archive',
    image: markRaw(ArchiveIcon),
    border: true,
  },
  {
    id: 3,
    title: 'Create new folder',
    image: '',
    emptyDiv: true,
    button: 'Save',
    border: true,
  },
  {
    id: 4,
    title: 'Move to Focused inbox',
    image: '',
    emptyDiv: true,
    border: false,
  },
  {
    id: 5,
    title: 'Always move to Focused inbox',
    image: '',
    emptyDiv: true,
    border: false,
  },
  {
    id: 6,
    title: 'Move to a different folder...',
    image: '',
    emptyDiv: true,
    border: false,
  },
])
const toggleReportMenu = ref<boolean>(false)
const reportMenuOptions = ref([
  {
    id: 1,
    title: 'Report phishing',
    image: markRaw(ReportIcon),
    border: false,
  },
  {
    id: 2,
    title: 'Report junk',
    image: markRaw(BlockIcon),
    border: false,
  },
])
const toggleReplyMenu = ref<boolean>(false)
const replyMenuOptions = ref([
  {
    id: 1,
    title: 'Reply',
    image: markRaw(ReplyIcon),
    border: false,
  },
  {
    id: 2,
    title: 'Reply all',
    image: markRaw(ReplyAllIcon),
    border: false,
  },
  {
    id: 3,
    title: 'Forward',
    image: markRaw(ForwardIcon),
    border: false,
  },
])
const toggleFlagMenu = ref<boolean>(false)
const flagMenuOptions = ref([
  {
    id: 1,
    title: 'Today',
    image: '',
    border: false,
  },
  {
    id: 2,
    title: 'Tomorrow',
    image: '',
    border: false,
  },
  {
    id: 3,
    title: 'This week',
    image: '',
    border: false,
  },
  {
    id: 4,
    title: 'Next week',
    image: '',
    border: false,
  },
  {
    id: 5,
    title: 'No date',
    image: '',
    border: true,
  },
  {
    id: 6,
    title: 'Mark complete',
    image: '',
    border: false,
  },
  {
    id: 7,
    title: 'Clear flag',
    image: '',
    border: false,
    opacity: true,
  },
])
const toggleMarkAllReadMenu = ref<boolean>(false)
const toggleReadUnreadButton = ref<boolean>(false)
const setReadUnreadToggleButton = () => {
  toggleReadUnreadButton.value = !toggleReadUnreadButton.value
  if (toggleReadUnreadButton.value) {
    store.commit('emails/READ_ALL_MESSAGE')
  } else {
    store.commit('emails/UNREAD_ALL_MESSAGE')
  }
}
const currentRoute = ref<string>('')
onMounted(() => {
  currentRoute.value = route.fullPath
  document.addEventListener('click', () => {
    toggleDeleteMenu.value = false
    toggleMoveMenu.value = false
    toggleReportMenu.value = false
    toggleReplyMenu.value = false
    toggleFlagMenu.value = false
    toggleMarkAllReadMenu.value = false
    // store.commit('emails/SET_CHECK_SINGLE_MESSAGE', 0)
    // toggleSingleCheckBox()
  })
  window.addEventListener('popstate', function (event) {
    listenWindowBackForwardEvent(event.state.current)
  })
})
onUnmounted(() => {
  document.removeEventListener('click', () => {
    toggleDeleteMenu.value = false
    toggleMoveMenu.value = false
    toggleReportMenu.value = false
    toggleReplyMenu.value = false
    toggleFlagMenu.value = false
    toggleMarkAllReadMenu.value = false
    // store.commit('emails/SET_CHECK_SINGLE_MESSAGE', 0)
    // toggleSingleCheckBox()
  })
  document.removeEventListener('wheel', () => {
    if (showMenu.value) {
      store.commit('emails/SET_CHECK_SINGLE_MESSAGE', 0)
      showMenu.value = false
      toggleSingleCheckBox()
      position.value.x = 0
      position.value.y = 0
    }
  })
  window.removeEventListener('popstate', function (event) {
    listenWindowBackForwardEvent(event.state.current)
  })
})
const setToggleDeleteMenu = () => {
  toggleDeleteMenu.value = !toggleDeleteMenu.value
  toggleMoveMenu.value = false
  toggleReportMenu.value = false
  toggleReplyMenu.value = false
  toggleFlagMenu.value = false
  toggleMarkAllReadMenu.value = false
}
const setToggleMoveMenu = () => {
  toggleMoveMenu.value = !toggleMoveMenu.value
  toggleDeleteMenu.value = false
  toggleReportMenu.value = false
  toggleReplyMenu.value = false
  toggleFlagMenu.value = false
  toggleMarkAllReadMenu.value = false
}
const setToggleReportMenu = () => {
  toggleReportMenu.value = !toggleReportMenu.value
  toggleDeleteMenu.value = false
  toggleMoveMenu.value = false
  toggleReplyMenu.value = false
  toggleFlagMenu.value = false
  toggleMarkAllReadMenu.value = false
}
const setToggleReplyMenu = () => {
  toggleReplyMenu.value = !toggleReplyMenu.value
  toggleReportMenu.value = false
  toggleDeleteMenu.value = false
  toggleMoveMenu.value = false
  toggleFlagMenu.value = false
  toggleMarkAllReadMenu.value = false
}
const setToggleFlagMenu = () => {
  toggleFlagMenu.value = !toggleFlagMenu.value
  toggleReportMenu.value = false
  toggleDeleteMenu.value = false
  toggleMoveMenu.value = false
  toggleReplyMenu.value = false
  toggleMarkAllReadMenu.value = false
}
const settoggleMarkAllReadMenu = () => {
  toggleMarkAllReadMenu.value = !toggleMarkAllReadMenu.value
  toggleFlagMenu.value = false
  toggleReportMenu.value = false
  toggleDeleteMenu.value = false
  toggleMoveMenu.value = false
  toggleReplyMenu.value = false
}

const showSpecificMessage = ref(false)
const setSpecificMessage = (emailMessage: EmailMessage) => {
  showSpecificMessage.value = true
  router.push(`${currentRoute.value}/${emailMessage.id}`)
  store.commit('emails/READ_A_SPECIFIC_MESSAGE', emailMessage.id)
  store.commit('emails/SET_SELECTED_EMAIL_MESSAGE', emailMessage)
  store.commit('emails/RESET_SHOW_COMPOSE_SECTION')
}
const listenWindowBackForwardEvent = (path: string) => {
  showSpecificMessage.value = false
}
</script>

<template>
  <div ref="messageMenu" class="w-full h-full overflow-hidden">
    <div
      class="flex justify-between items-center pl-2 pr-4 py-2 border-b border-[#E3E3E3]"
    >
      <div v-if="!showComposeSection" class="flex space-x-2 items-center">
        <div
          class="flex items-center relative"
          :class="checkedAll || showMinus ? 'bg-[#F1F2F6] rounded-md' : ''"
        >
          <div
            class="hover:bg-[#F1F2F6] flex items-center h-10 px-2 rounded-md cursor-pointer"
          >
            <InputsCheckBoxInput
              :model-value="checkedAll"
              :show-minus="showMinus"
              @update:modelValue="toggleCheckedAll"
            />
          </div>
        </div>
        <div
          class="relative hover:bg-[#F1F2F6] px-2 w-fit h-7 flex space-x-[11px] justify-center items-center rounded cursor-pointer"
          :class="showMinus || checkedAll || showSpecificMessage ? ' opacity-100' : ' opacity-30'"
        >
          <SharedIconHubMicrosoftSidebarDeleteItemsIcon
            @click.stop="setToggleDeleteMenu"
          />
          <div
            class="flex space-x-2 items-center"
            @click.stop="setToggleDeleteMenu"
          >
            <p class="text-[#525252] text-sm max-[1210px]:hidden">Delete</p>
            <SharedIconHubMicrosoftArrowIcon class="w-2 h-1" />
          </div>
          <!--             v-if="(showMinus || checkedAll) && toggleDeleteMenu" -->
          <SourceHubMicrosoftModalDelete
            v-if="(showMinus || checkedAll || showSpecificMessage) && toggleDeleteMenu"
            :modalOptions="deleteMenuOptions"
            width="105px"
            class="top-7 -left-[10px]"
          />
        </div>
        <div
          class="hover:bg-[#F1F2F6] px-2 w-fit h-7 flex space-x-[11px] justify-center items-center rounded cursor-pointer"
          :class="showMinus || checkedAll || showSpecificMessage ? ' opacity-100' : ' opacity-30'"
        >
          <SharedIconHubMicrosoftSidebarArchiveIcon />
          <div class="flex space-x-2 items-center max-[1272px]:hidden">
            <p class="text-[#525252] text-sm">Archive</p>
          </div>
        </div>
        <div
          class="hover:bg-[#F1F2F6] px-2 w-fit h-7 flex space-x-[11px] justify-center items-center rounded cursor-pointer relative"
          :class="showMinus || checkedAll || showSpecificMessage ? ' opacity-100' : ' opacity-30'"
        >
          <SharedIconHubEmailsMoveToIcon @click.stop="setToggleMoveMenu" />
          <div
            class="flex space-x-2 items-center"
            @click.stop="setToggleMoveMenu"
          >
            <p
              class="text-[#525252] text-sm whitespace-nowrap max-[1319px]:hidden"
            >
              Move to
            </p>
            <SharedIconHubMicrosoftArrowIcon class="w-2 h-1" />
          </div>
          <SourceHubMicrosoftModalDelete
            v-if="(showMinus || checkedAll || showSpecificMessage) && toggleMoveMenu"
            :modalOptions="moveMenuOptions"
            width="279px"
            class="top-7 -left-[10px]"
          />
        </div>
        <div
          class="w-[1px] min-w-[1px] h-[28px] bg-[#707070] bg-opacity-30"
        ></div>
        <div
          class="relative hover:bg-[#F1F2F6] px-2 w-fit h-7 flex space-x-[11px] justify-center items-center rounded cursor-pointer"
          :class="[
            showMinus || checkedAll || showSpecificMessage ? ' opacity-100' : ' opacity-30',
            (showMinus || checkedAll || showSpecificMessage) && toggleReportMenu ? 'bg-[#F1F2F6]' : '',
          ]"
        >
          <SharedIconHubMicrosoftReportIcon @click.stop="setToggleReportMenu" />
          <div
            class="flex space-x-2 items-center"
            @click.stop="setToggleReportMenu"
          >
            <p class="text-[#525252] text-sm max-[1374px]:hidden">Report</p>
            <SharedIconHubMicrosoftArrowIcon class="w-2 h-1" />
          </div>
          <SourceHubMicrosoftModalDelete
            v-if="(showMinus || checkedAll || showSpecificMessage) && toggleReportMenu"
            :modalOptions="reportMenuOptions"
            width="172px"
            class="top-7 -left-[10px]"
          />
        </div>
        <div
          class="w-[1px] min-w-[1px] h-[28px] bg-[#707070] bg-opacity-30"
        ></div>
        <div
          class="relative hover:bg-[#F1F2F6] px-2 w-fit h-7 flex space-x-[11px] justify-center items-center rounded cursor-pointer"
          :class="[
            showMinus || checkedAll || showSpecificMessage ? ' opacity-100' : ' opacity-30',
            (showMinus || checkedAll || showSpecificMessage) && toggleReplyMenu ? 'bg-[#F1F2F6]' : '',
          ]"
        >
          <SharedIconHubMicrosoftReplyAllIcon
            @click.stop="setToggleReplyMenu"
          />
          <div
            class="flex space-x-2 items-center"
            @click.stop="setToggleReplyMenu"
          >
            <p
              class="text-[#525252] text-sm whitespace-nowrap max-[1422px]:hidden"
            >
              Reply All
            </p>
            <SharedIconHubMicrosoftArrowIcon class="w-2 h-1" />
          </div>
          <SourceHubMicrosoftModalDelete
            v-if="(showMinus || checkedAll || showSpecificMessage) && toggleReplyMenu"
            :modalOptions="replyMenuOptions"
            width="119px"
            class="top-7 -left-[10px]"
          />
        </div>
        <div
          class="w-[1px] min-w-[1px] h-[28px] bg-[#707070] bg-opacity-30"
        ></div>
        <div
          class="hover:bg-[#F1F2F6] px-2 w-fit h-7 flex space-x-[11px] justify-center items-center rounded cursor-pointer"
          :class="showMinus || checkedAll || showSpecificMessage ? ' opacity-100' : ' opacity-100'"
          @click="setReadUnreadToggleButton"
        >
          <SharedIconHubEmailsReadMessageIcon />
          <div class="flex space-x-2 items-center max-[1517px]:hidden">
            <p class="text-[#525252] text-sm whitespace-nowrap">
              Read / Unread
            </p>
          </div>
        </div>
        <div
          class="relative hover:bg-[#F1F2F6] px-2 w-fit h-7 flex space-x-[11px] justify-center items-center rounded cursor-pointer"
          :class="[
            showMinus || checkedAll || showSpecificMessage ? ' opacity-100' : ' opacity-30',
            (showMinus || checkedAll || showSpecificMessage) && toggleFlagMenu ? 'bg-[#F1F2F6]' : '',
          ]"
        >
          <SharedIconHubMicrosoftFlagIcon @click.stop="setToggleFlagMenu" />
          <div
            class="flex space-x-2 items-center max-[1616px]:hidden"
            @click.stop="setToggleFlagMenu"
          >
            <p class="text-[#525252] text-sm whitespace-nowrap">
              Flag / Unflag
            </p>
            <SharedIconHubMicrosoftArrowIcon class="w-2 h-1" />
          </div>
          <SourceHubMicrosoftModalDelete
            v-if="(showMinus || checkedAll || showSpecificMessage) && toggleFlagMenu"
            :modalOptions="flagMenuOptions"
            width="139px"
            class="top-7 -left-[10px]"
          />
        </div>
        <div
          class="w-[1px] min-w-[1px] h-[28px] bg-[#707070] bg-opacity-30"
        ></div>
        <div
          class="hover:bg-[#F1F2F6] px-2 w-fit h-7 flex space-x-[11px] justify-center items-center rounded cursor-pointer"
          :class="showMinus || checkedAll || showSpecificMessage ? ' opacity-100' : ' opacity-30'"
        >
          <SharedIconHubMicrosoftUndoIcon />
          <div class="flex space-x-2 items-center max-[1656px]:hidden">
            <p class="text-[#525252] text-sm">Undo</p>
          </div>
        </div>
        <div
          class="w-[1px] min-w-[1px] h-[28px] bg-[#707070] bg-opacity-30"
        ></div>
        <div
          class="hover:bg-[#F1F2F6] w-7 min-w-[7] h-7 flex justify-center items-center rounded cursor-pointer relative"
          :class="toggleMarkAllReadMenu ? 'bg-[#F1F2F6]' : ''"
          @click.stop="settoggleMarkAllReadMenu"
        >
          <SharedIconHubEmailsThreeDotMenuIcon
            class="transform rotate-90"
            color="#525252"
          />
          <SourceHubMicrosoftModalSelect
            v-if="toggleMarkAllReadMenu"
            class="top-7 min-[1840px]:left-[0px] right-0"
          />
        </div>
      </div>
      <SourceHubMicrosoftComposeInputModifier v-else />
    </div>
    <div class="w-full h-full grid grid-cols-[minmax(300px,0.4fr)_1fr]">
      <div class="h-[calc(100%-58px)] custom-scroll">
        <div
          class="px-3 py-3 flex space-x-2 border-b border-[#E3E3E3] cursor-pointer relative message-box"
          v-for="emailMessage in emailMessages"
          :key="emailMessage.id"
          :class="[
            emailMessage.checked && emailMessage.read
              ? 'bg-[#D6E7FF]'
              : emailMessage.checked && !emailMessage.read
                ? 'bg-[#D6E7FF] border-l-4 border-l-[#0F6CBC]'
                : '',
            emailMessage.read ? '' : 'border-l-4 border-l-[#0F6CBC]',
            emailMessage.selected ? 'bg-[#D6E7FF]' : '',
          ]"
          @contextmenu="(event) => checkSingleMessage(event, emailMessage.id)"
        >
          <div
            class="w-10 h-10 min-w-10 min-h-10 rounded-full flex items-center justify-start"
          >
            <InputsCheckBoxInput
              :id="emailMessage.id"
              class="checkbox"
              :class="emailMessage.checked ? '!block' : '!hidden'"
              v-model="emailMessage.checked"
              :model-value="emailMessage.checked"
              @update:modelValue="toggleSingleCheckBox"
            />
            <img
              class="profile-image"
              :class="emailMessage.checked ? '!hidden' : 'block'"
              :src="emailMessage.profileUrl"
              :alt="emailMessage.from"
            />
          </div>
          <div
            class="flex flex-col flex-grow space-y-1.5"
            @click.stop="setSpecificMessage(emailMessage)"
          >
            <h2
              class="text-[#333333]"
              :class="emailMessage.read ? '' : 'font-semibold'"
            >
              {{ emailMessage.from }}
            </h2>
            <div
              class="flex justify-between !space-x-2"
              :class="
                emailMessage.read
                  ? 'text-[#333333]'
                  : 'text-[#0F6CBC] font-semibold'
              "
            >
              <div class="line-clamp-1" v-html="emailMessage.subject"></div>
              <p class="whitespace-nowrap">{{ emailMessage.createdAt }}</p>
            </div>
            <div
              class="line-clamp-1 text-[#707070]"
              v-html="emailMessage.snippet"
            ></div>
          </div>
        </div>
      </div>
      <div
        v-if="!showSpecificMessage && !showComposeSection"
        class="w-full h-full bg-[#F1F2F6] flex justify-center items-center"
      >
        <div class="flex flex-col text-center items-center">
          <img
            width="96"
            height="96"
            src="/social/email-open.svg"
            alt="email-open"
          />
          <h1 class="text-lg font-semibold mt-[22px] text-[#333333]">
            Select an item to read
          </h1>
          <p class="text-lg mt-1.5 text-[#707070]">Nothing is selected</p>
        </div>
      </div>
      <div
        v-else-if="showSpecificMessage && !showComposeSection"
        class="w-full h-[calc(100%-58px)] bg-[#F1F2F6] custom-scroll"
      >
        <NuxtPage />
      </div>
      <SourceHubMicrosoftComposeBox v-if="showComposeSection" />
    </div>
  </div>
</template>

<style scoped>
.page-number:hover .show-hide {
  display: flex;
}
.message-box:hover .checkbox {
  display: block !important;
}
.message-box:hover .profile-image {
  display: none !important;
}
</style>
