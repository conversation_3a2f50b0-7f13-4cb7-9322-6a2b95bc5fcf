<script setup lang="ts">
import { useStore } from 'vuex'

const store = useStore()
interface Option {
  id: number
  title: string
  selected: boolean
}

const options = ref<Option[]>([
  {
    id: 1,
    title: 'My Contacts',
    selected: true,
  },
  {
    id: 2,
    title: 'Contacts',
    selected: false,
  },
  {
    id: 3,
    title: 'Default Global Address List',
    selected: false,
  },
])
const chnageOption = (id: number) => {
  options.value.forEach((option: Option) => {
    if (option.id === id) {
      option.selected = true
    } else {
      option.selected = false
    }
  })
}
</script>

<template>
  <div
    class="bg-white max-w-[1280px] w-full h-[80%] py-4 rounded-2xl flex flex-col z-10 absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"
  >
    <div
      class="px-6 pb-4 flex justify-between items-center border-b border-[#F1F2F6]"
    >
      <p class="text-lg text-[#505050] font-semibold">Add recipients</p>
      <SharedIconHubEmailsCrossIcon
        @click.stop="store.commit('emails/SET_SHOW_ADD_RECIPIENTS', false)"
        class="w-4 h-4 cursor-pointer"
      />
    </div>
    <div
      class="grid grid-cols-[265px_1fr] flex-grow w-full border-b border-[#F1F2F6]"
    >
      <div class="flex flex-col space-y-2 py-3 px-4">
        <div
          class="py-0.5 rounded cursor-pointer"
          v-for="option in options"
          :key="option.id"
          @click="chnageOption(option.id)"
          :class="{ 'bg-[#F1F2F6]': option.selected }"
        >
          <div class="px-2">
            <p class="text-[#333333]">{{ option.title }}</p>
          </div>
        </div>
      </div>
      <div class="flex justify-center items-center bg-[#F1F2F6] py-3 px-4">
        <div class="flex flex-col !space-y-[22px]">
          <img
            class="w-[240px] h-[180px]"
            src="/images/icon/contact_icon.png"
            alt="contact_icon"
          />
          <p class="text-[#333333] text-center">List of Contract Is Empty</p>
        </div>
      </div>
    </div>
    <div class="w-full flex justify-end items-center space-x-2 px-6 pt-4">
      <button
        class="border border-[#4A71D4] text-[#4A71D4] font-semibold rounded-full flex justify-center items-center w-[100px] h-[35px]"
        @click.stop="store.commit('emails/SET_SHOW_ADD_RECIPIENTS', false)"
      >
        Cancel
      </button>
      <button
        class="bg-[#4A71D4] text-[#FFFFFF] font-semibold rounded-full flex justify-center items-center w-[100px] h-[35px]"
        @click.stop="store.commit('emails/SET_SHOW_ADD_RECIPIENTS', false)"
      >
        Save
      </button>
    </div>
  </div>
</template>
