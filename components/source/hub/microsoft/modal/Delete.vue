<script setup lang="ts">
interface ModalOptions {
  id: number
  border: boolean
  emptyDiv: boolean
  image: string
  title: string
  button: string
  opacity: boolean
}

interface Props {
  width?: string
  modalOptions: ModalOptions[]
}

const props = withDefaults(defineProps<Props>(), {
  width: '0px',
  modalOptions: [
    {
      id: 0,
      border: false,
      emptyDiv: false,
      image: '',
      title: '',
      button: '',
      opacity: false,
    },
  ],
})
</script>

<template>
  <div class="box-modal py-1 absolute" :style="{ '--modal-width': width }">
    <div
      v-for="modalOption in modalOptions"
      :key="modalOption.id"
      class="px-4 pr-3.5 py-1.5 hover:bg-[#F1F2F6] flex items-center !space-x-2"
      :class="modalOption.border ? 'border-b border-[#E3E3E3]' : ''"
    >
      <component v-if="modalOption.image" :is="modalOption.image" />
      <div v-if="modalOption.emptyDiv" class="min-w-[14px]"></div>
      <div class="flex-grow flex justify-between items-center">
        <p
          class="flex-grow line-clamp-1"
          :class="modalOption.opacity ? 'text-[#D2D2D2]' : 'text-[#525252]'"
        >
          {{ modalOption.title }}
        </p>
        <button v-if="modalOption.button" class="text-[#C5C5C5]">
          {{ modalOption.button }}
        </button>
      </div>
    </div>
  </div>
</template>

<style scoped>
.box-modal {
  width: var(--modal-width);
  background: #ffffff 0% 0% no-repeat padding-box;
  box-shadow: 2px 2px 4px #22283114;
  border-radius: 8px;
  z-index: 1;
}
</style>
