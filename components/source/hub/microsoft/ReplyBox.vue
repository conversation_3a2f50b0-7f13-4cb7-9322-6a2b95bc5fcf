<script setup lang="ts">
import { useStore } from 'vuex'

const store = useStore()

interface Props {
  setShowReplyBox?: (payload: MouseEvent) => void
  deleteSpecificComposeItem?: Function
  itemId?: number
  toPersonName?: string
}

const props = withDefaults(defineProps<Props>(), {
  setShowReplyBox: () => {},
  deleteSpecificComposeItem: (id: number) => {},
  itemId: 0,
  toPersonName: '',
})
const accountItem = computed(() => {
  return store.state.social.accountItem
})
const showComposeSection = computed(() => store.state.emails.showComposeSection)
</script>

<template>
  <div class="flex flex-col min-h-[264px]">
    <nav class="flex space-x-2 items-center">
      <div class="grid grid-cols-[40px,1fr] gap-x-4 items-center">
        <SourceAccountLogo :account-profile-pic="accountItem.profilePic" />
        <div v-if="!showComposeSection">
          <p class="text-[#333333] text-sm">
            To:
            <span v-if="toPersonName">{{ toPersonName }}</span>
          </p>
        </div>
      </div>
      <div v-if="showComposeSection" class="w-full text-[#707070]">
        <div class="w-full py-2 pr-4 border-b border-[#F1F2F6]">
          <input
            class="w-full outline-none border-none"
            type="text"
            placeholder="Recipients"
          />
        </div>
        <div class="w-full py-2 pr-4 mt-2 border-b border-[#F1F2F6]">
          <input
            class="w-full outline-none border-none"
            type="text"
            placeholder="Subject"
          />
        </div>
      </div>
    </nav>
    <div class="mt-3 flex-grow">
      <textarea
        class="w-full h-auto no-scrollbar resize-none textarea-style outline-none border-none bg-white text-[#333333]"
        name=""
        id=""
      ></textarea>
    </div>
    <div
      v-if="!showComposeSection"
      class="w-[30px] h-3.5 bg-[#F1F2F6] rounded-[5px] flex justify-center items-center"
    >
      <SharedIconHubEmailsThreeDotMenuIcon
        class="transform rotate-90"
        color="#525252"
      />
    </div>
    <div class="flex space-x-2 mt-4">
      <button
        class="flex justify-center items-center rounded h-[33px] bg-[#0F6CBC] text-white text-sm"
      >
        <div
          class="flex justify-center items-center px-4 border-r border-white !space-x-2"
        >
          <SharedIconHubMicrosoftSidebarSentItemsIcon color="#fff" />
          <p>Send</p>
        </div>
        <div class="flex justify-center items-center px-2">
          <SharedIconHubMicrosoftArrowIcon class="w-2 h-1" color="#fff" />
        </div>
      </button>
      <button
        class="flex items-center !space-x-2 border border-[#525252] h-[33px] px-4 py-2 rounded"
        @click="setShowReplyBox(), deleteSpecificComposeItem(itemId)"
      >
        <SharedIconHubMicrosoftSidebarDeleteItemsIcon />
        <p class="text-[#333333] text-sm">Discard</p>
      </button>
    </div>
    <!-- <div class="mt-4">
      <SourceHubEmailsInputModifier
        :setShowReplyBox="setShowReplyBox"
        :deleteSpecificComposeItem="deleteSpecificComposeItem"
        :itemId="itemId"
      />
    </div> -->
  </div>
</template>

<style lang="scss" scoped>
// .reply-box {
//   background: #ffffff 0% 0% no-repeat padding-box;
//   box-shadow: 1px 2px 8px #00000029;
//   border-radius: 8px;
// }
.textarea-style {
  field-sizing: content;
  width: 100%;
}
</style>
