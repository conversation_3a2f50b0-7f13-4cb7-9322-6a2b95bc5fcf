<template>
  <div
    class="flex my-4 px-2 py-4 rounded-lg"
    :class="comment.selected ? 'bg-[#DCEAFF]' : 'bg-white'"
    @click="replies.hideAllMenus"
  >
    <img
      class="w-8 min-w-8 h-8 min-h-8 mr-1 rounded-full"
      :src="
        comment.profileImageUrl
          ? comment.profileImageUrl
          : store.state.defaultImg
      "
      alt=""
    />
    <div class="w-full">
      <div
        :id="comment.id"
        class="px-4 py-1.5 font-medium rounded-lg"
        :class="comment.selected ? 'bg-white' : 'bg-[#EBEDF5]'"
      >
        <a
          v-if="comment.facebookUrl"
          :href="comment.facebookUrl"
          target="_blank"
          class="text-[#333333] text-sm hover:text-blue-500 hover:underline"
        >
          <span v-if="comment.name" class="font-semibold"
            >{{ comment.name }}
          </span>
        </a>
        <!-- <span class="font-bold"> - </span>
        <span v-if="comment.createdAt" class="text-ash-dark">
          <date-time
            :show-time="false"
            :datetime="comment.createdAt"
          ></date-time>
        </span> -->
        <p
          v-if="comment.text"
          v-nameIndicator="messageTagNames(comment.messageTags)"
          v-see-more.right="200"
          class="font-normal text-[#525252] break-all"
          v-html="comment.text"
        ></p>
        <!-- <div id="fb-root"></div>
        <script
          async
          defer
          crossorigin="anonymous"
          src="https://connect.facebook.net/en_GB/sdk.js#xfbml=1&version=v13.0&appId=482095120000076&autoLogAppEvents=1"
          nonce="ms5VurmK"
        ></script> -->
      </div>
      <img
        v-if="
          (comment.type === 'photo' ||
            comment.type === 'animated_image_share') &&
          (comment.archivedSourceUrl || comment.sourceUrl)
        "
        class="w-38 h-auto rounded-2xl mt-1 border shadow-sm"
        :src="
          comment.archivedSourceUrl
            ? comment.archivedSourceUrl
            : comment.sourceUrl
        "
        alt=""
      />
      <img
        v-if="comment.type === 'sticker' && comment.attachmentUrl"
        class="w-38 h-auto rounded-2xl mt-1 border shadow-sm"
        :src="comment.attachmentUrl"
        alt=""
      />
      <iframe
        v-else-if="comment.type === 'video_inline'"
        class="rounded-2xl mt-1"
        :src="`https://www.facebook.com/plugins/video.php?height=200&href=${comment.attachmentUrl}%2F&show_text=false&width=300&t=0`"
        width="300"
        height="200"
        style="border: none; overflow: hidden"
        scrolling="no"
        frameborder="0"
        allowfullscreen="true"
        allow="autoplay; clipboard-write; encrypted-media; picture-in-picture; web-share"
      ></iframe>
      <div class="flex !space-x-4 text-[#707070] text-xs my-2">
        <p>1h</p>
        <p
          class="font-bold"
          @click.stop="
            selelctedCommentReply(comment.name, comment.facebookUrl, comment.id)
          "
        >
          Reply
        </p>
      </div>
      <!-- </div> -->
      <SourceHubSocialsCommentsReplies
        v-if="comment.commentsCount || comment.selected"
        ref="replies"
        :comments="comment.comments"
        :mainComment="comment"
        :reply-person-name="replyPersonName"
        @selected-comment-reply="selelctedCommentReply"
      >
      </SourceHubSocialsCommentsReplies>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useStore } from 'vuex'

const props = defineProps({
  comment: {
    type: Object,
    default: null,
  },
})

const store = useStore()

const messageTagNames = (payload: any) => {
  let names = []
  if (payload) {
    const profile = JSON.parse(payload)
    names = profile.map((item: any) => {
      return item.name
    })
  }
  return names
}
const replyPersonName = ref<string>('')
const replies = ref(null)
const selelctedCommentReply = (
  name: string,
  facebookUrl: string,
  id: number,
) => {
  replies.value?.editor.commands.clearContent()
  replyPersonName.value = name
  store.commit('social/SET_SELECTED_COMMENT_FOR_REPLY', id)
  replies.value?.editor
    .chain()
    .focus()
    .insertContent([
      {
        type: 'text',
        text: replyPersonName.value,
        marks: [
          {
            type: 'link',
            attrs: {
              href: facebookUrl,
            },
          },
        ],
      },
      { type: 'text', text: ' ' }, // add space
    ])
    .run()
}
</script>

<style scoped lang="scss">
.tiptap-link {
  color: #333333;
  background-color: #dceaff;
}
</style>
