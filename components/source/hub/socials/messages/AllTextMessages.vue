<script setup lang="ts">
import { useStore } from 'vuex'
import { Editor, EditorContent } from '@tiptap/vue-3'
import StarterKit from '@tiptap/starter-kit'
import Placeholder from '@tiptap/extension-placeholder'

const store = useStore()
const router = useRouter()

const conversationUser = computed(() => store.state.social.conversationUser)
const conversationOwner = computed(() => store.state.social.conversationOwner)
const personLists = computed(() => store.state.social.personLists)
// Custom computed properties
const conversations = computed(() =>
  JSON.parse(JSON.stringify(store.getters['social/conversations'])),
)

const images = (archiveAttachments: any, attachments: any) => {
  let temp = []
  if (archiveAttachments.length > 0) {
    temp = archiveAttachments.filter(
      (x) =>
        x.mime_type === 'image/jpeg' ||
        x.mime_type === 'image/jpg' ||
        x.mime_type === 'image/png' ||
        x.mime_type === 'image/svg' ||
        x.mime_type === 'image/apng' ||
        x.mime_type === 'image/gif',
    )
  } else if (attachments.length > 0) {
    temp = attachments.filter(
      (x) =>
        x.mime_type === 'image/jpeg' ||
        x.mime_type === 'image/jpg' ||
        x.mime_type === 'image/png' ||
        x.mime_type === 'image/svg' ||
        x.mime_type === 'image/apng' ||
        x.mime_type === 'image/gif',
    )
  }
  return temp
}
const otherAttachments = (archiveAttachments: any, attachments: any) => {
  let temp = []
  if (archiveAttachments.length > 0) {
    temp = archiveAttachments.filter(
      (x) =>
        x.mime_type === 'video/mp4' ||
        x.mime_type === 'video/webm' ||
        x.mime_type === 'video/ogg' ||
        x.mime_type === 'audio/mpeg' ||
        x.mime_type === 'audio/mp3' ||
        x.mime_type === 'audio/wav',
    )
  } else if (attachments.length > 0) {
    temp = attachments.filter(
      (x) =>
        x.mime_type === 'video/mp4' ||
        x.mime_type === 'video/webm' ||
        x.mime_type === 'video/ogg' ||
        x.mime_type === 'audio/mpeg' ||
        x.mime_type === 'audio/mp3' ||
        x.mime_type === 'audio/wav',
    )
  }
  return temp
}

const specificMessageId = computed(() => store.state.social.specificId)
const accountItem = computed(() => {
  return store.state.social.accountItem
})
const messageSectionHeight = computed(() => {
  if (accountItem.value.provider === 'iMessage') {
    return 'h-[calc(100vh-530px)]'
  } else if (
    accountItem.value.provider === 'WhatsApp' ||
    accountItem.value.provider === 'Telegram'
  ) {
    return 'xl:h-[calc(100vh-530px)] h-[calc(100vh-585px)]'
  } else {
    return '2xl:h-[calc(100vh-530px)] h-[calc(100vh-585px)]'
  }
})
const messageWrapper = ref<HTMLElement | null>(null)
const showEmojiPicker = ref<boolean>(false)
const editor = ref<Editor | null>(null)
onMounted(async () => {
  await nextTick()
  if (messageWrapper.value) {
    messageWrapper.value.scrollTop = messageWrapper.value.scrollHeight
  }
  if (specificMessageId.value) {
    scrollToMessage(specificMessageId.value)
  }
  document.addEventListener('click', () => {
    showClipBoardMenuHandler(false)
    showEmojiPicker.value = false
  })
  editor.value = new Editor({
    // content: `<p>I'm running Tiptap with Vue.js. 🎉</p>`,
    extensions: [
      StarterKit,
      Placeholder.configure({
        // Use a placeholder:
        placeholder: 'Type your replay.',
      }), // Prevents auto-opening links
    ],
    editorProps: {
      attributes: {
        class:
          'w-full h-auto no-scrollbar resize-none textarea-style text-[#707070] bg-[#F1F2F6] text-lg outline-none border-none',
      },
    },
  })
})
onUnmounted(() => {
  document.removeEventListener('click', () => {
    showClipBoardMenuHandler(false)
    showEmojiPicker.value = false
  })
})
const scrollToMessage = async (id: number) => {
  await nextTick()
  const blogSection = document.getElementById(`${id}`)
  if (blogSection) {
    blogSection.scrollIntoView({ behavior: 'smooth' })
  }
}
const showClipBoardMenu = ref<boolean>(false)
const showClipBoardMenuHandler = (value: boolean) => {
  showClipBoardMenu.value = value
  showEmojiPicker.value = false
}
const toggleEmojiPicker = () => {
  showEmojiPicker.value = !showEmojiPicker.value
  showClipBoardMenu.value = false
}
</script>

<template>
  <div class="flex flex-col h-full overflow-visible">
    <div class="h-full overflow-visible">
      <div
        class="bg-white messageAvatarInfo p-4 border-b-[3px] border-[#F1F2F6]"
      >
        <div
          v-if="conversationUser || conversationOwner"
          class="flex space-x-2"
        >
          <div class="profileAvatar">
            <img
              v-if="conversationUser.profileImageUrl"
              class="w-13 h-13 rounded-full object-cover border border-gray-400"
              :src="conversationUser.profileImageUrl"
              :alt="`${conversationUser.name}'s Profile Picture`"
            />
            <template v-else>
              <div
                v-if="personLists.provider === 'Facebook'"
                class="w-13 h-13 rounded-full"
              >
                <SharedIconFacebookIcon
                  class="w-13 h-13 border border-gray-400"
                ></SharedIconFacebookIcon>
              </div>
              <SharedIconInstagramIcon
                v-if="personLists.provider === 'Instagram'"
                class="w-13 h-13 border border-gray-400"
              ></SharedIconInstagramIcon>
              <div
                v-if="personLists.provider === 'Twitter'"
                class="w-13 h-13 rounded-full"
              >
                <SharedIconTwitterIcon
                  class="w-13 h-13 border border-gray-400"
                ></SharedIconTwitterIcon>
              </div>
              <div
                v-if="personLists.provider === 'LinkedIn'"
                class="w-13 h-13 rounded-full"
              >
                <SharedIconLinkedinIcon
                  class="w-13 h-13 border border-gray-400"
                ></SharedIconLinkedinIcon>
              </div>
            </template>
          </div>
          <div class="flex flex-col space-y-0.5">
            <p class="text-[#525252] font-semibold text-lg">
              {{ conversationUser.name }}
            </p>
            <p class="text-[#707070] text-sm">+****************</p>
          </div>
        </div>
      </div>
      <!-- :id="messageId" -->
      <div
        ref="messageWrapper"
        class="messageWrapper text-lg 2xl:text-xl bg-white p-6 overflow-scroll custom-scroll"
        :class="[
          messageSectionHeight,
          accountItem.provider === 'WhatsApp'
            ? 'custom-whatsapp-scroll'
            : accountItem.provider === 'Telegram'
              ? 'custom-telegram-scroll'
              : 'custom-scroll',
        ]"
      >
        <template v-if="conversations.length > 0">
          <!-- <template v-if="showMoreMessageSkeleton">
            <div id="messageSkeleton" class="w-full">
              <div
                v-for="moreMessage in 5"
                :key="'more' + moreMessage"
                class="w-full"
              >
                <div class="send-right-wrapper flex flex-row-reverse">
                  <div class="send-right my-2 max-w-75">
                    <div class="bg-gray-500 h-6 w-26 rounded-full"></div>
                  </div>
                </div>

                <div class="receive-left-wrapper">
                  <div class="sender-image m-2 self-end">
                    <div
                      class="w-[50px] min-w-[50px] h-[50px] min-h-[50px] rounded-full object-cover border border-gray-400 bg-gray-500"
                    ></div>
                  </div>
                  <div class="receive-left space-y-1">
                    <div class="bg-gray-500 h-6 w-26 rounded-full"></div>
                  </div>
                </div>
              </div>
            </div>
          </template> -->
          <div
            v-for="(conversation, index) in conversations"
            :key="index"
            class="w-full"
          >
            <div
              v-if="!conversation.user"
              class="send-right-wrapper flex flex-row-reverse"
            >
              <div class="send-right flex flex-col text-white my-2 w-full">
                <div class="w-full flex justify-end py-2 text-gray-1700">
                  <DateTime
                    :datetime="conversation.date"
                    format="MMMM, dd yyyy, hh:mm aa"
                    :show-time="true"
                    class="whitespace-nowrap"
                  />
                </div>
                <div
                  v-for="(message, i) in conversation.messages"
                  :key="message.id"
                  class="max-w-75 self-end"
                >
                  <template v-if="message.text">
                    <div
                      :id="message.id"
                      class="line_break text-left my-0.5 max-w-300 rounded-lg receive-left-text"
                      :class="
                        accountItem.provider === 'WhatsApp'
                          ? 'receive-whatsapp-text'
                          : accountItem.provider === 'Telegram'
                            ? 'receive-telegram-text'
                            : ''
                      "
                    >
                      {{ message.text }}
                    </div>
                  </template>
                  <template v-if="message.provider === 'Facebook'">
                    <template
                      v-if="
                        images(message.archivedAttachments, message.attachments)
                          .length > 0
                      "
                    >
                      <div
                        class="grid gap-0.5 max-w-300 my-0.5"
                        :class="
                          images(
                            message.archivedAttachments,
                            message.attachments,
                          ).length < 5
                            ? 'grid-cols-2'
                            : 'grid-cols-3'
                        "
                      >
                        <div
                          v-for="(image, imageIndex) in images(
                            message.archivedAttachments,
                            message.attachments,
                          )"
                          :key="imageIndex"
                          class="bg-white rounded-xl"
                          :class="
                            images(
                              message.archivedAttachments,
                              message.attachments,
                            ).length > 1
                              ? 'col-span-1 max-h-52'
                              : 'col-span-2 max-w-300'
                          "
                        >
                          <img
                            v-if="
                              image.mime_type === 'image/jpeg' ||
                              image.mime_type === 'image/jpg' ||
                              image.mime_type === 'image/png' ||
                              image.mime_type === 'image/svg' ||
                              image.mime_type === 'image/apng' ||
                              image.mime_type === 'image/gif'
                            "
                            class="object-fill rounded-xl max-h-full max-w-full w-full shadow-sm border"
                            :class="
                              images(
                                message.archivedAttachments,
                                message.attachments,
                              ).length > 1
                                ? 'max-h-full max-w-full w-full'
                                : ''
                            "
                            :src="image.url"
                            alt
                          />
                        </div>
                      </div>
                    </template>
                    <template
                      v-else-if="
                        otherAttachments(
                          message.archivedAttachments,
                          message.attachments,
                        ).length > 0
                      "
                    >
                      <div class="grid grid-cols-2 max-w-300 my-0.5">
                        <div
                          v-for="(image, imageIndex) in otherAttachments(
                            message.archivedAttachments,
                            message.attachments,
                          )"
                          :key="imageIndex"
                          class="bg-white rounded-xl col-span-2 max-w-300"
                        >
                          <video
                            v-if="
                              image.mime_type === 'video/mp4' ||
                              image.mime_type === 'video/webm'
                            "
                            class="w-full rounded-xl shadow-sm"
                            height="400"
                            controls="controls"
                          >
                            <source :src="image.url" />
                            <source :src="image.preview_url" />
                          </video>
                          <audio
                            v-else-if="
                              image.mime_type === 'audio/mpeg' ||
                              image.mime_type === 'audio/mp3' ||
                              image.mime_type === 'audio/wav'
                            "
                            class="send-audio w-full"
                            controls
                          >
                            <source :src="image.url" :type="image.mime_type" />
                          </audio>
                        </div>
                      </div>
                    </template>
                    <template v-if="message.archivedSticker || message.sticker">
                      <div class="grid grid-cols-2 my-0.5">
                        <div class="col-span-2 max-w-120">
                          <img
                            class="object-cover max-h-full max-w-full w-full"
                            :src="
                              message.archivedSticker
                                ? message.archivedSticker
                                : message.sticker
                            "
                            alt
                          />
                        </div>
                      </div>
                    </template>
                    <template v-if="message.archivedAttachments.length === 1">
                      <template
                        v-for="(
                          image, imageIndex
                        ) in message.archivedAttachments"
                      >
                        <div
                          v-if="
                            image.mime_type !== 'image/jpeg' &&
                            image.mime_type !== 'image/jpg' &&
                            image.mime_type !== 'image/png' &&
                            image.mime_type !== 'image/svg' &&
                            image.mime_type !== 'image/apng' &&
                            image.mime_type !== 'image/gif' &&
                            image.mime_type !== 'video/mp4' &&
                            image.mime_type !== 'video/webm' &&
                            image.mime_type !== 'video/ogg' &&
                            image.mime_type !== 'audio/mpeg' &&
                            image.mime_type !== 'audio/mp3' &&
                            image.mime_type !== 'audio/wav'
                          "
                          :key="imageIndex"
                          class="my-0.5"
                        >
                          <a
                            v-if="image.mime_type !== 'image/jpeg'"
                            class="flex space-x-2 items-center flex-nowrap"
                            :href="image.url"
                          >
                            <svg
                              class="svg-icon"
                              style="
                                width: 20px;
                                height: 20px;
                                vertical-align: middle;
                                overflow: hidden;
                                fill: #ffffff;
                              "
                              viewBox="0 0 1024 1024"
                              version="1.1"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <path
                                d="M481.706667 670.293333a42.666667 42.666667 0 0 0 14.08 8.96 42.666667 42.666667 0 0 0 32.426666 0 42.666667 42.666667 0 0 0 14.08-8.96l128-128a42.666667 42.666667 0 0 0-60.586666-60.586666L554.666667 537.173333V384a42.666667 42.666667 0 0 0-85.333334 0v153.173333l-55.04-55.466666a42.666667 42.666667 0 0 0-60.586666 0 42.666667 42.666667 0 0 0 0 60.586666zM512 938.666667A426.666667 426.666667 0 1 0 85.333333 512a426.666667 426.666667 0 0 0 426.666667 426.666667z m0-768a341.333333 341.333333 0 1 1-341.333333 341.333333 341.333333 341.333333 0 0 1 341.333333-341.333333z"
                              />
                            </svg>
                            <span
                              class="line_break hover:underline text-left receive-left-text"
                              :class="
                                accountItem.provider === 'WhatsApp'
                                  ? 'receive-whatsapp-text'
                                  : accountItem.provider === 'Telegram'
                                    ? 'receive-telegram-text'
                                    : ''
                              "
                              >{{ image.name }}
                            </span>
                          </a>
                        </div>
                      </template>
                    </template>
                    <template v-else-if="message.attachments.length === 1">
                      <template
                        v-for="(image, imageIndex) in message.attachments"
                      >
                        <div
                          v-if="
                            image.mime_type !== 'image/jpeg' &&
                            image.mime_type !== 'image/jpg' &&
                            image.mime_type !== 'image/png' &&
                            image.mime_type !== 'image/svg' &&
                            image.mime_type !== 'image/apng' &&
                            image.mime_type !== 'image/gif' &&
                            image.mime_type !== 'video/mp4' &&
                            image.mime_type !== 'video/webm' &&
                            image.mime_type !== 'video/ogg' &&
                            image.mime_type !== 'audio/mpeg' &&
                            image.mime_type !== 'audio/mp3' &&
                            image.mime_type !== 'audio/wav'
                          "
                          :key="imageIndex"
                          class="my-0.5"
                        >
                          <a
                            v-if="image.mime_type !== 'image/jpeg'"
                            class="flex space-x-2 items-center"
                            :href="image.url"
                          >
                            <svg
                              class="svg-icon"
                              style="
                                width: 20px;
                                height: 20px;
                                vertical-align: middle;
                                overflow: hidden;
                                fill: #ffffff;
                              "
                              viewBox="0 0 1024 1024"
                              version="1.1"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <path
                                d="M481.706667 670.293333a42.666667 42.666667 0 0 0 14.08 8.96 42.666667 42.666667 0 0 0 32.426666 0 42.666667 42.666667 0 0 0 14.08-8.96l128-128a42.666667 42.666667 0 0 0-60.586666-60.586666L554.666667 537.173333V384a42.666667 42.666667 0 0 0-85.333334 0v153.173333l-55.04-55.466666a42.666667 42.666667 0 0 0-60.586666 0 42.666667 42.666667 0 0 0 0 60.586666zM512 938.666667A426.666667 426.666667 0 1 0 85.333333 512a426.666667 426.666667 0 0 0 426.666667 426.666667z m0-768a341.333333 341.333333 0 1 1-341.333333 341.333333 341.333333 341.333333 0 0 1 341.333333-341.333333z"
                              />
                            </svg>
                            <span
                              class="line_break hover:underline text-left receive-left-text"
                              :class="
                                accountItem.provider === 'WhatsApp'
                                  ? 'receive-whatsapp-text'
                                  : accountItem.provider === 'Telegram'
                                    ? 'receive-telegram-text'
                                    : ''
                              "
                              >{{ image.name }}
                            </span>
                          </a>
                        </div>
                      </template>
                    </template>
                    <template
                      v-else-if="message.archivedAttachments.length > 0"
                    >
                      <template
                        v-for="(
                          image, imageIndex
                        ) in message.archivedAttachments"
                      >
                        <div
                          v-if="
                            image.mime_type !== 'image/jpeg' &&
                            image.mime_type !== 'image/jpg' &&
                            image.mime_type !== 'image/png' &&
                            image.mime_type !== 'image/svg' &&
                            image.mime_type !== 'image/apng' &&
                            image.mime_type !== 'image/gif' &&
                            image.mime_type !== 'video/mp4' &&
                            image.mime_type !== 'video/webm' &&
                            image.mime_type !== 'video/ogg' &&
                            image.mime_type !== 'audio/mpeg' &&
                            image.mime_type !== 'audio/mp3' &&
                            image.mime_type !== 'audio/wav'
                          "
                          :key="imageIndex"
                          class="rounded-lg"
                        >
                          <a
                            v-if="image.mime_type !== 'image/jpeg'"
                            class="flex space-x-2 items-center"
                            :href="image.url"
                          >
                            <svg
                              class="svg-icon"
                              style="
                                width: 20px;
                                height: 20px;
                                vertical-align: middle;
                                overflow: hidden;
                                fill: #ffffff;
                              "
                              viewBox="0 0 1024 1024"
                              version="1.1"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <path
                                d="M481.706667 670.293333a42.666667 42.666667 0 0 0 14.08 8.96 42.666667 42.666667 0 0 0 32.426666 0 42.666667 42.666667 0 0 0 14.08-8.96l128-128a42.666667 42.666667 0 0 0-60.586666-60.586666L554.666667 537.173333V384a42.666667 42.666667 0 0 0-85.333334 0v153.173333l-55.04-55.466666a42.666667 42.666667 0 0 0-60.586666 0 42.666667 42.666667 0 0 0 0 60.586666zM512 938.666667A426.666667 426.666667 0 1 0 85.333333 512a426.666667 426.666667 0 0 0 426.666667 426.666667z m0-768a341.333333 341.333333 0 1 1-341.333333 341.333333 341.333333 341.333333 0 0 1 341.333333-341.333333z"
                              />
                            </svg>
                            <span
                              class="line_break hover:underline text-left receive-left-text"
                              :class="
                                accountItem.provider === 'WhatsApp'
                                  ? 'receive-whatsapp-text'
                                  : accountItem.provider === 'Telegram'
                                    ? 'receive-telegram-text'
                                    : ''
                              "
                              >{{ image.name }}
                            </span>
                          </a>
                        </div>
                      </template>
                    </template>
                    <template v-else-if="message.attachments.length > 0">
                      <template
                        v-for="(image, imageIndex) in message.attachments"
                      >
                        <div
                          v-if="
                            image.mime_type !== 'image/jpeg' &&
                            image.mime_type !== 'image/jpg' &&
                            image.mime_type !== 'image/png' &&
                            image.mime_type !== 'image/svg' &&
                            image.mime_type !== 'image/apng' &&
                            image.mime_type !== 'image/gif' &&
                            image.mime_type !== 'video/mp4' &&
                            image.mime_type !== 'video/webm' &&
                            image.mime_type !== 'video/ogg' &&
                            image.mime_type !== 'audio/mpeg' &&
                            image.mime_type !== 'audio/mp3' &&
                            image.mime_type !== 'audio/wav'
                          "
                          :key="imageIndex"
                          class="rounded-lg"
                        >
                          <a
                            v-if="image.mime_type !== 'image/jpeg'"
                            class="flex space-x-2 items-center"
                            :href="image.url"
                          >
                            <svg
                              class="svg-icon"
                              style="
                                width: 20px;
                                height: 20px;
                                vertical-align: middle;
                                overflow: hidden;
                                fill: #ffffff;
                              "
                              viewBox="0 0 1024 1024"
                              version="1.1"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <path
                                d="M481.706667 670.293333a42.666667 42.666667 0 0 0 14.08 8.96 42.666667 42.666667 0 0 0 32.426666 0 42.666667 42.666667 0 0 0 14.08-8.96l128-128a42.666667 42.666667 0 0 0-60.586666-60.586666L554.666667 537.173333V384a42.666667 42.666667 0 0 0-85.333334 0v153.173333l-55.04-55.466666a42.666667 42.666667 0 0 0-60.586666 0 42.666667 42.666667 0 0 0 0 60.586666zM512 938.666667A426.666667 426.666667 0 1 0 85.333333 512a426.666667 426.666667 0 0 0 426.666667 426.666667z m0-768a341.333333 341.333333 0 1 1-341.333333 341.333333 341.333333 341.333333 0 0 1 341.333333-341.333333z"
                              />
                            </svg>
                            <span
                              class="line_break hover:underline text-left receive-left-text"
                              :class="
                                accountItem.provider === 'WhatsApp'
                                  ? 'receive-whatsapp-text'
                                  : accountItem.provider === 'Telegram'
                                    ? 'receive-telegram-text'
                                    : ''
                              "
                              >{{ image.name }}
                            </span>
                          </a>
                        </div>
                      </template>
                    </template>
                  </template>
                  <template v-else>
                    <template v-if="message.archivedAttachments.length > 0">
                      <div class="grid gap-0.5 grid-cols-2 my-0.5">
                        <div
                          v-for="(
                            image, imageIndex
                          ) in message.archivedAttachments"
                          :key="imageIndex"
                          class="bg-white rounded-xl col-span-2 max-w-300"
                        >
                          <img
                            class="object-cover rounded-xl max-h-full max-w-full w-full shadow-sm border"
                            :class="
                              message.archivedAttachments.length > 1
                                ? 'max-h-full max-w-full w-full'
                                : ''
                            "
                            :src="image"
                            alt
                          />
                        </div>
                      </div>
                    </template>
                    <template v-else-if="message.attachments.length > 0">
                      <div class="grid gap-0.5 grid-cols-2 my-0.5">
                        <div
                          v-for="(image, imageIndex) in message.attachments"
                          :key="imageIndex"
                          class="bg-white rounded-xl col-span-2 max-w-300"
                        >
                          <img
                            class="object-cover rounded-xl max-h-full max-w-full w-full shadow-sm border"
                            :class="
                              message.attachments.length > 1
                                ? 'max-h-full max-w-full w-full'
                                : ''
                            "
                            :src="image"
                            alt
                          />
                        </div>
                      </div>
                    </template>
                  </template>
                </div>
              </div>
            </div>

            <div
              v-else-if="conversation.user"
              class="receive-left-wrapp flex flex-col my-2 max-w-75"
            >
              <div class="flex items-center space-x-2">
                <div class="sender-image self-end">
                  <img
                    v-if="conversationUser.profileImageUrl"
                    class="w-[50px] min-w-[50px] h-[50px] min-h-[50px] rounded-full object-cover border border-gray-400"
                    :src="conversationUser.profileImageUrl"
                    :alt="`${
                      conversationUser.name
                        ? conversationUser.name
                        : conversationOwner.username
                    }'s Profile Picture`"
                  />
                  <template v-else>
                    <div
                      v-if="personLists.provider === 'Facebook'"
                      class="w-[50px] min-w-[50px] h-[50px] min-h-[50px] rounded-full"
                    >
                      <SharedIconFacebookIcon
                        class="w-[50px] min-w-[50px] h-[50px] min-h-[50px] border border-gray-400"
                      ></SharedIconFacebookIcon>
                    </div>
                    <SharedIconInstagramIcon
                      v-if="personLists.provider === 'Instagram'"
                      class="w-[50px] min-w-[50px] h-[50px] min-h-[50px] border border-gray-400"
                    ></SharedIconInstagramIcon>
                    <div
                      v-if="personLists.provider === 'Twitter'"
                      class="w-[50px] min-w-[50px] h-[50px] min-h-[50px] rounded-full"
                    >
                      <SharedIconTwitterIcon
                        class="w-[50px] min-w-[50px] h-[50px] min-h-[50px] border border-gray-400"
                      ></SharedIconTwitterIcon>
                    </div>
                    <div
                      v-if="personLists.provider === 'LinkedIn'"
                      class="w-[50px] min-w-[50px] h-[50px] min-h-[50px] rounded-full"
                    >
                      <SharedIconLinkedinIcon
                        class="w-[50px] min-w-[50px] h-[50px] min-h-[50px] border border-gray-400"
                      ></SharedIconLinkedinIcon>
                    </div>
                  </template>
                </div>
                <div class="receive-left text-gray-1200 space-y-1">
                  <div class="w-full py-2 text-gray-1700 flex justify-start">
                    <p>
                      <DateTime
                        :datetime="conversation.date"
                        format="MMMM, dd yyyy, hh:mm aa"
                        :show-time="true"
                        class="whitespace-nowrap"
                      />
                    </p>
                  </div>
                  <div
                    v-for="(message, i) in conversation.messages"
                    :key="message.id"
                  >
                    <template v-if="message.text">
                      <div
                        :id="message.id"
                        class="line_break text-left max-w-300 rounded-lg send-right-text"
                      >
                        {{ message.text }}
                      </div>
                    </template>
                    <template v-if="message.provider === 'Facebook'">
                      <template v-if="message.archivedAttachments.length > 0">
                        <div
                          class="grid gap-0.5 max-w-300"
                          :class="
                            message.archivedAttachments.length < 5
                              ? 'grid-cols-2'
                              : 'grid-cols-3'
                          "
                        >
                          <div
                            v-for="(
                              image, imageIndex
                            ) in message.archivedAttachments"
                            :key="imageIndex"
                            class="bg-white rounded-xl"
                            :class="
                              message.archivedAttachments.length > 1 &&
                              image.mime_type !== 'video/mp4'
                                ? 'col-span-1 max-h-52'
                                : 'col-span-2 max-w-300'
                            "
                          >
                            <img
                              v-if="
                                image.mime_type === 'image/jpeg' ||
                                image.mime_type === 'image/jpg' ||
                                image.mime_type === 'image/png' ||
                                image.mime_type === 'image/svg' ||
                                image.mime_type === 'image/apng' ||
                                image.mime_type === 'image/gif'
                              "
                              class="object-fill rounded-xl max-h-full max-w-full w-full shadow-sm border"
                              :class="
                                message.archivedAttachments.length > 1
                                  ? 'max-h-full max-w-full w-full'
                                  : ''
                              "
                              :src="image.url"
                              alt
                            />
                            <video
                              v-else-if="
                                image.mime_type === 'video/mp4' ||
                                image.mime_type === 'video/webm'
                              "
                              class="w-full rounded-xl shadow-sm"
                              height="400"
                              controls="controls"
                            >
                              <source :src="image.url" />
                              <source :src="image.preview_url" />
                            </video>
                            <audio
                              v-else-if="
                                image.mime_type === 'audio/mpeg' ||
                                image.mime_type === 'audio/mp3' ||
                                image.mime_type === 'audio/wav'
                              "
                              class="receive-audio w-full"
                              controls
                            >
                              <source
                                :src="image.url"
                                :type="image.mime_type"
                              />
                            </audio>
                          </div>
                        </div>
                      </template>
                      <template v-else-if="message.attachments.length > 0">
                        <div
                          class="grid gap-0.5 max-w-300"
                          :class="
                            message.attachments.length < 5
                              ? 'grid-cols-2'
                              : 'grid-cols-3'
                          "
                        >
                          <div
                            v-for="(image, imageIndex) in message.attachments"
                            :key="imageIndex"
                            class="bg-white rounded-xl"
                            :class="
                              message.attachments.length > 1 &&
                              image.mime_type !== 'video/mp4'
                                ? 'col-span-1 max-h-52'
                                : 'col-span-2 max-w-300'
                            "
                          >
                            <img
                              v-if="
                                image.mime_type === 'image/jpeg' ||
                                image.mime_type === 'image/jpg' ||
                                image.mime_type === 'image/png' ||
                                image.mime_type === 'image/svg' ||
                                image.mime_type === 'image/apng' ||
                                image.mime_type === 'image/gif'
                              "
                              class="object-fill rounded-xl max-h-full max-w-full w-full shadow-sm border"
                              :class="
                                message.attachments.length > 1
                                  ? 'max-h-full max-w-full w-full'
                                  : ''
                              "
                              :src="image.url"
                              alt
                            />
                            <video
                              v-else-if="
                                image.mime_type === 'video/mp4' ||
                                image.mime_type === 'video/webm'
                              "
                              class="w-full rounded-xl shadow-sm"
                              height="400"
                              controls="controls"
                            >
                              <source :src="image.url" />
                              <source :src="image.preview_url" />
                            </video>
                            <audio
                              v-else-if="
                                image.mime_type === 'audio/mpeg' ||
                                image.mime_type === 'audio/mp3' ||
                                image.mime_type === 'audio/wav'
                              "
                              class="receive-audio w-full"
                              controls
                            >
                              <source
                                :src="image.url"
                                :type="image.mime_type"
                              />
                            </audio>
                          </div>
                        </div>
                      </template>
                      <template
                        v-if="message.archivedSticker || message.sticker"
                      >
                        <div class="grid grid-cols-2">
                          <div class="col-span-2 max-w-120">
                            <img
                              class="object-cover max-h-full max-w-full w-full"
                              :src="
                                message.archivedSticker
                                  ? message.archivedSticker
                                  : message.sticker
                              "
                              alt
                            />
                          </div>
                        </div>
                      </template>
                      <template v-if="message.archivedAttachments.length > 0">
                        <template
                          v-for="(
                            image, imageIndex
                          ) in message.archivedAttachments"
                        >
                          <div
                            v-if="
                              image.mime_type !== 'image/jpeg' &&
                              image.mime_type !== 'image/jpg' &&
                              image.mime_type !== 'image/png' &&
                              image.mime_type !== 'image/svg' &&
                              image.mime_type !== 'image/apng' &&
                              image.mime_type !== 'image/gif' &&
                              image.mime_type !== 'video/mp4' &&
                              image.mime_type !== 'video/webm' &&
                              image.mime_type !== 'video/ogg' &&
                              image.mime_type !== 'audio/mpeg' &&
                              image.mime_type !== 'audio/mp3' &&
                              image.mime_type !== 'audio/wav'
                            "
                            :key="imageIndex"
                            class="rounded-lg"
                          >
                            <a
                              v-if="image.mime_type !== 'image/jpeg'"
                              class="flex space-x-2 items-center"
                              :href="image.url"
                            >
                              <svg
                                class="svg-icon"
                                style="
                                  width: 20px;
                                  height: 20px;
                                  vertical-align: middle;
                                  overflow: hidden;
                                  fill: currentColor;
                                "
                                viewBox="0 0 1024 1024"
                                version="1.1"
                                xmlns="http://www.w3.org/2000/svg"
                              >
                                <path
                                  d="M481.706667 670.293333a42.666667 42.666667 0 0 0 14.08 8.96 42.666667 42.666667 0 0 0 32.426666 0 42.666667 42.666667 0 0 0 14.08-8.96l128-128a42.666667 42.666667 0 0 0-60.586666-60.586666L554.666667 537.173333V384a42.666667 42.666667 0 0 0-85.333334 0v153.173333l-55.04-55.466666a42.666667 42.666667 0 0 0-60.586666 0 42.666667 42.666667 0 0 0 0 60.586666zM512 938.666667A426.666667 426.666667 0 1 0 85.333333 512a426.666667 426.666667 0 0 0 426.666667 426.666667z m0-768a341.333333 341.333333 0 1 1-341.333333 341.333333 341.333333 341.333333 0 0 1 341.333333-341.333333z"
                                />
                              </svg>
                              <span
                                class="line_break hover:underline text-left send-right-text"
                                >{{ image.name }}</span
                              >
                            </a>
                          </div>
                        </template>
                      </template>
                      <template v-else-if="message.attachments.length > 0">
                        <template
                          v-for="(image, imageIndex) in message.attachments"
                        >
                          <div
                            v-if="
                              image.mime_type !== 'image/jpeg' &&
                              image.mime_type !== 'image/jpg' &&
                              image.mime_type !== 'image/png' &&
                              image.mime_type !== 'image/svg' &&
                              image.mime_type !== 'image/apng' &&
                              image.mime_type !== 'image/gif' &&
                              image.mime_type !== 'video/mp4' &&
                              image.mime_type !== 'video/webm' &&
                              image.mime_type !== 'video/ogg' &&
                              image.mime_type !== 'audio/mpeg' &&
                              image.mime_type !== 'audio/mp3' &&
                              image.mime_type !== 'audio/wav'
                            "
                            :key="imageIndex"
                            class="rounded-lg"
                          >
                            <a
                              v-if="image.mime_type !== 'image/jpeg'"
                              class="flex space-x-2 items-center"
                              :href="image.url"
                            >
                              <svg
                                class="svg-icon"
                                style="
                                  width: 20px;
                                  height: 20px;
                                  vertical-align: middle;
                                  overflow: hidden;
                                  fill: currentColor;
                                "
                                viewBox="0 0 1024 1024"
                                version="1.1"
                                xmlns="http://www.w3.org/2000/svg"
                              >
                                <path
                                  d="M481.706667 670.293333a42.666667 42.666667 0 0 0 14.08 8.96 42.666667 42.666667 0 0 0 32.426666 0 42.666667 42.666667 0 0 0 14.08-8.96l128-128a42.666667 42.666667 0 0 0-60.586666-60.586666L554.666667 537.173333V384a42.666667 42.666667 0 0 0-85.333334 0v153.173333l-55.04-55.466666a42.666667 42.666667 0 0 0-60.586666 0 42.666667 42.666667 0 0 0 0 60.586666zM512 938.666667A426.666667 426.666667 0 1 0 85.333333 512a426.666667 426.666667 0 0 0 426.666667 426.666667z m0-768a341.333333 341.333333 0 1 1-341.333333 341.333333 341.333333 341.333333 0 0 1 341.333333-341.333333z"
                                />
                              </svg>
                              <span
                                class="line_break hover:underline text-left send-right-text"
                                >{{ image.name }}</span
                              >
                            </a>
                          </div>
                        </template>
                      </template>
                    </template>
                    <template v-else>
                      <template v-if="message.archivedAttachments.length > 0">
                        <div class="grid gap-0.5 grid-cols-2">
                          <div
                            v-for="(
                              image, imageIndex
                            ) in message.archivedAttachments"
                            :key="imageIndex"
                            class="bg-white rounded-xl col-span-2 max-w-300"
                          >
                            <img
                              class="object-cover rounded-xl max-h-full max-w-full w-full shadow-sm border"
                              :class="
                                message.archivedAttachments.length > 1
                                  ? 'max-h-full max-w-full w-full'
                                  : ''
                              "
                              :src="image"
                              alt
                            />
                          </div>
                        </div>
                      </template>
                      <template v-else-if="message.attachments.length > 0">
                        <div class="grid gap-0.5 grid-cols-2">
                          <div
                            v-for="(image, imageIndex) in message.attachments"
                            :key="imageIndex"
                            class="bg-white rounded-xl col-span-2 max-w-300"
                          >
                            <img
                              class="object-cover rounded-xl max-h-full max-w-full w-full shadow-sm border"
                              :class="
                                message.attachments.length > 1
                                  ? 'max-h-full max-w-full w-full'
                                  : ''
                              "
                              :src="image"
                              alt
                            />
                          </div>
                        </div>
                      </template>
                    </template>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </template>
        <div
          v-if="conversations.length === 0"
          class="flex justify-center items-center w-full message-height text-gray-1000 text-lg font-semibold"
        >
          <p>No data found</p>
        </div>
      </div>
      <div class="relative flex space-x-2 items-center h-[80px] pl-4 pr-6">
        <img
          width="28"
          height="28"
          src="/social/messages/button-atatchment.svg"
          alt="button-atatchment"
        />
        <div
          class="flex-grow py-2 px-6 flex rounded-[20px] bg-[#F1F2F6] justify-between items-center overflow-hidden"
        >
          <!-- max-[1920px]:h-[35.5vh] max-[1920px]:top-[-34.5vh] -->
          <SourceHubSocialsClipBoardMenu
            v-if="showClipBoardMenu"
            class="absolute w-[calc(100%-120px)] h-[360px] max-h-[360px] top-[-358px] left-1/2 -translate-x-[51.7%]"
            v-model:showClipBoardMenu="showClipBoardMenu"
            @click.stop=""
          />
          <EmojiPicker
            v-if="showEmojiPicker"
            @click.stop=""
            :editor="editor"
            class="absolute top-[-326px] right-[80px]"
          />
          <!-- <input
            class="text-[#707070] bg-[#F1F2F6] text-lg outline-none border-none"
            type="text"
            placeholder="Type your reply."
          /> -->
          <editor-content
            class="w-full max-h-[28px] overflow-y-auto message-text-editor"
            :editor="editor"
            @click.stop=""
          />
          <div class="flex items-center space-x-2">
            <img
              width="20"
              height="20"
              src="/social/messages/icon-emo.svg"
              alt="icon-emo"
              @click.stop="toggleEmojiPicker"
              class="cursor-pointer"
            />
            <img
              width="20"
              height="20"
              src="/social/messages/icon-clipboard.svg"
              alt="icon-clipboard"
              class="cursor-pointer"
              @click.stop="showClipBoardMenuHandler(true)"
            />
          </div>
        </div>
        <img
          v-if="accountItem.provider === 'WhatsApp'"
          width="38"
          height="38"
          src="/social/messages/button-whatsapp-send.svg"
          alt="button-send"
        />
        <img
          v-else-if="accountItem.provider === 'Telegram'"
          width="38"
          height="38"
          src="/social/messages/button-telegram-send.svg"
          alt="button-send"
        />
        <img
          v-else
          width="38"
          height="38"
          src="/social/messages/button-send.svg"
          alt="button-send"
        />
      </div>
      <!-- <div
        v-if="currentComp === 'Instagram' && hideText($config.public.workflow)"
        class="rounded-b-3xl message-border-radius bg-white w-full"
      >
        <div class="message-border w-auto pl-3 pr-4 mx-2.5 mb-2.5">
          <div class="flex space-x-2 items-center h-9">
            <SmileFace
              class="min-width cursor-pointer"
              @click.native="toggleEmojiPicker"
            ></SmileFace>
            <input
              v-model="emojisOutput"
              type="text"
              class="message-placeholder text-gray-1200 px-2 pb-0.5 border-none outline-none flex-grow"
              placeholder="Message…"
            />
            <div v-show="showEmojiPicker">
              <Picker
                :data="emojiIndex"
                set="twitter"
                :show-preview="false"
                :style="{
                  position: 'absolute',
                  bottom: '38px',
                  left: '0px',
                  width: '100%',
                  height: '80%',
                }"
                @select="showEmoji"
              />
            </div>
            <ImageIcon class="min-width" @click.native="onPickFile"></ImageIcon>
            <input
              ref="fileInput"
              type="file"
              style="display: none"
              accept="image/*"
              @change="onFilePicked"
            />
            <LoveIcon
              class="min-width"
              @click.native="sendHeartEmoji"
            ></LoveIcon>
          </div>
        </div>
      </div> -->
    </div>
  </div>
</template>

<style scoped>
.send-right-text {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  padding-left: 0.75rem;
  padding-right: 0.75rem;
  background: #ebedf5;
  border-radius: 8px;
  opacity: 1;
  width: -moz-fit-content;
  width: fit-content;
  color: #333333;
}
.receive-left-text {
  padding-top: 0.625rem;
  padding-bottom: 0.625rem;
  padding-left: 0.875rem;
  padding-right: 0.875rem;
  background: #0084ff;
  border-radius: 8px;
  opacity: 1;
  width: -moz-fit-content;
  width: fit-content;
  color: white;
}
.receive-whatsapp-text {
  background: #d8fdd2;
  color: #333333;
}
.receive-telegram-text {
  background: #b2e5ff;
  color: #333333;
}
.max-w-300 {
  max-width: 100%;
}
.textarea-style {
  field-sizing: content;
  width: 100%;
}
</style>
