<template>
  <div
    v-if="singlePost"
    class="w-full bg-white mb-3 rounded-lg overflow-hidden pt-3.5"
  >
    <div
      v-if="singlePost.mentionText"
      class="px-4"
      v-html="singlePost.mentionText"
    ></div>
    <div
      :id="singlePost.id"
      class="card h-auto cursor-pointer overflow-hidden flex flex-col"
      :class="singlePost.type !== 'reel' ? 'py-3' : 'py-0'"
      :style="{ '--scrollColor': scrollColor }"
    >
      <div v-if="singlePost.type !== 'reel'" class="flex px-4">
        <SharedIconFacebookIcon
          v-if="!singlePost.profileImageUrl"
          class="w-10 h-10 rounded-full border border-gray-400"
        ></SharedIconFacebookIcon>
        <img
          height="40"
          width="40"
          :src="singlePost.profileImageUrl"
          class="rounded-full profile-image"
          alt="dp"
          srcset=""
        />
        <div class="ml-1 flex flex-col space-y-0.5">
          <h2 class="profile-name">
            {{ singlePost.profilename }}
            <span v-if="singlePost.placeName">
              <span
                v-if="singlePost.placeName.includes(singlePost.placeCity)"
                class="text-gray-light text-base"
              >
                is in
              </span>
              <span v-else class="text-gray-light text-base"> is at </span
              >{{ singlePost.placeName }}</span
            >
            <span
              v-if="singlePost?.tags && singlePost?.tags.length > 0"
              class="space-x-1"
            >
              <span class="text-[#707070] font-normal">is with</span>
              <template v-if="singlePost.tags.length > 2">
                <span>{{ singlePost.tags[0].name }}</span>
                <span class="text-[#707070] font-normal">and</span>
                <span>{{ singlePost.tags.length - 1 }} others</span>
              </template>
              <template v-else>
                <span v-for="(tag, index) in singlePost.tags" :key="tag.id">
                  {{ tag.name
                  }}<span
                    class="text-[#707070] font-normal"
                    v-if="index < singlePost.tags.length - 1"
                  >
                    and</span
                  >
                </span>
              </template>
            </span>
          </h2>

          <div class="flex items-center space-x-1">
            <date-time
              class="text-[#525252] text-xs"
              :datetime="
                singlePost.updatedAt
                  ? singlePost.updatedAt
                  : singlePost.createdAt
                    ? singlePost.createdAt
                    : ''
              "
              format="MMMM dd, yyyy"
              :show-time="false"
              :friendly="false"
            ></date-time>
            <span
              v-if="
                singlePost.placeCity &&
                !singlePost.placeName.includes(singlePost.placeCity)
              "
              class="text-xs text-gray-light"
            >
              <span> · </span>
              {{ singlePost.placeCity }}
            </span>

            <span
              v-if="singlePost.privacy"
              class="text-sm text-gray-light mb-0.5"
            >
              <span> · </span>
              <span>
                <ClientOnly>
                  <fa
                    v-if="singlePost.privacy === 'Public'"
                    class="text-gray-light text-xs font-normal"
                    :icon="['fas', 'earth-americas']"
                  />
                  <fa
                    v-if="
                      singlePost.privacy === 'Friends of friends' ||
                      singlePost.privacy === 'Custom' ||
                      singlePost.privacy === 'Friends'
                    "
                    class="text-gray-light text-xs font-normal"
                    :icon="['fas', 'gear']"
                  />
                  <fa
                    v-if="singlePost.privacy === 'Only me'"
                    class="text-gray-light text-xs font-normal"
                    :icon="['fas', 'lock']"
                  />
                  <fa
                    v-if="singlePost.privacy === 'Your friends'"
                    class="text-gray-light text-xs font-normal"
                    :icon="['fas', 'user-group']"
                  />
                </ClientOnly>
              </span>
            </span>
          </div>
        </div>
      </div>
      <div v-if="singlePost.type !== 'reel'" class="px-4 flex-grow">
        <!-- v-see-more.right="200" -->
        <p
          v-if="singlePost.text && singlePost.text !== singlePost.link"
          v-see-more.right="200"
          v-html="addLineBreak(singlePost.text, singlePost.provider)"
          class="comment break-words"
        ></p>
        <div
          v-if="
            singlePost.statusType !== 'created_event' &&
            singlePost.type !== 'event' &&
            !singlePost.event
          "
        >
          <!-- v-see-more.right="200" -->
          <p
            v-if="singlePost.description && !singlePost.link"
            v-see-more.right="200"
            v-html="addLineBreak(singlePost.description, singlePost.provider)"
            class="comment break-words"
          ></p>
          <!-- <button class="mt-2 block">See Media</button> -->
          <a
            v-if="singlePost.link"
            :href="singlePost.link"
            target="_blank"
            rel="noopener noreferrer"
            class="link break-words line-clamp-1"
            >{{ singlePost.link }}</a
          >
        </div>
      </div>
      <div
        v-if="
          (singlePost.archivedAttachmentImages.length > 0 &&
            singlePost.archivedAttachmentImages.length < 5) ||
          (singlePost.attachmentImages.length > 0 &&
            singlePost.attachmentImages.length < 5)
        "
        class="flex justify-center flex-wrap px-4 pt-2"
      >
        <div
          v-for="(feature, imageIndex) in singlePost.archivedAttachmentImages
            ? singlePost.archivedAttachmentImages.slice(0, 5)
            : singlePost.attachmentImages.slice(0, 5)"
          :key="feature"
          class="flex justify-center items-center cursor-pointer"
          :class="imageIndex < 2 ? 'column-2' : 'column-3'"
        >
          <img class="w-full h-full hover-border" :src="feature" />
        </div>
      </div>
      <div
        v-else-if="
          (singlePost.archivedAttachmentImages.length > 0 &&
            singlePost.archivedAttachmentImages.length >= 5) ||
          (singlePost.attachmentImages.length > 0 &&
            singlePost.attachmentImages.length >= 5)
        "
        class="flex justify-center flex-nowrap px-4 pt-2"
      >
        <div class="flex flex-col w-1/2">
          <div
            v-for="(feature, imageIndex) in singlePost.archivedAttachmentImages
              ? singlePost.archivedAttachmentImages.slice(0, 2)
              : singlePost.attachmentImages.slice(0, 2)"
            :key="feature"
            class="flex justify-center items-center cursor-pointer"
            :class="imageIndex < 2 ? 'column-2' : 'column-3'"
          >
            <img class="w-full h-full hover-border" :src="feature" />
          </div>
        </div>
        <div class="flex flex-col w-1/2">
          <div
            v-for="(feature, imageIndex) in singlePost.archivedAttachmentImages
              ? singlePost.archivedAttachmentImages.slice(2, 5)
              : singlePost.attachmentImages.slice(2, 5)"
            :key="feature"
            class="flex justify-center items-center cursor-pointer"
            :class="imageIndex < 3 ? 'column-2' : 'column-3'"
          >
            <img class="w-full h-full hover-border" :src="feature" />
            <div
              v-if="imageIndex === 2"
              class="w-full h-full absolute z-0 post-overlay"
            >
              <div class="sub-overlay"></div>
              <p
                class="xl:text-5xl md:text-3xl text-xl relative opacity-100 text-white font-bold"
              >
                {{
                  `+${
                    singlePost.archivedAttachmentImages
                      ? singlePost.archivedAttachmentImages.length - 4
                      : singlePost.attachmentImages.length - 4
                  }`
                }}
              </p>
            </div>
          </div>
        </div>
      </div>
      <div
        v-else-if="
          (singlePost.archivedFullPicture || singlePost.fullPicture) &&
          !singlePost.archivedSourceUrl &&
          !singlePost.sourceUrl &&
          (singlePost.type === 'photo' ||
            singlePost.statusType === 'added_photos') &&
          singlePost.archivedAttachmentImages.length === 0
        "
        class="w-full pt-6"
      >
        <img
          class="w-full posted-image"
          :src="
            singlePost.linkName.includes('giphy') ||
            singlePost.linkName.includes('tenor')
              ? singlePost.link
              : singlePost.archivedFullPicture
                ? singlePost.archivedFullPicture
                : singlePost.fullPicture
          "
          :alt="`${singlePost.name} user Posted Image`"
        />
      </div>
      <div
        v-else-if="
          (singlePost.archivedFullPicture || singlePost.fullPicture) &&
          !singlePost.archivedSourceUrl &&
          !singlePost.sourceUrl &&
          (singlePost.statusType === 'shared_story' ||
            singlePost.statusType === 'mobile_status_update') &&
          singlePost.archivedAttachmentImages.length === 0
        "
        class="w-full pt-6"
      >
        <a
          v-if="
            !singlePost.linkName.includes('giphy') &&
            !singlePost.linkName.includes('tenor') &&
            singlePost.link
          "
          :href="singlePost.link"
          target="_blank"
          rel="noopener noreferrer"
        >
          <img
            class="w-full posted-image"
            :src="
              singlePost.linkName.includes('giphy') ||
              singlePost.linkName.includes('tenor')
                ? singlePost.link
                : singlePost.archivedFullPicture
                  ? singlePost.archivedFullPicture
                  : singlePost.fullPicture
            "
            :alt="`${singlePost.name} user Posted Image`"
          />
        </a>

        <div v-else>
          <img
            class="w-full posted-image"
            :src="
              singlePost.linkName.includes('giphy') ||
              singlePost.linkName.includes('tenor')
                ? singlePost.link
                : singlePost.archivedFullPicture
                  ? singlePost.archivedFullPicture
                  : singlePost.fullPicture
            "
            :alt="`${singlePost.name} user Posted Image`"
          />
        </div>
      </div>
      <div
        v-if="
          (singlePost.archivedSourceUrl || singlePost.sourceUrl) &&
          (singlePost.type === 'video' ||
            singlePost.statusType === 'added_video' ||
            singlePost.statusType === 'shared_story' ||
            singlePost.statusType === 'mobile_status_update') &&
          singlePost.archivedAttachmentImages.length === 0
        "
        class="w-full py-6"
      >
        <video class="w-full h-auto" height="400" controls>
          <source
            :src="
              singlePost.archivedSourceUrl
                ? singlePost.archivedSourceUrl
                : singlePost.sourceUrl
            "
          />
          <source
            :src="
              singlePost.archivedFullPicture
                ? singlePost.archivedFullPicture
                : singlePost.fullPicture
            "
          />
        </video>
      </div>
      <div
        v-else-if="
          (singlePost.archivedSourceUrl || singlePost.sourceUrl) &&
          singlePost.type === 'reel' &&
          !singlePost.archivedSourceUrl.includes('youtube') &&
          !singlePost.sourceUrl.includes('youtube')
        "
        class="w-full py-0 reel main-reel relative drop-shadow-xl"
      >
        <div class="flex px-4 absolute top-4 left-0 z-1">
          <SharedIconFacebookIcon
            v-if="!singlePost.profileImageUrl"
            class="w-13 h-13 rounded-full border border-gray-400"
          ></SharedIconFacebookIcon>
          <img
            height="42"
            width="42"
            :src="singlePost.profileImageUrl"
            class="rounded-full profile-image"
            alt="dp"
            srcset=""
          />
          <div class="ml-2 lineHeight-1">
            <h2 class="text-xl text-white">
              {{ singlePost.profilename }}
              <span v-if="singlePost.placeName"
                ><span
                  v-if="singlePost.placeName.includes(singlePost.placeCity)"
                  class="text-white text-base"
                  >is in </span
                ><span v-else class="text-white text-base">is at </span
                >{{ singlePost.placeName }}</span
              >
            </h2>
            <span class="text-xs text-white">Reels</span
            ><span class="text-white text-xs mt-2"> · </span>
            <date-time
              class="text-white text-xs"
              :datetime="
                singlePost.updatedAt
                  ? singlePost.updatedAt
                  : singlePost.createdAt
                    ? singlePost.createdAt
                    : ''
              "
              format="MMMM dd, yyyy"
              :show-time="false"
              :friendly="false"
            ></date-time>
            <span
              v-if="
                singlePost.placeCity &&
                !singlePost.placeName.includes(singlePost.placeCity)
              "
              class="text-xs text-white"
            >
              <span> · </span>{{ singlePost.placeCity }}
            </span>
            <span v-if="singlePost.privacy" class="text-sm text-white">
              <span> · </span
              ><span
                ><fa
                  v-if="singlePost.privacy === 'Public'"
                  class="text-white text-xs font-normal"
                  :icon="['fas', 'earth-americas']"
                />
                <fa
                  v-if="
                    singlePost.privacy === 'Friends of friends' ||
                    singlePost.privacy === 'Custom' ||
                    singlePost.privacy === 'Friends'
                  "
                  class="text-white text-xs font-normal"
                  :icon="['fas', 'gear']"
                />
                <fa
                  v-if="singlePost.privacy === 'Only me'"
                  class="text-white text-xs font-normal"
                  :icon="['fas', 'lock']"
                />
                <fa
                  v-if="
                    singlePost.privacy === 'Your friends' ||
                    singlePost.privacy === 'Your friends of friends'
                  "
                  class="text-white text-xs font-normal"
                  :icon="['fas', 'user-group']"
                />
              </span>
            </span>
          </div>
        </div>
        <div class="px-4 absolute bottom-4 left-0 z-1">
          <!-- v-see-more.right="200" -->
          <p
            v-if="singlePost.text && singlePost.text !== singlePost.link"
            v-html="addLineBreak(singlePost.text, singlePost.provider)"
            class="text-lg text-white break-words"
          ></p>
          <div
            v-if="
              singlePost.statusType !== 'created_event' &&
              singlePost.type !== 'event' &&
              !singlePost.event
            "
          >
            <!-- v-see-more.right="200" -->
            <p
              v-if="singlePost.description && !singlePost.link"
              v-html="addLineBreak(singlePost.description, singlePost.provider)"
              class="text-lg text-white break-words"
            ></p>
            <!-- <button class="mt-2 block">See Media</button> -->
            <!-- <a
            v-if="singlePost.link && singlePost.linkName !== 'Born'"
            :href="singlePost.link"
            target="_blank"
            rel="noopener noreferrer"
            class="link break-words line-clamp-1"
            >{{ singlePost.link }}</a
          > -->
          </div>
        </div>
        <div
          class="absolute right-4 bottom-4 flex flex-col space-y-6 text-white z-1"
        >
          <div class="flex flex-col space-y-2 items-center">
            <SharedIconReelLikeIcon
              v-if="singlePost.reactionsCount === 0"
              class="w-10 h-10 drop-shadow-md"
            />
            <SharedIconReelLikeBgIcon class="w-10 h-10 drop-shadow-md" v-else />
            <p>{{ singlePost.reactionsCount }}</p>
          </div>
          <div
            class="flex flex-col space-y-2 items-center"
            @click="
              singlePost.commentsCount > 0 ? loadFeedComment(singlePost) : ''
            "
          >
            <SharedIconReelCommentIcon class="w-10 h-10 drop-shadow-md" />
            <p>{{ singlePost.commentsCount }}</p>
          </div>
          <div class="flex flex-col space-y-2 items-center">
            <SharedIconReelShareIcon class="w-10 h-10 drop-shadow-md" />
            <p>{{ singlePost.sharesCount }}</p>
          </div>
        </div>
        <div class="video-container">
          <video
            :id="`${singlePost.id}-custom-video`"
            class="reel"
            @click="togglePlayPause(singlePost.id)"
          >
            <source
              :src="
                singlePost.archivedSourceUrl &&
                singlePost.archivedSourceUrl.includes('http')
                  ? singlePost.archivedSourceUrl
                  : singlePost.sourceUrl
              "
            />
            <source
              :src="
                singlePost.archivedFullPicture
                  ? singlePost.archivedFullPicture
                  : singlePost.fullPicture
              "
            />
          </video>
          <div class="custom-controls">
            <button
              :id="`${singlePost.id}-play-pause`"
              class="w-9 h-9"
              @click="togglePlayPause(singlePost.id)"
            >
              <img class="w-4 h-4" :src="pause" />
            </button>
            <button
              :id="`${singlePost.id}-mute-unmute`"
              class="w-9 h-9"
              @click="toggleMuteUnmute(singlePost.id)"
            >
              <img class="w-4 h-4" :src="unmute" />
            </button>
          </div>
        </div>
      </div>
      <div
        v-if="
          singlePost.event ||
          singlePost.statusType === 'created_event' ||
          singlePost.type === 'event'
        "
        class="px-3.5 py-2 event_bg_color border-x"
        @click="
          singlePost.attachmentImages && singlePost.attachmentImages.length > 0
            ? showSingleImagePost(singlePost, 0)
            : showSinglePost(singlePost)
        "
      >
        <date-time
          class="text-red-soft text-xs md:text-sm"
          :datetime="singlePost.event.start_time"
          :end-time="singlePost.event.end_time"
          :show-time="false"
          :friendly="false"
          :show-day="true"
        ></date-time>
        <!-- v-see-more.right="200" -->
        <p
          v-if="singlePost.event.name"
          v-html="addLineBreak(singlePost.event.name, singlePost.provider)"
          class="text_color text-lg break-words"
        ></p>
        <p
          v-if="singlePost.placeName"
          class="description_color text-base break-words"
        >
          {{ singlePost.placeName }}
        </p>
        <!-- v-see-more.right="200" -->
        <p
          v-if="singlePost.description"
          v-html="addLineBreak(singlePost.description, singlePost.provider)"
          class="description_color text-base break-words"
        ></p>
      </div>
      <div
        v-if="
          singlePost.linkName &&
          !singlePost.linkName.includes('giphy') &&
          !singlePost.linkName.includes('tenor')
        "
        class="link-preview border-x"
      >
        <p class="text-sm text-gray-400">
          {{ singlePost.caption.toUpperCase() }}
        </p>
        <p v-if="singlePost.linkName" class="text-lg font-bold text-gray-2300">
          {{ singlePost.linkName }}
        </p>
        <p class="text-md text-gray-400 line-clamp-1">
          {{ singlePost.description }}
        </p>
      </div>
      <template v-if="isLive">
        <div
          v-if="singlePost.type !== 'reel'"
          class="flex justify-between px-4 pt-3.5"
        >
          <div class="social-icons">
            <img src="@/assets/img/icon/like.svg" alt="" />
            <SharedIconFacebookLoveIcon />
            <img src="@/assets/img/icon/wow.svg" alt="" />
            <span class="reaction-conter">{{ singlePost.reactionsCount }}</span>
          </div>
          <div class="flex space-x-3">
            <div
              class="comments-conter"
              @click="
                singlePost.commentsCount > 0 ? loadFeedComment(singlePost) : ''
              "
            >
              <span>{{ singlePost.commentsCount }} </span>
              <span class="ml-1">Comments</span>
            </div>

            <div class="shares-counter">
              <span>{{ singlePost.sharesCount }} </span>
              <span class="ml-1">Shares</span>
            </div>
          </div>
        </div>
        <div class="px-4 w-full">
          <div class="border border-[#B8B8B8] my-4"></div>
        </div>
        <div class="px-4">
          <p class="text-[#707070] font-semibold">View all comments</p>
        </div>
        <SourceHubSocialsComments
          v-if="selectedPostComments"
          class="px-4"
          :comments="selectedPostComments"
          @click="showEmojiPicker = false"
          @hideMenus="hideAllMenus()"
        ></SourceHubSocialsComments>
        <div
          class="flex space-x-1 px-4 relative"
          :class="avatarPreview ? 'items-start' : 'items-center'"
        >
          <img
            class="w-8 h-8 rounded-full"
            :src="singlePost.profileImageUrl"
            :alt="singlePost.profilename"
          />
          <div class="flex-grow">
            <div
              class="bg-[#F1F2F6] relative flex-grow flex justify-between items-center overflow-hidden px-6 pr-0 py-1.5 rounded-[24px]"
            >
              <!-- <textarea
              class="w-[clac(100%-100px)] h-auto no-scrollbar text-lg resize-none textarea-style outline-none border-none bg-[#F1F2F6] text-[#707070]"
              name=""
              id=""
              value=""
              placeholder="Comment as Sharparchive"
            ></textarea> -->
              <editor-content
                class="w-full max-h-[100px] pr-4 overflow-y-auto overflow-x-hidden comment-text-editor"
                :editor="editor"
                @click.stop=""
              />
              <div class="flex items-end absolute right-6 bottom-3">
                <div class="flex space-x-2.5">
                  <img
                    width="20"
                    height="20"
                    src="/social/comments/icon-emoji.svg"
                    alt="icon-emoji"
                    class="cursor-pointer"
                    @click.stop="toggleEmojiPicker"
                  />
                  <img
                    v-if="!avatarPreview"
                    width="20"
                    height="20"
                    src="/social/comments/camera-photos.svg"
                    alt="icon-photos"
                    class="cursor-pointer"
                    @click="fileInput.click()"
                  />
                  <input
                    ref="fileInput"
                    class="hidden"
                    type="file"
                    accept="image/*"
                    @change="onFileSelected"
                  />
                  <img
                    v-if="!avatarPreview"
                    width="20"
                    height="20"
                    src="/social/comments/icon-gif.svg"
                    alt="icon-gif"
                  />
                  <img
                    v-if="!avatarPreview"
                    width="20"
                    height="20"
                    src="/social/comments/icon-clipboard.svg"
                    alt="icon-clipboard"
                    class="cursor-pointer"
                    @click.stop="showClipBoardMenuHandler(true)"
                  />
                </div>
              </div>
            </div>
            <div v-if="avatarPreview" class="px-4 w-full flex space-x-2 mt-2.5">
              <img
                class="max-w-[80px]"
                :src="avatarPreview"
                alt="comment-image"
              />
              <div
                class="w-6 h-6 rounded-full bg-[#ebedf5] flex items-center justify-center cursor-pointer"
                @click="avatarPreview = ''"
              >
                <ClientOnly>
                  <fa
                    class="text-lg font-semibold text-[#525252]"
                    :icon="['fas', 'times']"
                  />
                </ClientOnly>
              </div>
            </div>
          </div>
          <EmojiPicker
            v-if="showEmojiPicker"
            @click.stop=""
            :editor="editor"
            class="absolute top-[-344px] right-[20px]"
          />
          <SourceHubSocialsClipBoardMenu
            v-if="showClipBoardMenu"
            class="absolute w-full h-[360px] max-h-[360px] top-[-358px] left-0"
            v-model:showClipBoardMenu="showClipBoardMenu"
            @click.stop=""
          />
        </div>
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useStore } from 'vuex'
import Mute from '~/assets/img/icon/mic_offAsset_10.webp'
import Unmute from '~/assets/img/icon/micAsset_9.webp'
import Pause from '~/assets/img/icon/pauseAsset_14.webp'
import Play from '~/assets/img/icon/playAsset_13.webp'
import { useFeedComment } from '~/composables/feeds/useComment'
import { useLineBreak } from '~/composables/useLineBreak'
import { Editor, EditorContent } from '@tiptap/vue-3'
import StarterKit from '@tiptap/starter-kit'
import Placeholder from '@tiptap/extension-placeholder'

const props = defineProps({
  scrollColor: {
    type: String,
    default: '',
  },
  singlePost: {
    type: Object,
    default: () => {},
  },
  isLive: {
    type: Boolean,
    default: true,
  },
})

const vueStore = useStore()
const router = useRouter()
const { loadComment } = useFeedComment()
const { addLineBreak } = useLineBreak()
const feedComments = ref(null)
const collapseable = ref(false)
const showEmojiPicker = ref<boolean>(false)
const fileInput = ref<HTMLInputElement | null>(null)
const loadFeedComment = async (singlePost: any) => {
  if (collapseable.value) {
    feedComments.value = null
    collapseable.value = false
  } else {
    try {
      const { data } = (await loadComment({
        provider: singlePost.provider,
        id: singlePost.id,
      })) as any
      if (data.length) {
        feedComments.value = data
        collapseable.value = true
      }
    } catch (error) {
      console.log(error)
    }
  }
}

// const singlePost = computed(() => vueStore.state.social.singlePost)
const selectedPostComments = computed(
  () => vueStore.state.social.selectedPostComments,
)
const specificCommentsId = computed(() => vueStore.state.social.specificId)
const pause = ref<string>(Pause)
const play = ref<string>(Play)
const mute = ref<string>(Mute)
const unmute = ref<string>(Unmute)
const editor = ref<Editor | null>(null)
const showClipBoardMenu = ref<boolean>(false)
const showClipBoardMenuHandler = (value: boolean) => {
  showClipBoardMenu.value = value
  showEmojiPicker.value = false
}
const hideAllMenus = () => {
  showEmojiPicker.value = false
  showClipBoardMenu.value = false
}
onMounted(() => {
  setTimeout(() => {
    if (specificCommentsId.value) {
      scrollToComment(specificCommentsId.value)
    }
  }, 1000)
  editor.value = new Editor({
    // content: `<p></p>`,
    extensions: [
      // StarterKit.configure({}),
      StarterKit,
      Placeholder.configure({
        // Use a placeholder:
        placeholder: 'Comment as Sharparchive',
      }),
    ],
    editorProps: {
      attributes: {
        class:
          'w-[clac(100%-100px)] h-auto no-scrollbar text-lg resize-none textarea-style outline-none border-none bg-[#F1F2F6] text-[#707070]',
      },
    },
  })
  document.addEventListener('click', () => {
    showEmojiPicker.value = false
    showClipBoardMenu.value = false
  })
})
onUnmounted(() => {
  document.removeEventListener('click', () => {
    showEmojiPicker.value = false
    showClipBoardMenu.value = false
  })
})
const scrollToComment = async (id: number) => {
  await nextTick()
  const commentSection = document.getElementById(`${id}`)
  if (commentSection) {
    commentSection.scrollIntoView({ behavior: 'smooth' })
  }
}
const togglePlayPause = (id: number) => {
  const video = document.getElementById(
    `${id}-custom-video`,
  ) as HTMLVideoElement | null
  const playPauseButton = document.getElementById(
    `${id}-play-pause`,
  ) as HTMLButtonElement | null
  // const muteUnmuteButton = document.getElementById('mute-unmute')
  if (video && playPauseButton) {
    video.controls = false
    if (video.paused || video.ended) {
      video.play()
      playPauseButton.innerHTML = `<fa
                  class="text-white text-xs font-normal"
                  :icon="['fas', 'pause']"
                />`
    } else {
      video.pause()
      playPauseButton.innerHTML = `<fa
                  class="text-white text-xs font-normal"
                  :icon="['fas', 'play']"
                />`
    }
  }
}

const toggleMuteUnmute = (id: number) => {
  const video = document.getElementById(
    `${id}-custom-video`,
  ) as HTMLVideoElement | null
  // const playPauseButton = document.getElementById('play-pause')
  const muteUnmuteButton = document.getElementById(
    `${id}-mute-unmute`,
  ) as HTMLButtonElement | null
  if (video && muteUnmuteButton) {
    video.controls = false
    if (video.muted) {
      video.muted = false
      muteUnmuteButton.textContent = 'Mute'
    } else {
      video.muted = true
      muteUnmuteButton.textContent = 'Unmute'
    }
  }
}

const toggleEmojiPicker = () => {
  showEmojiPicker.value = !showEmojiPicker.value
  showClipBoardMenu.value = false
}
const { avatarPreview, onFileSelected } = useFileUpload()
</script>

<style lang="scss" scoped>
.card {
  @apply bg-white my-0 w-full z-1;
  // min-width: 400px;
  // max-width: 690px;
}

.profile-name {
  @apply font-semibold text-base;
  color: #333333;
}

.profile-image {
  @apply w-10.1 h-10.1 rounded-full;
}

.lineHeight-1 {
  line-height: 1px;
}

.profile-date {
  @apply text-xs text-gray-light;
}

.comment {
  @apply mt-3 mb-2;
  color: #525252;
}

.link {
  @apply pt-2;
  color: #1263cc;
}
.posted-image {
  @apply max-h-116;
}
.link-preview {
  @apply my-0 py-2 px-4 bg-gray-1400;
}

.link-preview p {
  @apply text-gray-light;
}

.social-icons {
  @apply flex items-center;
}

.social-icons > img {
  @apply inline-block -mr-0.5;
  height: 18px;
  width: 18px;
}

.reaction-conter {
  @apply text-base ml-1.5 text-gray-light;
}

.comments-conter {
  @apply inline-block text-gray-light;
}
.shares-counter {
  @apply inline-block text-gray-light;
}
button {
  @apply py-1 mb-2 px-2.5 rounded-md text-gray-1200;
  background: #e6e6e6;
}
.column-2 {
  flex: 48%;
  // margin: 2px;
  @apply px-[1px];
  overflow: hidden;
  text-align: center;
  clear: both;
  position: relative;
}

.column-3 {
  margin: 2px;
  flex: 32%;
  overflow: hidden;
  clear: both;
  text-align: center;
  position: relative;
}
.hover-border {
  border: 1px solid transparent;
}

.post-overlay {
  align-items: center;
  border-radius: inherit;
  display: flex;
  justify-content: center;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: auto;
  z-index: 150;
  color: #fff;
  transition:
    0.3s cubic-bezier(0.25, 0.8, 0.5, 1),
    z-index 1ms;
}

.scroll {
  overflow-y: auto;
  overflow-x: hidden;
  -ms-overflow-style: none; /* IE 11 */
  scrollbar-width: thin;
  scrollbar-color: var(--scrollColor) #ececec; /* Firefox 64 */
  &::-webkit-scrollbar {
    width: 4px;
  }

  /* Track */
  &::-webkit-scrollbar-track {
    border-radius: 3px;
    background: #ececec;
  }

  /* Handle */
  &::-webkit-scrollbar-thumb {
    background: var(--scrollColor);
    border-radius: 3px;
  }

  /* Handle on hover */
  &::-webkit-scrollbar-thumb:hover {
    background: var(--scrollColor);
  }
}

// .comment-text-editor::-webkit-scrollbar {
//   display: none !important;
// }

.event_bg_color {
  background-color: rgb(240, 242, 245);
}
.text_color {
  color: #050505;
}
.description_color {
  color: #65676b;
}
.video-container {
  position: relative;
  // max-width: 480px;
  width: 100%;
  margin: 0 auto;
}

/* Style for the video element */
video {
  width: 100%;
}

/* Style for the custom control buttons */
.custom-controls {
  position: absolute;
  // bottom: 10px;
  // left: 50%;
  // transform: translateX(-50%);
  top: 8px;
  right: 0px;
  background-color: transparent;
  border: none;
  border-radius: 5px;
  padding: 5px 10px;
}

/* Style for individual control buttons */
.custom-controls button {
  background: none;
  border: none;
  cursor: pointer;
  color: white;
  font-size: 18px;
  margin: 0 5px;
}
.reel {
  height: 638px !important;
}
.main-reel {
  background-color: #63635c;
  border-radius: 10px;
  text-shadow:
    2px 7px 5px rgba(0, 0, 0, 0.3),
    0px -4px 10px rgba(255, 255, 255, 0.3);
}
@media (max-width: 500px) {
  .custom-controls {
    top: 32px;
    right: 0px;
  }

  /* Style for individual control buttons */
  .custom-controls button {
    width: 20px;
    padding: 0 2px;
  }
}
.textarea-style {
  field-sizing: content;
  width: calc(100% - 110px);
  /* min-height: 3rem; */
  max-height: 6rem;
}
</style>
