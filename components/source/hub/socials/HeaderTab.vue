<script setup lang="ts">
import All from '~/components/shared/icon/hub/socials/All.vue'
import Messages from '~/components/shared/icon/hub/socials/Messages.vue'
import Comments from '~/components/shared/icon/hub/socials/Comments.vue'
import Tagged from '~/components/shared/icon/hub/socials/Tagged.vue'
import Mentions from '~/components/shared/icon/hub/socials/Mentions.vue'
import Communities from '~/components/shared/icon/hub/socials/Communities.vue'
import Groups from '~/components/shared/icon/hub/socials/Groups.vue'
import Calls from '~/components/shared/icon/hub/socials/Calls.vue'
import Primary from '~/components/shared/icon/hub/emails/Primary.vue'
import Promotions from '~/components/shared/icon/hub/emails/Promotions.vue'
import Social from '~/components/shared/icon/hub/emails/Social.vue'
import Updates from '~/components/shared/icon/hub/emails/Updates.vue'
import Forums from '~/components/shared/icon/hub/emails/Forums.vue'
import { useStore } from 'vuex'

const emit = defineEmits<{
  (e: 'seleted-type', value: string): void
}>()

const config = useRuntimeConfig()
const store = useStore()

interface Options {
  id: number
  img: string | Component
  text: string
  path: string
  count: number
}
interface SocialsTabMenus {
  title: string
  options: Options[]
}
const accountType = computed(() => store.state.social.accountType)
const accountItem = computed(() => {
  return store.state.social.accountItem
})
const route = useRoute()
const socialsTabMenus = computed(() => {
  switch (accountType.value) {
    case 'socials':
      return {
        title: 'Activity',
        options: [
          {
            id: 1,
            img: markRaw(All),
            text: 'All',
            path: 'all',
            count: 0,
          },
          {
            id: 2,
            img: markRaw(Messages),
            text: 'Messages',
            path: 'messages',
            count: 1,
          },
          {
            id: 3,
            img: markRaw(Comments),
            text: 'Comments',
            path: 'comments',
            count: 2,
          },
          {
            id: 4,
            img: markRaw(Tagged),
            text: 'Tagged',
            path: 'tagged',
            count: 0,
          },
          {
            id: 5,
            img: markRaw(Mentions),
            text: 'Mentions',
            path: 'mentions',
            count: 2,
          },
        ],
      }
      break
    case 'emails':
      if (accountItem.value && accountItem.value.provider === 'Google') {
        return {
          title: 'Categories',
          options: [
            {
              id: 1,
              img: markRaw(Primary),
              text: 'Primary',
              path: `/source/hub/emails/${route.params.slug}/primary`,
              count: 0,
            },
            {
              id: 2,
              img: markRaw(Promotions),
              text: 'Promotions',
              path: `/source/hub/emails/${route.params.slug}/promotions`,
              count: 0,
            },
            {
              id: 3,
              img: markRaw(Social),
              text: 'Social',
              path: `/source/hub/emails/${route.params.slug}/social`,
              count: 0,
            },
            {
              id: 4,
              img: markRaw(Updates),
              text: 'Updates',
              path: `/source/hub/emails/${route.params.slug}/updates`,
              count: 0,
            },
            {
              id: 5,
              img: markRaw(Forums),
              text: 'Forums',
              path: `/source/hub/emails/${route.params.slug}/forums`,
              count: 0,
            },
          ],
        }
      } else if (
        accountItem.value &&
        accountItem.value.provider === 'Microsoft'
      ) {
        return {
          title: 'Categories',
          options: [
            {
              id: 1,
              // img: markRaw(Messages),
              text: 'Focused',
              path: `/source/hub/emails/${route.params.slug}/focused`,
              count: 0,
            },
            {
              id: 2,
              // img: markRaw(Communities),
              text: 'Others',
              path: `/source/hub/emails/${route.params.slug}/others`,
              count: 0,
            },
          ],
        }
      }
      break
    case 'text':
      if (accountItem.value && accountItem.value.provider === 'iMessage') {
        return {
          title: 'Activity',
          options: [
            {
              id: 1,
              img: markRaw(All),
              text: 'All',
              path: 'all',
              count: 0,
            },
          ],
        }
      } else if (
        accountItem.value &&
        accountItem.value.provider === 'WhatsApp'
      ) {
        return {
          title: 'Activity',
          options: [
            {
              id: 1,
              img: markRaw(Messages),
              text: 'Chats',
              path: 'chats',
              count: 0,
            },
            {
              id: 2,
              img: markRaw(Communities),
              text: 'Communities',
              path: 'communities',
              count: 0,
            },
            {
              id: 3,
              img: markRaw(Calls),
              text: 'Calls',
              path: 'calls',
              count: 0,
            },
          ],
        }
      } else if (
        accountItem.value &&
        accountItem.value.provider === 'Telegram'
      ) {
        return {
          title: 'Activity',
          options: [
            {
              id: 1,
              img: markRaw(Messages),
              text: 'Chats',
              path: 'chats',
              count: 0,
            },
            {
              id: 2,
              img: markRaw(Groups),
              text: 'Groups',
              path: 'groups',
              count: 0,
            },
            {
              id: 3,
              img: markRaw(Calls),
              text: 'Calls',
              path: 'calls',
              count: 0,
            },
          ],
        }
      }
      break
  }
})
const searchBoxPlace = computed(() => {
  if (accountItem.value.provider === 'iMessage') {
    return 'flex-row items-center space-x-4'
  } else if (
    accountItem.value.provider === 'WhatsApp' ||
    accountItem.value.provider === 'Telegram'
  ) {
    return 'xl:flex-row flex-col xl:space-x-4 xl:space-y-0 space-y-4 xl:items-center'
  } else {
    return '2xl:flex-row flex-col 2xl:space-x-4 2xl:space-y-0 space-y-4 2xl:items-center'
  }
})
const getSelectedNotificationItems = (text: string) => {
  emit('seleted-type', text.toLocaleLowerCase())
  store.commit('social/RESET_STATES')
  store.commit(
    'social/SET_MESSAGES_NOTIFICATION_ITEMS',
    text.toLocaleLowerCase(),
  )
}
</script>

<template>
  <div class="w-full">
    <div class="flex justify-between" :class="searchBoxPlace">
      <div class="flex space-x-2 items-center">
        <div class="pr-2 border-r border-[#C2C2C2]">
          <p class="text-[#707070]">{{ socialsTabMenus.title }}</p>
        </div>
        <div class="flex space-x-4 items-center">
          <NuxtLink
            v-for="option in socialsTabMenus.options"
            :key="option.id"
            :to="option.path"
            class="relative flex !space-x-2 items-center px-6 py-2 rounded-full text-[#525252] bg-[#F1F2F6]"
            @click.native="getSelectedNotificationItems(option.text)"
          >
            <component v-if="option.img" :is="option.img"></component>
            <p class="font-semibold">{{ option.text }}</p>
            <div
              v-if="option.count"
              class="w-5 h-5 rounded-full text-xs bg-[#E21F3F] text-white font-bold absolute -top-2 right-0 flex justify-center items-center"
            >
              <p>{{ option.count }}</p>
            </div>
          </NuxtLink>
        </div>
      </div>
      <HomeSearchBar class="!max-w-[296px] !bg-[#F1F2F6] search-bar" />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.router-link-exact-active, .router-link-active {
  @apply bg-blue-200 text-white;
}
</style>
