<script setup lang="ts">
import { useStore } from 'vuex'

const store = useStore()
const route = useRoute()
const accountItem = computed(() => {
  return store.state.social.accountItem
})
const accountType = computed(() => store.state.social.accountType)
const notificationType = ref('all')
const selectedType = (type: string) => {
  notificationType.value = type
}
</script>

<template>
  <div class="w-full px-4 pt-4 pb-6 border-b-[3px] border-[#F1F2F6]">
    <div class="w-full flex flex-col space-y-[37px]">
      <div class="w-full flex justify-between">
        <div class="flex space-x-2.5">
          <SourceAccountLogo
            :account-profile-pic="accountItem.profilePic"
            :account-provider="accountItem.provider"
            width="54px"
            height="54px"
          />
          <div class="flex flex-col space-y-[2px]">
            <p class="text-[#525252] text-xl font-bold">
              {{ accountItem.name }}
            </p>
            <p class="text-[#707070]">
              {{
                accountType === 'text'
                  ? accountItem.number
                  : accountItem.username
              }}
            </p>
          </div>
        </div>
        <div v-if="accountType === 'emails'" class="flex space-x-4">
          <SharedIconHubEmailsButtonGoogleHelp
            v-if="accountItem.provider === 'Google'"
          />
          <div
            v-if="accountItem.provider === 'Microsoft'"
            class="w-[35px] h-[35px] rounded-full bg-[#F1F2F6] flex justify-center items-center"
          >
            <SharedIconHubMicrosoftLightIcon class="w-3 h-4" />
          </div>
          <SharedIconHubEmailsButtonSettings />
          <SharedIconHubEmailsButtonApps
            v-if="accountItem.provider === 'Google'"
          />
          <BaseButton
            class="w-[116px] h-[35px] rounded-full flex justify-center items-center bg-[#4A71D4] text-white font-semibold"
            :text="accountItem.provider === 'Google' ? 'Compose' : 'New Mail'"
            @click="store.commit('emails/SET_SHOW_COMPOSE_SECTION')"
          />
        </div>
        <BaseButton
          v-else
          class="w-[132px] h-[35px] rounded-full flex justify-center items-center bg-[#4A71D4] text-white font-semibold"
          :text="
            notificationType === 'messages' ||
            route.fullPath.includes('messages') ||
            accountType === 'text'
              ? 'New Chat'
              : 'Create Post'
          "
          @click="$router.push('/source/create-post')"
        />
      </div>
      <SourceHubSocialsHeaderTab @seleted-type="selectedType" />
    </div>
  </div>
</template>
