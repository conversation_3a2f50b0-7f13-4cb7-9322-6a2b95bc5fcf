<template>
  <div>
    <EditorContent :editor="editor"  @keydown="onKeydown" />
    <!-- File Preview Below Editor -->
    <div v-if="attachedFiles.length" class="space-y-2">
      <div
        v-for="(file, index) in attachedFiles"
        :key="index"
        class="flex items-center justify-between bg-gray-100 p-2 rounded shadow-sm"
      >
        <div>
          <a
            :href="fileUrls[index]"
            :download="file.name"
            class="text-sm font-medium text-blue-600 hover:underline"
          >
            {{ file.name }}
          </a>
          <p class="text-xs text-gray-500">{{ formatSize(file.size) }}</p>
        </div>
        <button
          @click="removeFile(index)"
          class="text-red-500 hover:text-red-700 text-sm"
          title="Remove"
        >
          ✖
        </button>
      </div>
    </div>
    <div
      v-if="showLinkMenu"
      :style="`position: absolute; top: ${linkMenuPosition.top}px; left: ${linkMenuPosition.left}px; z-index: 100; background: white; border: 1px solid #ccc; padding: 12px; border-radius: 10px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);`"
    >
      <div v-if="!textSelected">
        <input
          v-model="linkText"
          placeholder="Text"
          class="border p-1 rounded w-56 mb-2 block text-black"
        />
      </div>
      <input
        v-model="linkUrl"
        placeholder="Type or paste a link"
        class="border p-1 rounded w-56 mb-2 block text-black"
      />
      <div class="flex gap-2 justify-end">
        <button
          @click="applyLink"
          class="bg-blue-500 text-white px-3 py-1 rounded"
        >
          Apply
        </button>
        <button
          @click="updateLink"
          class="bg-green-500 text-white px-3 py-1 rounded"
        >
          Update
        </button>
        <button
          @click="removeLink"
          class="bg-red-500 text-white px-3 py-1 rounded"
        >
          Remove
        </button>
        <button @click="closeLinkMenu" class="bg-gray-300 px-3 py-1 rounded">
          Cancel
        </button>
      </div>
    </div>

    <div
      v-if="showLinkOptions"
      :style="`position: absolute; top: ${linkMenuPosition.top}px; left: ${linkMenuPosition.left}px; z-index: 100; background: white; border: 1px solid #ccc; padding: 10px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);`"
    >
      <a :href="selectedLink" target="_blank" class="text-blue-600 underline"
        >Go to link: {{ selectedLink }}</a
      >
      |
      <button @click="editLink" class="text-blue-500">Change</button> |
      <button @click="removeLink" class="text-red-500">Remove</button>
    </div>

    <button
      @click="showLinkPopup()"
      class="mt-4 bg-blue-500 text-white px-4 py-2 rounded"
    >
      Add Link
    </button>
    <!-- File Attachment -->
    <div class="mt-4">
      <input
        type="file"
        multiple
        @change="handleFileChange"
        class="mb-2"
        ref="fileInput"
      />
    </div>
    <!-- <NuxtEmojiPicker
      :native="false"
      :display-recent="true"
      theme="light"
      @select="addEmoji"
    /> -->
    <!-- <EmojiPicker :native="false" :display-recent="true" @select="addEmoji" /> -->
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, nextTick } from 'vue'
import { EditorContent, useEditor } from '@tiptap/vue-3'
import StarterKit from '@tiptap/starter-kit'
import Link from '@tiptap/extension-link'
import { Attachment } from '~/composables/Attachment'

// import EmojiPicker from 'vue3-emoji-picker'
// import 'vue3-emoji-picker/css'

const showLinkMenu = ref(false)
const linkUrl = ref('')
const linkText = ref('')
const textSelected = ref(false)
const linkMenuPosition = ref({ top: 0, left: 0 })
const showLinkOptions = ref(false) // Shows the first popup (Go to Link, Change, Remove)
const showEditLink = ref(false) // Shows the second popup (Link input)
const selectedLink = ref(null) // Stores the selected link
const selectedText = ref(null)

const editor = useEditor({
  extensions: [
    StarterKit,
    Link.configure({
      openOnClick: true,
      autolink: true,
      HTMLAttributes: {
        class: 'tiptap-link',
        style: 'color: #2563eb; text-decoration: underline; cursor: pointer;',
      },
    }),
    Attachment,
  ],
  content:
    '<p>Click the Add Link button or press Ctrl+K to insert a hyperlink.</p>',
})

const updatePopupPosition = () => {
  nextTick(() => {
    const selection = window.getSelection()
    if (!selection || selection.rangeCount === 0) return

    const range = selection.getRangeAt(0)
    textSelected.value = !range.collapsed
    const rect = range.getBoundingClientRect()
    const editorElement = document.querySelector('.ProseMirror')
    const editorRect = editorElement?.getBoundingClientRect() ?? {
      top: 0,
      left: 0,
    }

    // Check if selected text has a link
    const node = selection.anchorNode?.parentElement
    const link = node?.closest('a') // Find nearest <a> tag
    console.log('link', link, node, link.getAttribute('href'))
    if (link && selection.toString().trim()) {
      selectedLink.value = link.getAttribute('href')
      selectedText.value = selection.toString().trim()
      showLinkOptions.value = true // Show first popup
      showEditLink.value = false
    } else {
      selectedLink.value = null
      showLinkOptions.value = false
    }

    // Set popup position
    linkMenuPosition.value = {
      top: rect.bottom - editorRect.top + 10,
      left: rect.left - editorRect.left,
    }
  })
}

// const updatePopupPosition = () => {
//   nextTick(() => {
//     // const selection = window.getSelection()
//     // if (selection && selection.rangeCount > 0) {
//     //   const range = selection.getRangeAt(0)
//     //   textSelected.value = !range.collapsed
//     //   const rect = range.getBoundingClientRect()
//     //   const editorElement = document.querySelector('.ProseMirror')
//     //   const editorRect = editorElement?.getBoundingClientRect() ?? {
//     //     top: 0,
//     //     left: 0,
//     //   }
//     //   linkMenuPosition.value = {
//     //     top: rect.bottom - editorRect.top + 10,
//     //     left: rect.left - editorRect.left,
//     //   }
//     //   console.log('linkMenuPosition', linkMenuPosition.value)
//     // }
//     const selection = editor.value?.state.selection
//     if (!selection) return

//     const { from, to, empty } = selection
//     textSelected.value = !empty // True if text is selected
//     // Get link if the cursor is inside one
//     const linkMark = editor.value?.getAttributes('link')
//     // selectedLink.value = linkMark?.href || null

//     // if (textSelected.value || selectedLink.value) {
//     const coords = editor.value?.view.coordsAtPos(from)
//     const editorRect = editor.value?.view.dom.getBoundingClientRect()

//     linkMenuPosition.value = {
//       top: coords.top - editorRect.top + 30,
//       left: coords.left - editorRect.left,
//     }

//     showLinkMenu.value = true
//   })
// }

const applyLink = () => {
  console.log('applyLink', linkUrl.value, textSelected.value)
  if (linkUrl.value) {
    editor.value.chain().focus()
    console.log('applyLink', linkUrl.value, textSelected.value)
    if (textSelected.value) {
      editor.value.chain().setLink({ href: linkUrl.value }).run()
      console.log('textSelected', textSelected.value)
    } else if (linkText.value) {
      editor.value
        .chain()
        .focus()
        .insertContent(
          `<a href='${linkUrl.value}' class='tiptap-link'>${linkText.value}</a>`,
        )
        .run()
    }
  }
  closeLinkMenu()
}

const updateLink = () => {
  showLinkMenu.value = true
  const { state, chain } = editor.value
  const { selection, doc } = state
  const selectedText = state.selection

  console.log('updateLink', selectedText, linkUrl.value)

  // if (selectedText.empty) return // No selection, do nothing
  if (selection.empty) return // No selection, do nothing
  const from = selection.from
  const to = selection.to
  const currentText = doc.textBetween(from, to, ' ') // Get selected text
  const currentLink = state.doc
    .resolve(selectedText.from)
    .marks()
    .find((mark) => mark.type.name === 'link')

  console.log('currentLink', currentLink, currentText)
  // if (currentLink) {
  //   editor.value
  //     .chain()
  //     .extendMarkRange('link') // Extend selection to whole link
  //     .setLink({ href: linkUrl.value }) // Update link
  //     .run()
  // }
  // if (selectedText.value !== linkText.value) {

  // }
  editor.value.chain().extendMarkRange('link').unsetLink().run()

  // Insert new text with updated link
  editor.value
    .chain()
    .deleteRange({ from, to }) // Remove old text
    .insertContentAt(from, {
      type: 'text',
      text: linkText.value || currentText, // Use input text or keep old text
      marks: [{ type: 'link', attrs: { href: linkUrl.value } }],
    })
    .run()

  closeLinkMenu()
}

// const removeLink = () => {
//   editor.value.chain().focus().extendMarkRange('link').unsetLink().run()
//   closeLinkMenu()
// }

const editLink = () => {
  showLinkOptions.value = false
  showLinkMenu.value = true
  linkUrl.value = selectedLink.value || '' // Pre-fill the existing link
  linkText.value = selectedText.value || '' // Pre-fill the existing text
}

const removeLink = () => {
  editor.value.chain().focus().extendMarkRange('link').unsetLink().run()
  showLinkOptions.value = false
}

const closeLinkMenu = () => {
  showLinkMenu.value = false
  linkUrl.value = ''
  linkText.value = ''
  console.log(
    'closeLinkMenu',
    showLinkMenu.value,
    linkUrl.value,
    linkText.value,
  )
}

const showLinkPopup = async () => {
  await updatePopupPosition()
  showLinkMenu.value = true
  console.log('showLinkMenu', showLinkMenu.value)
}

const addEmoji = (emoji) => {
  console.log(emoji, emoji.i, 'emoji.native')
  if (editor.value) {
    editor.value.chain().focus().insertContent(emoji.i).run()
  }
  // showEmojiPicker.value = false;
}
// ✅ Handle keydown to unset link before typing
const onKeydown = (event) => {
  if (!editor.value) return

  // We only want to unset the link if we're right after a link
  const { state } = editor.value
  const { from, empty } = state.selection
  if (!empty) return // only when caret, not selection

  const marks = state.storedMarks || state.doc.resolve(from).marks()
  const hasLink = marks.some(mark => mark.type.name === 'link')

  if (hasLink) {
    editor.value.chain().focus().unsetMark('link').run()
  }
}

onMounted(() => {
  editor.value?.setOptions({
    editorProps: {
      handleDOMEvents: {
        keydown: (view, event) => {
          if (event.key === 'k' && (event.metaKey || event.ctrlKey)) {
            showLinkPopup()
            return true
          }
          return false
        },
        mouseup: () => {
          updatePopupPosition()
        },
      },
    },
  })

  editor.value?.setOptions({
    editorProps: {
      handleDOMEvents: {
        mouseup: () => {
          updatePopupPosition()
        },
      },
    },
  })
})

onBeforeUnmount(() => {
  editor.destroy()
})
// const fileInput = ref<HTMLInputElement>()
// const triggerFileInput = () => {
//   fileInput.value?.click()
// }

// const handleFileChange = async (e: Event) => {
//   const files = (e.target as HTMLInputElement).files
//   if (!files?.length || !editor.value) return

//   const file = files[0]

//   // ✅ Upload to backend (optional) or use blob preview
//   const fileUrl = URL.createObjectURL(file)

//   editor.value.commands.insertContent({
//     type: 'attachment',
//     attrs: {
//       fileName: file.name,
//       fileUrl,
//     },
//   })

//   fileInput.value!.value = '' // reset input
// }

const MAX_FILE_SIZE_MB = 25

const attachedFiles = ref<File[]>([])
const fileInput = ref<HTMLInputElement | null>(null)
const fileUrls = ref<string[]>([])
// Handle file input
const handleFileChange = (event: Event) => {
  const input = event.target as HTMLInputElement
  const files = input.files
  if (!files) return

  for (const file of files) {
    const fileSizeMB = file.size / (1024 * 1024)
    if (fileSizeMB > MAX_FILE_SIZE_MB) {
      alert(`File "${file.name}" exceeds the 25MB limit.`)
      continue
    }
    attachedFiles.value.push(file)
    const url = URL.createObjectURL(file)
    fileUrls.value.push(url)
  }

  if (fileInput.value) fileInput.value.value = ''
}

// Remove file
const removeFile = (index: number) => {
  attachedFiles.value.splice(index, 1)
}

// Format file size
const formatSize = (size: number) => {
  const mb = size / (1024 * 1024)
  return mb >= 1 ? `${mb.toFixed(2)} MB` : `${(size / 1024).toFixed(2)} KB`
}
</script>

<style>
.tiptap-link {
  color: #2563eb;
  text-decoration: underline;
  cursor: pointer;
}
input[type='file']::file-selector-button {
  padding: 0.5em 1em;
  background-color: #2563eb;
  color: white;
  border: none;
  border-radius: 0.375rem;
  cursor: pointer;
}
</style>
