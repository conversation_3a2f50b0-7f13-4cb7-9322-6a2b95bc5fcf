<script setup lang="ts">
interface Props {
  options?: Array<{
    label: string
    value: string
  }>
  updatedFontFamily?: Function
  label?: string
}

const props = withDefaults(defineProps<Props>(), {
  options: [],
  updatedFontFamily: (label: string, value: string) => {},
  label: '',
})
</script>

<template>
  <div
    class="h-[405px] min-h-[405px] w-[172px] min-w-[172px] px-1 py-1 flex flex-col space-y-0 align-box absolute -top-[398px] left-[65px] z-10"
  >
    <div
      v-for="option in options"
      :key="option.value"
      @click="updatedFontFamily(option.label, option.value)"
      class="px-2 py-1.5 hover:bg-[#F1F2F6] rounded cursor-pointer grid !gap-x-2.5 grid-cols-[16px_1fr] items-center"
    >
      <fa
        class="text-[#707070]"
        :class="option.label === label ? 'opacity-100' : 'opacity-0'"
        :icon="['fa', 'fa-check']"
      />
      <p class="#525252">
        {{ option.label }}
      </p>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.align-box {
  background: #ffffff 0% 0% no-repeat padding-box;
  box-shadow: 2px 2px 4px #22283114;
  border-radius: 8px;
}
</style>
