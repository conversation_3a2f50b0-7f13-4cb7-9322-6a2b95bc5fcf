<script setup lang="ts">
import { useStore } from 'vuex'

const store = useStore()
const route = useRoute()
const router = useRouter()

interface EmailMessage {
  id: number
  subject: string
  snippet: string
  description: string
  createdAt: string
  read: boolean
  checked: boolean
  favourite: boolean
}

const emailMessages = computed(() => store.state.emails.emailMessages)

const checkedAll = ref<boolean>(false)
const showMinus = ref<boolean>(false)
const toggleCheckedAll = ($event: any) => {
  if (!showMinus.value) {
    checkedAll.value = !checkedAll.value
    store.commit('emails/SET_CHECKED_ALL_EMAIL_MESSAGES')
  } else {
    showMinus.value = false
    checkedAll.value = false
    store.commit('emails/SET_UNCHECKED_ALL_EMAIL_MESSAGES')
  }
}
const toggleSingleCheckBox = () => {
  showMinus.value = emailMessages.value.some(
    (emailMessage: EmailMessage) => emailMessage.checked,
  )
  if (showMinus.value) {
    checkedAll.value = true
  }
  const allChecked = emailMessages.value.every(
    (emailMessage: EmailMessage) => emailMessage.checked,
  )
  if (allChecked) {
    showMinus.value = false
    checkedAll.value = true
  }
  const allUnchecked = emailMessages.value.every(
    (emailMessage: EmailMessage) => !emailMessage.checked,
  )
  if (allUnchecked) {
    showMinus.value = false
    checkedAll.value = false
  }
}
const toggleSelectMenu = ref<boolean>(false)
const toggleMarkAllReadMenu = ref<boolean>(false)
const toggleReadUnreadButton = ref<boolean>(false)
const toggleMoveToMenu = ref<boolean>(false)
const setReadUnreadToggleButton = () => {
  toggleReadUnreadButton.value = !toggleReadUnreadButton.value
  if (toggleReadUnreadButton.value) {
    store.commit('emails/READ_ALL_MESSAGE')
  } else {
    store.commit('emails/UNREAD_ALL_MESSAGE')
  }
}
onMounted(() => {
  document.addEventListener('click', () => {
    toggleSelectMenu.value = false
    toggleMarkAllReadMenu.value = false
    toggleMoveToMenu.value = false
    if (showMenu.value) {
      store.commit('emails/SET_CHECK_SINGLE_MESSAGE', 0)
      showMenu.value = false
      toggleSingleCheckBox()
      position.value.x = 0
      position.value.y = 0
    }
  })
  document.addEventListener('wheel', () => {
    if (showMenu.value) {
      store.commit('emails/SET_CHECK_SINGLE_MESSAGE', 0)
      showMenu.value = false
      toggleSingleCheckBox()
      position.value.x = 0
      position.value.y = 0
    }
  })
  window.addEventListener('popstate', function (event) {
    listenWindowBackForwardEvent(event.state.current)
  })
})
onUnmounted(() => {
  document.removeEventListener('click', () => {
    toggleSelectMenu.value = false
    toggleMarkAllReadMenu.value = false
    toggleMoveToMenu.value = false
    store.commit('emails/SET_CHECK_SINGLE_MESSAGE', 0)
    showMenu.value = false
    toggleSingleCheckBox()
    position.value.x = 0
    position.value.y = 0
  })
  document.removeEventListener('wheel', () => {
    if (showMenu.value) {
      store.commit('emails/SET_CHECK_SINGLE_MESSAGE', 0)
      showMenu.value = false
      toggleSingleCheckBox()
      position.value.x = 0
      position.value.y = 0
    }
  })
  window.removeEventListener('popstate', function (event) {
    listenWindowBackForwardEvent(event.state.current)
  })
})
const settoggleSelectMenu = () => {
  toggleSelectMenu.value = !toggleSelectMenu.value
  toggleMoveToMenu.value = false
  toggleMarkAllReadMenu.value = false
  // store.commit('emails/SET_CHECK_SINGLE_MESSAGE', 0)
  // showMenu.value = false
  // toggleSingleCheckBox()
  // position.value.x = 0
  // position.value.y = 0
}
const settoggleMoveToMenu = () => {
  toggleMoveToMenu.value = !toggleMoveToMenu.value
  toggleSelectMenu.value = false
  toggleMarkAllReadMenu.value = false
  // store.commit('emails/SET_CHECK_SINGLE_MESSAGE', 0)
  // showMenu.value = false
  // toggleSingleCheckBox()
  // position.value.x = 0
  // position.value.y = 0
}
const settoggleMarkAllReadMenu = () => {
  toggleMarkAllReadMenu.value = !toggleMarkAllReadMenu.value
  toggleSelectMenu.value = false
  toggleMoveToMenu.value = false
  // store.commit('emails/SET_CHECK_SINGLE_MESSAGE', 0)
  // showMenu.value = false
  // toggleSingleCheckBox()
  // position.value.x = 0
  // position.value.y = 0
}
const position = ref<{ x: number; y: number }>({ x: 0, y: 0 })
const showMenu = ref<boolean>(false)
const menu = ref<HTMLElement | null>(null)
const messageMenu = ref<HTMLElement | null>(null)
const checkSingleMessage = (event: any, id: number) => {
  event.preventDefault() // Prevent default browser menu
  store.commit('emails/SET_CHECK_SINGLE_MESSAGE', id)
  showMenu.value = true
  toggleSingleCheckBox()
  setTimeout(() => {
    // Get cursor position
    let x = event.clientX
    let y = event.clientY
    const temp = document.getElementById('menu')

    // Get screen width and height
    const menuWidth = temp?.offsetWidth || 150
    const menuHeight = temp?.offsetHeight || 100
    // const screenWidth = messageMenu.value?.offsetWidth
    // const screenHeight = messageMenu.value?.offsetHeight

    const screenWidth = window.innerWidth
    const screenHeight = window.innerHeight

    // Adjust position to prevent overflow
    if (x + menuWidth > screenWidth) x -= menuWidth
    if (y + menuHeight > screenHeight) y -= menuHeight
    y = Math.abs(y)
    position.value = { x, y }
  })
}
const showSpecificMessage = ref(false)
const setSpecificMessage = (emailMessage: EmailMessage) => {
  showSpecificMessage.value = true
  router.push(`${route.fullPath}/${emailMessage.id}`)
  store.commit('emails/READ_A_SPECIFIC_MESSAGE', emailMessage.id)
  store.commit('emails/SET_SELECTED_EMAIL_MESSAGE', emailMessage)
}
const listenWindowBackForwardEvent = (path: string) => {
  showSpecificMessage.value = false
}
</script>

<template>
  <div ref="messageMenu" class="w-full h-full overflow-hidden">
    <div
      class="flex justify-between items-center pl-2 pr-4 py-2 border-b border-[#E3E3E3]"
    >
      <div class="flex space-x-2 items-center">
        <div
          v-if="!showSpecificMessage"
          class="flex items-center relative"
          :class="
            checkedAll || showMinus || toggleSelectMenu
              ? 'bg-[#F1F2F6] rounded-md'
              : ''
          "
        >
          <div
            class="hover:bg-[#F1F2F6] flex items-center h-10 px-2 rounded-md cursor-pointer"
          >
            <InputsCheckBoxInput
              :model-value="checkedAll"
              :show-minus="showMinus"
              @update:modelValue="toggleCheckedAll"
            />
          </div>
          <div
            class="hover:bg-[#F1F2F6] flex items-center h-10 px-2 rounded-md cursor-pointer"
            @click.stop="settoggleSelectMenu"
          >
            <SharedIconHubEmailsDownArrow class="hover:bg-[#F1F2F6]" />
          </div>
          <SourceHubEmailsSelectMenu
            v-if="toggleSelectMenu"
            :toggleSingleCheckBox="toggleSingleCheckBox"
          />
        </div>
        <div
          v-if="!showMinus && !checkedAll && !showSpecificMessage"
          class="hover:bg-[#F1F2F6] w-10 h-10 flex justify-center items-center rounded-full cursor-pointer"
        >
          <SharedIconHubEmailsRefreshIcon />
        </div>
        <div
          v-if="showSpecificMessage"
          class="hover:bg-[#F1F2F6] w-10 h-10 flex justify-center items-center rounded-full cursor-pointer"
          @click.stop="router.back()"
        >
          <SharedIconHubEmailsBackArrow />
        </div>
        <div
          v-if="showMinus || checkedAll || showSpecificMessage"
          class="flex space-x-2"
        >
          <div
            class="hover:bg-[#F1F2F6] w-10 h-10 flex justify-center items-center rounded-full cursor-pointer"
          >
            <SharedIconHubEmailsArchiveIcon />
          </div>
          <div
            class="hover:bg-[#F1F2F6] w-10 h-10 flex justify-center items-center rounded-full cursor-pointer"
          >
            <SharedIconHubEmailsReportSpamIcon />
          </div>
          <div
            class="hover:bg-[#F1F2F6] w-10 h-10 flex justify-center items-center rounded-full cursor-pointer"
          >
            <SharedIconHubEmailsDeleteIcon />
          </div>
          <div class="w-[1px] h-10 py-5 flex items-center">
            <div class="h-[18px] w-full bg-[#C2C2C2]"></div>
          </div>
          <div
            class="hover:bg-[#F1F2F6] w-10 h-10 flex justify-center items-center rounded-full cursor-pointer"
            @click="setReadUnreadToggleButton"
          >
            <SharedIconHubEmailsUnreadMessageIcon
              v-if="toggleReadUnreadButton"
            />
            <SharedIconHubEmailsReadMessageIcon v-else />
          </div>
          <div
            class="hover:bg-[#F1F2F6] w-10 h-10 flex justify-center items-center rounded-full cursor-pointer relative"
            :class="toggleMoveToMenu ? 'bg-[#F1F2F6]' : ''"
          >
            <SharedIconHubEmailsMoveToIcon @click.stop="settoggleMoveToMenu" />
            <SourceHubEmailsMoveToMenu
              v-if="toggleMoveToMenu"
              @click.stop="toggleMoveToMenu = true"
            />
          </div>
        </div>
        <div
          class="hover:bg-[#F1F2F6] w-10 h-10 flex justify-center items-center rounded-full cursor-pointer relative"
          :class="toggleMarkAllReadMenu ? 'bg-[#F1F2F6]' : ''"
          @click.stop="settoggleMarkAllReadMenu"
        >
          <SharedIconHubEmailsThreeDotMenuIcon />
          <SourceHubEmailsMarkAllReadMenu
            v-if="toggleMarkAllReadMenu"
            :checkedAll="checkedAll"
            :showMinus="showMinus"
          />
        </div>
      </div>
      <div class="flex space-x-8 items-center">
        <div
          class="page-number text-[#707070] text-sm px-2 py-1.5 rounded-lg cursor-pointer hover:bg-[#F1F2F6] relative"
        >
          1-50 of 128
          <SourceHubEmailsOldestNewestMenu class="show-hide hidden" />
        </div>
        <div class="flex space-x-8 items-center">
          <SharedIconHubEmailsLeftArrow />
          <SharedIconHubEmailsRightArrow />
        </div>
      </div>
    </div>
    <div class="w-full h-[calc(100%-58px)] custom-scroll">
      <template v-if="!showSpecificMessage">
        <div
          class="px-4 py-3 grid grid-cols-[0.4fr_1fr_0.1fr] gap-4 border-b border-[#E3E3E3] cursor-pointer relative"
          v-for="emailMessage in emailMessages"
          :key="emailMessage.id"
          :class="
            emailMessage.checked
              ? 'bg-[#D6E7FF]'
              : emailMessage.read
                ? 'bg-[#F2F5FC]'
                : 'bg-[#FFFFFF]'
          "
          @contextmenu="(event) => checkSingleMessage(event, emailMessage.id)"
        >
          <div class="flex space-x-4 items-center">
            <InputsCheckBoxInput
              :id="emailMessage.id"
              v-model="emailMessage.checked"
              :model-value="emailMessage.checked"
              @update:modelValue="toggleSingleCheckBox"
            />
            <SharedIconHubEmailsStarIcon
              v-if="!emailMessage.favourite"
              class="cursor-pointer min-w-4 min-h-4"
              @click.stop="
                store.commit('emails/SET_FAVOURITE_MESSAGE', emailMessage.id)
              "
            />
            <ClientOnly v-else>
              <fa
                class="w-4 h-4 min-w-4 min-h-4 text-yellow-midlight cursor-pointer"
                :icon="['fas', 'star']"
                @click.stop="
                  store.commit('emails/SET_FAVOURITE_MESSAGE', emailMessage.id)
                "
              />
            </ClientOnly>
            <div
              class="line-clamp-1 flex-grow"
              :class="
                emailMessage.read
                  ? 'text-[#707070]'
                  : 'text-[#525252] font-bold'
              "
              @click.stop="setSpecificMessage(emailMessage)"
            >
              {{ emailMessage.from }}
            </div>
          </div>
          <div
            class="flex space-x-2"
            @click.stop="setSpecificMessage(emailMessage)"
          >
            <div
              class="whitespace-nowrap"
              :class="
                emailMessage.read
                  ? 'text-[#707070]'
                  : 'text-[#525252] font-bold'
              "
              v-html="emailMessage.subject"
            ></div>
            <div class="line-clamp-1" v-html="emailMessage.snippet"></div>
          </div>
          <div
            class="text-sm flex items-center justify-end"
            :class="
              emailMessage.read ? 'text-[#707070]' : 'text-[#525252] font-bold'
            "
            @click.stop="setSpecificMessage(emailMessage)"
          >
            {{ emailMessage.createdAt }}
          </div>
          <Transition name="page">
            <SourceHubEmailsSingleMessageMenu
              v-if="emailMessage.checked && showMenu"
              id="menu"
              ref="menu"
              :style="{
                top: `${position.y}px`,
                left: `${position.x}px`,
              }"
              :message-read="emailMessage.read"
              :specific-id="emailMessage.id"
            />
          </Transition>
        </div>
      </template>
      <NuxtPage v-else-if="showSpecificMessage" />
    </div>
  </div>
</template>

<style scoped>
.page-number:hover .show-hide {
  display: flex;
}
</style>
