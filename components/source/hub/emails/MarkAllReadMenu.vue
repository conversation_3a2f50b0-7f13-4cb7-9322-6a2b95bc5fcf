<script setup lang="ts">
import { useStore } from 'vuex'
import ReadMessageIcon from '~/components/shared/icon/hub/emails/ReadMessageIcon.vue'
import Snooze from '~/components/shared/icon/hub/emails/Snooze.vue'
import AddTasks from '~/components/shared/icon/hub/emails/AddTasks.vue'
import Label from '~/components/shared/icon/hub/emails/Label.vue'
import Attachment from '~/components/shared/icon/hub/emails/Attachment.vue'
import Filter from '~/components/shared/icon/hub/emails/Filter.vue'
import Mute from '~/components/shared/icon/hub/emails/Mute.vue'
import Switch from '~/components/shared/icon/hub/emails/Switch.vue'

const store = useStore()

interface Props {
  toggleSingleCheckBox?: Function
  checkedAll?: boolean
  showMinus?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  toggleSingleCheckBox: () => {},
  checkedAll: false,
  showMinus: false,
})

interface MarkAllReadMenu {
  id: number
  image: string | Component
  title: string
  description: string
}

interface ExtendMenu {
  id: number
  image: string | Component
  title: string
}

const markAllReadMenu = ref<MarkAllReadMenu>({
  id: 1,
  image: markRaw(ReadMessageIcon),
  title: 'Make all as read',
  description: 'Select messages to see more actions',
})

const extendMenu = ref<ExtendMenu[]>([
  {
    id: 1,
    image: markRaw(Snooze),
    title: 'Snooze',
  },
  {
    id: 2,
    image: markRaw(AddTasks),
    title: 'Add to Tasks',
  },
  {
    id: 3,
    image: markRaw(Label),
    title: 'Label as',
  },
  {
    id: 4,
    image: markRaw(Attachment),
    title: 'Forward as attachment',
  },
  {
    id: 5,
    image: markRaw(Filter),
    title: 'Filter message like these',
  },
  {
    id: 6,
    image: markRaw(Mute),
    title: 'Mute',
  },
  {
    id: 7,
    image: markRaw(Switch),
    title: 'Switch to advance toolbar',
  },
])

const handleOptionClick = () => {
  store.commit('emails/READ_ALL_MESSAGE_WITH_ALL_UNCHECK')
}
</script>

<template>
  <div
    class="flex flex-col space-y-3.5 bg-white py-2 absolute top-10 left-0"
    :class="
      checkedAll || showMinus ? 'select-extend-menu-box' : 'select-menu-box'
    "
  >
    <div v-if="checkedAll || showMinus" class="flex flex-col">
      <div
        v-for="item in extendMenu"
        :key="item.id"
        class="flex justify-between items-center !space-x-2 px-3.5 py-3 hover:bg-[#F1F2F6] cursor-pointer"
        :class="
          item.id === 2 || item.id === 6 ? 'border-b border-[#C2C2C2]' : ''
        "
      >
        <div class="flex items-center !space-x-2">
          <component :is="item.image" />
          <p class="text-[#525252]">{{ item.title }}</p>
        </div>
        <SharedIconHubEmailsDownArrow
          v-if="item.id === 3"
          class="transform rotate-[-90deg]"
        />
      </div>
    </div>
    <div v-else class="flex flex-col">
      <div
        class="flex !space-x-2 px-3.5 py-3 hover:bg-[#F1F2F6] cursor-pointer border-b border-[#C2C2C2]"
        @click="handleOptionClick"
      >
        <component :is="markAllReadMenu.image" />
        <p class="text-[#525252]">{{ markAllReadMenu.title }}</p>
      </div>
      <p class="px-4 pt-2 text-[#707070] text-sm italic">
        {{ markAllReadMenu.description }}
      </p>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.select-menu-box {
  min-width: 176px;
  max-width: 176px;
  height: fit-content;
  z-index: 1;
  background: #ffffff 0% 0% no-repeat padding-box;
  box-shadow: 2px 2px 4px #22283114;
  border-radius: 8px;
}
.select-extend-menu-box {
  min-width: 244px;
  max-width: 244px;
  height: fit-content;
  z-index: 1;
  background: #ffffff 0% 0% no-repeat padding-box;
  box-shadow: 2px 2px 4px #22283114;
  border-radius: 8px;
}
</style>
