<script setup lang="ts">
import { addDays, format } from 'date-fns'
import { toZonedTime } from 'date-fns-tz'
import { useStore } from 'vuex'

const store = useStore()
onMounted(async () => {
  handleSetExpirationDate(expirationItems.value[1])
})

interface ExpirationItems {
  id: number
  value: number
  text: string
}
const expirationItems = ref<ExpirationItems[]>([
  {
    id: 1,
    value: 1,
    text: 'Expire in 1 day',
  },
  {
    id: 2,
    value: 7,
    text: 'Expire in 1 week',
  },
  {
    id: 3,
    value: 30,
    text: 'Expire in 1 month',
  },
  {
    id: 4,
    value: 90,
    text: 'Expire in 3 months',
  },
  {
    id: 5,
    value: 1825,
    text: 'Expire in 5 years',
  },
])
const utcTimeZone = computed(() => store.state.system.utcTimeZone)
const futureDate = ref<string>('')
const handleSetExpirationDate = (date: ExpirationItems) => {
  futureDate.value = addDays(new Date(), date.value).toISOString()
  const zonedDate = toZonedTime(futureDate.value, utcTimeZone.value)

  futureDate.value = format(zonedDate, 'EE, MMM dd, yyyy')
}
const hideModal = () => {
  store.commit('emails/SET_CONFIDENTIAL_MODE_MODAL', false)
}
</script>

<template>
  <div
    class="max-w-[678px] rounded-2xl bg-white fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-1"
  >
    <div
      class="py-4 px-4 flex justify-between items-center border-b border-[#F1F2F6]"
    >
      <span class="text-lg text-[#505050] font-semibold"
        >Confidential mode</span
      >
      <SharedIconHubEmailsCrossIcon class="cursor-pointer" @click="hideModal" />
    </div>

    <div class="dialog-content py-[18px] px-4 border-b border-[#F1F2F6]">
      <p class="text-lg text-[#707070]">
        Recipients won’t have the option to forward, copy, print, or download
        this email. <a href="#" class="text-[#4A71D4] underline">Learn more</a>
      </p>

      <div class="mt-5 flex flex-col space-y-2">
        <label
          for="expiration"
          class="text-[#525252] font-semibold text-sm block"
          >SET EXPIRATION</label
        >
        <div class="flex !space-x-4 items-center">
          <BaseDropsDown
            :options="expirationItems"
            idKey="id"
            labelKey="text"
            placeholder="Expire in 1 week"
            :menuWidth="240"
            :menuHeight="35"
            :dropdownWidth="240"
            :dropdownMaxHeight="290"
            menuTextColor="#525252"
            dropsdownTextColor="#525252"
            menuBgColor="#F1F2F6"
            scrollbarTrackColor="#a1cdff50"
            scrollbarThumbColor="#a1cdff"
            @change="handleSetExpirationDate"
          />
          <p class="text-lg text-[#525252]">{{ futureDate }}</p>
        </div>
      </div>
      <div class="flex flex-col space-y-1.5 mt-[22px]">
        <p class="text-[#525252] font-semibold text-sm block">
          REQUIRE PASSCODE
        </p>
        <div class="flex space-x-2 items-center">
          <p class="text-[#525252]">
            All passcodes will be generated by Google.
          </p>
          <SharedIconHubEmailsQueryIcon />
        </div>
      </div>
      <div class="mt-4 w-full flex items-center space-x-[99px]">
        <label class="radio-label">
          <input type="radio" name="sms" value="no" checked />
          <div class="flex items-center radio-custom-wrapper">
            <span class="radio-custom"></span
            ><span class="ml-2.5">No SMS passcode </span>
          </div>
        </label>

        <label class="radio-label flex !space-x-2.5 items-center">
          <input type="radio" name="sms" value="yes" />
          <div class="flex items-center radio-custom-wrapper">
            <span class="radio-custom"></span
            ><span class="ml-2.5">SMS passcode </span>
          </div>
        </label>

        <!-- <div class="radio-group">
          <input
            type="radio"
            id="no-sms"
            name="passcode"
            value="no-sms"
            checked
          />
          <label for="no-sms">No SMS passcode</label>
        </div>
        <div class="radio-group">
          <input type="radio" id="sms" name="passcode" value="sms" />
          <label for="sms">SMS passcode</label>
        </div> -->
      </div>
    </div>

    <div class="py-4 px-4 w-full flex justify-end space-x-2">
      <button
        class="w-26 h-[35px] flex justify-center items-center rounded-full border border-[#4A71D4] text-[#4A71D4] font-semibold"
        id="closeDialog2"
        @click="hideModal"
      >
        Cancel
      </button>
      <button
        class="w-26 h-[35px] flex justify-center items-center rounded-full bg-[#4A71D4] text-white font-semibold"
        @click="hideModal"
      >
        Save
      </button>
    </div>
  </div>
</template>

<style scoped>
/* Hide the default radio button */
.radio-label input[type='radio'] {
  display: none;
}

/* Create a custom radio button */
.radio-label .radio-custom-wrapper .radio-custom {
  display: flex;
  height: 16px;
  width: 16px;
  min-width: 16px;
  border: 2px solid #ccc;
  border-radius: 50%;
  transition: border 0.3s;
}

/* Style the radio button when checked */
.radio-label input[type='radio']:checked + .radio-custom-wrapper .radio-custom {
  border-color: #4a71d4;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* Create the checked indicator (the blue dot) */
.radio-label .radio-custom-wrapper .radio-custom:after {
  content: '';
  position: absolute;
  display: none;
}

.radio-label
  input[type='radio']:checked
  + .radio-custom-wrapper
  .radio-custom:after {
  display: block;
}

.radio-label .radio-custom-wrapper .radio-custom:after {
  height: 8px;
  width: 8px;
  border-radius: 50%;
  background: #4a71d4;
}
</style>
