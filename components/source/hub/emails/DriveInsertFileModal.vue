<script setup lang="ts">
import { useStore } from 'vuex'

const store = useStore()

import {
  parse,
  isToday,
  isYesterday,
  isThisWeek,
  isThisMonth,
  startOfWeek,
  endOfWeek,
  subWeeks,
  subMonths,
  isBefore,
  isAfter,
} from 'date-fns'

interface ButtonOptions {
  id: number
  text: string
  select: boolean
}
const buttonOptions = ref<ButtonOptions[]>([
  {
    id: 1,
    text: 'Recent',
    select: true,
  },
  {
    id: 2,
    text: 'My Drive',
    select: false,
  },
  {
    id: 3,
    text: 'Shared with me',
    select: false,
  },
  {
    id: 4,
    text: 'Upload',
    select: false,
  },
])
const setButtonOptionsSelect = (id: number) => {
  buttonOptions.value.forEach((buttonOption: ButtonOptions) => {
    if (buttonOption.id === id) {
      buttonOption.select = !buttonOption.select
    } else {
      buttonOption.select = false
    }
  })
}
interface InsertFiles {
  id: number
  img: string
  title: string
  date: string
  select: boolean
}
const insertFiles = ref<InsertFiles[]>([
  {
    id: 1,
    img: '/social/email/1.png',
    title: 'Minecraft-1',
    date: '28-02-2025',
    select: false,
  },
  {
    id: 2,
    img: '/social/email/2.png',
    title: 'Minecraft-thumbnail',
    date: '28-02-2025',
    select: false,
  },
  {
    id: 3,
    img: '/social/email/3.png',
    title: 'Minecraft-thumbnail',
    date: '27-02-2025',
    select: false,
  },
  {
    id: 4,
    img: '/social/email/4.png',
    title: 'Minecraft-1',
    date: '27-02-2025',
    select: false,
  },
  {
    id: 5,
    img: '/social/email/5.png',
    title: 'Minecraft-thumbnail',
    date: '27-02-2025',
    select: false,
  },
  {
    id: 6,
    img: '/social/email/1.png',
    title: 'Minecraft-thumbnail',
    date: '26-02-2025',
    select: false,
  },
  {
    id: 7,
    img: '/social/email/2.png',
    title: 'Minecraft-thumbnail',
    date: '22-02-2025',
    select: false,
  },
  {
    id: 8,
    img: '/social/email/3.png',
    title: 'Minecraft-thumbnail',
    date: '15-02-2025',
    select: false,
  },
  {
    id: 9,
    img: '/social/email/4.png',
    title: 'Minecraft-thumbnail',
    date: '10-01-2025',
    select: false,
  },
])
const itemSelect = ref<boolean>(false)
// Helper function to parse date
const parseDate = (dateStr: string) => {
  console.log(dateStr, parse(dateStr, 'dd-MM-yyyy', new Date()))
  return parse(dateStr, 'dd-MM-yyyy', new Date())
}
// Grouped files
const groupedFiles = computed(() => {
  const today = new Date()
  const startOfThisWeek = startOfWeek(today, { weekStartsOn: 1 }) // Monday start
  const startOfLastWeek = startOfWeek(subWeeks(today, 1), { weekStartsOn: 1 })
  const endOfLastWeek = endOfWeek(subWeeks(today, 1), { weekStartsOn: 1 })
  const startOfLastMonth = subMonths(today, 1)

  return {
    Today: insertFiles.value.filter((file: InsertFiles) =>
      isToday(parseDate(file.date)),
    ),
    Yesterday: insertFiles.value.filter((file: InsertFiles) =>
      isYesterday(parseDate(file.date)),
    ),
    'Earlier this week': insertFiles.value.filter(
      (file: InsertFiles) =>
        isThisWeek(parseDate(file.date)) &&
        !isToday(parseDate(file.date)) &&
        !isYesterday(parseDate(file.date)),
    ),
    'Last week': insertFiles.value.filter(
      (file: InsertFiles) =>
        isAfter(parseDate(file.date), startOfLastWeek) &&
        isBefore(parseDate(file.date), endOfLastWeek),
    ),
    'Last month': insertFiles.value.filter(
      (file: InsertFiles) =>
        isThisMonth(parseDate(file.date)) &&
        isBefore(parseDate(file.date), startOfLastWeek),
    ),
    Older: insertFiles.value.filter((file: InsertFiles) =>
      isBefore(parseDate(file.date), startOfLastMonth),
    ),
  }
})
const selectedInsertFile = (files: InsertFiles[], id: number) => {
  files.forEach((file: InsertFiles) => {
    if (file.id === id) {
      file.select = !file.select
    }
  })
  itemSelect.value = insertFiles.value.some((file: InsertFiles) => file.select)
}
const resetAllSeletedFiles = () => {
  insertFiles.value.forEach((insertFile: InsertFiles) => {
    insertFile.select = false
  })
  itemSelect.value = false
}
const toggleListGrid = ref<boolean>(false)
</script>

<template>
  <div
    class="overflow-hidden max-w-[1280px] w-[90%] max-h-[622px] h-full rounded-2xl bg-white fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-[999999999]"
  >
    <div class="max-h-[622px] h-full rounded-2xl bg-white overflow-hidden">
      <div @click="resetAllSeletedFiles">
        <div
          class="flex items-center justify-between px-6 py-3.5 border-b border-[#f1f1f2]"
        >
          <p class="text-[#505050] text-lg font-semibold">Insert File</p>
          <SharedIconHubEmailsCrossIcon
            class="cursor-pointer"
            @click.stop="
              store.commit('emails/SET_DRIVE_INSERT_FILE_MODAL', false)
            "
          />
        </div>
        <div class="px-6 py-4 border-b border-[#f1f1f2]">
          <div class="grid grid-cols-[268px_1fr] gap-[108px] items-center">
            <div class="text-left flex !space-x-2">
              <SharedIconHubEmailsDriveColorIcon />
              <p class="text-[#525252] text-lg font-semibold whitespace-nowrap">
                Insert file using Google Drive
              </p>
            </div>
            <div
              class="flex items-center justify-between space-x-3 bg-[#F1F2F6] px-[26px] py-2.5 rounded-full max-w-[480px]"
            >
              <div class="flex-grow">
                <div class="flex space-x-2 items-center">
                  <fa class="text-[#707070]" :icon="['fas', 'search']" />
                  <input
                    class="text-[#707070] bg-[#F1F2F6] flex-grow border-none outline-none"
                    type="text"
                    placeholder="Search in Drive or paste URL"
                  />
                </div>
              </div>
              <SharedIconHubEmailsFilterIcon />
            </div>
          </div>
          <div class="mt-8 flex space-x-4 w-full">
            <button
              v-for="buttonOption in buttonOptions"
              :key="buttonOption.id"
              class="px-6 w-fit h-[35px] rounded-full font-semibold flex justify-center items-center"
              :class="
                buttonOption.select
                  ? 'text-white bg-[#4A71D4]'
                  : 'bg-[#f1f2f6] text-[#525252]'
              "
              @click="setButtonOptionsSelect(buttonOption.id)"
            >
              {{ buttonOption.text }}
            </button>
          </div>
        </div>
        <div
          class="px-6 py-4 border-b border-[#f1f1f2] flex justify-between items-center"
        >
          <p class="text-[#707070]">Recent</p>
          <SharedIconHubEmailsListIcon
            v-if="!toggleListGrid"
            @click="toggleListGrid = true"
            class="cursor-pointer"
          />
          <SharedIconHubEmailsGridIcon
            v-else
            @click="toggleListGrid = false"
            class="cursor-pointer"
          />
        </div>
        <div
          v-if="toggleListGrid"
          class="px-6 py-4 border-b border-[#f1f1f2] flex justify-between items-center"
        >
          <p class="text-[#707070] font-semibold">Name</p>
        </div>
      </div>
      <div
        v-if="!toggleListGrid"
        class="px-6 py-6 pb-26 flex flex-col space-y-6 h-[calc(100%-258px)] custom-scroll"
        @click="resetAllSeletedFiles"
      >
        <div
          v-for="(files, category) in groupedFiles"
          :key="category"
          class="flex flex-col space-y-4"
          :class="files.length > 0 ? '' : '!mt-0'"
        >
          <h3 v-if="files.length > 0" class="text-[#707070]">{{ category }}</h3>
          <div
            v-if="files.length > 0"
            class="grid grid-cols-[repeat(auto-fit,minmax(min(240px,100%),240px))] gap-2"
          >
            <div
              class="max-w-[240px] max-h-[240px] h-[240px] cursor-pointer overflow-hidden relative"
              :class="file.select ? 'active-file-box' : 'file-box'"
              v-for="file in files"
              :key="file.id"
              @click.stop="selectedInsertFile(files, file.id)"
            >
              <img
                v-if="file.img"
                class="w-full"
                :src="file.img"
                :alt="file.title"
                width="100"
              />
              <div
                class="w-full flex !space-x-2.5 items-center px-5 py-4 absolute bottom-0"
                :class="file.select ? 'bg-[#e3efff]' : 'bg-white'"
              >
                <img
                  width="16"
                  height="16"
                  src="/social/email/image-color-icon.png"
                  alt="image color icon"
                />
                <p class="text-[#707070]">{{ file.title }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        v-else
        class="py-6 pb-26 flex flex-col space-y-0 h-[calc(100%-292px)] custom-scroll"
        @click="resetAllSeletedFiles"
      >
        <div
          v-for="(files, category) in groupedFiles"
          :key="category"
          class="flex flex-col space-y-0"
          :class="files.length > 0 ? '' : '!mt-0'"
        >
          <div
            v-if="files.length > 0"
            class="px-6 py-4 border-b border-[#f1f1f2]"
          >
            <h3 class="text-[#707070]">
              {{ category }}
            </h3>
          </div>
          <div v-if="files.length > 0" class="flex flex-col">
            <div
              :tabindex="file.id"
              class="w-full leading-[46px] px-6 py-3 flex !space-x-4 items-center cursor-pointer overflow-hidden relative"
              :class="
                file.select
                  ? 'bg-[#e3efff] focus:border focus:border-[#4A71D4]'
                  : 'bg-white hover:bg-[#F1F2F6] focus:bg-[#e0e0e3] focus:border-t focus:border-t-[#4A71D4] border border-b-[#f1f1f2] border-t-transparent border-l-transparent border-r-transparent last:border-b last:border-b-transparent'
              "
              v-for="file in files"
              :key="file.id"
              @click.stop="selectedInsertFile(files, file.id)"
            >
              <div class="w-8 h-8">
                <img
                  v-if="file.img"
                  class="w-8 h-8"
                  :src="file.img"
                  :alt="file.title"
                />
              </div>
              <p class="text-[#707070]">{{ file.title }}</p>
            </div>
          </div>
        </div>
      </div>
      <Transition name="slide-up">
        <div
          v-if="itemSelect"
          class="w-full py-4 px-6 flex justify-end space-x-2 border-t border-[#f1f1f2] bg-white sticky bottom-0 left-0 z-1"
        >
          <button
            class="w-fit h-[35px] px-6 flex justify-center items-center border border-[#4A71D4] text-[#4A71D4] font-semibold rounded-full"
            @click.stop="
              store.commit('emails/SET_DRIVE_INSERT_FILE_MODAL', false)
            "
          >
            Add as attachment
          </button>
          <button
            class="w-fit h-[35px] px-6 flex justify-center items-center bg-[#4A71D4] text-white font-semibold rounded-full"
            @click.stop="
              store.commit('emails/SET_DRIVE_INSERT_FILE_MODAL', false)
            "
          >
            Add as link
          </button>
        </div>
      </Transition>
    </div>
  </div>
</template>

<style scoped>
.file-box {
  background: white;
  border: 1px solid #c2c2c2;
  border-radius: 8px;
  opacity: 1;
}
.active-file-box {
  background: #e3efff;
  border: 1px solid #c2c2c2;
  border-radius: 8px;
  opacity: 1;
}
.slide-up-enter-active,
.slide-up-leave-active {
  transition: transform 0.5s ease;
}
.slide-up-enter-from,
.slide-up-leave-to {
  transform: translateY(100%);
}
</style>
