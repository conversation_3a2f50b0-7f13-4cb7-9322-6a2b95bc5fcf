<script setup lang="ts">
import { useStore } from 'vuex'
import { Editor, EditorContent } from '@tiptap/vue-3'
import StarterKit from '@tiptap/starter-kit'
import TextAlign from '@tiptap/extension-text-align'
import Highlight from '@tiptap/extension-highlight'
import TextStyle from '@tiptap/extension-text-style'
import Color from '@tiptap/extension-color'
import FontFamily from '@tiptap/extension-font-family'
import Underline from '@tiptap/extension-underline'
import Link from '@tiptap/extension-link'
import { CustomIndent } from '~/composables/tiptap-extension-indent'
import { FontSize } from '~/composables/FontSize.ts'
import Image from '@tiptap/extension-image'

const store = useStore()

interface Props {
  setShowReplyBox?: (payload: MouseEvent) => void
  deleteSpecificComposeItem?: Function
  itemId?: number
}

const props = withDefaults(defineProps<Props>(), {
  setShowReplyBox: () => {},
  deleteSpecificComposeItem: (id: number) => {},
  itemId: 0,
})
const showComposeSection = computed(() => store.state.emails.showComposeSection)
const fileChanged = computed(() => store.state.emails.fileChanged)
const fileUrl = computed(() => store.state.emails.fileUrl)
// watch(
//   () => fileChanged.value,
//   (newValue) => {
//     console.log('fileChanged', newValue)
//     if (newValue) {
//       console.log('fileChanged')
//       editor.value
//         ?.chain()
//         .focus('end')
//         .setImage({ src: fileUrl.value })
//         .createParagraphNear() // 🪄 Like pressing "Enter"
//         .selectNodeForward()
//         .run()
//     }
//   },
// )
watch(
  () => fileUrl.value,
  (newValue) => {
    if (newValue) {
      editor.value
        ?.chain()
        .focus('end')
        .setImage({ src: newValue })
        .createParagraphNear() // 🪄 Like pressing "Enter"
        .selectNodeForward()
        .run()
      store.commit('emails/SET_FILE_CHANGED', {
        fileChanged: false,
        fileUrl: '',
      })
    }
  },
)
const isTextSelected = ref(false)
const editor = ref<Editor | null>(null)
onMounted(() => {
  editor.value = new Editor({
    content: `<p>I'm running Tiptap with Vue.js. 🎉</p>`,
    onSelectionUpdate({ editor }) {
      nextTick(() => {
        const { from, to } = editor.state.selection
        isTextSelected.value = from !== to && !editor.state.selection.empty
      })
    },
    extensions: [
      StarterKit.configure({
        blockquote: {
          HTMLAttributes: {
            class: 'border-l-2 border-gray-300 pl-2',
          },
        },
        bulletList: {
          HTMLAttributes: {
            class: 'list-disc ml-3',
          },
        },
        orderedList: {
          HTMLAttributes: {
            class: 'list-decimal ml-3',
          },
        },
      }),
      TextAlign.configure({
        types: ['heading', 'paragraph'],
      }),
      TextStyle.configure({ mergeNestedSpanStyles: true }),
      Color,
      Highlight.configure({
        multicolor: true,
      }),
      FontFamily.configure({
        types: ['textStyle'],
      }),
      Underline,
      CustomIndent.configure({
        types: ['heading', 'paragraph'],
        minIndent: 0,
        maxIndent: 1000,
        indentLevel: 20,
      }),
      FontSize,
      Link.configure({
        openOnClick: true,
        autolink: true,
        HTMLAttributes: {
          class: 'tiptap-link',
          style: 'color: #2563eb; text-decoration: underline; cursor: pointer;',
        },
      }), // Prevents auto-opening links
      Image,
    ],
    editorProps: {
      attributes: {
        class:
          'w-full h-auto no-scrollbar resize-none textarea-style outline-none border-none bg-white text-[#333333]',
      },
    },
  })
  editor.value?.setOptions({
    editorProps: {
      handleDOMEvents: {
        mouseup: () => {
          updatePopupPosition()
        },
      },
    },
  })
  document.addEventListener('click', () => {
    closelinkPopUp()
    // linkPopupVisible.value = false
    // showLinkOptions.value = false
  })
})
onUnmounted(() => {
  document.removeEventListener('click', () => {
    closelinkPopUp()
    // linkPopupVisible.value = false
    // showLinkOptions.value = false
  })
})
const linkPopupVisible = ref(false)
const currentSelection = ref(null)
const linkMenuPosition = ref({ top: 0, left: 0 })
const textSelected = ref(false)
// Show Link Popup
const showLinkPopup = () => {
  updatePopupPosition()
  linkPopupVisible.value = true
}
const hideLinkPopup = () => {
  linkPopupVisible.value = false
}
const selectedLink = ref(null)
const showLinkOptions = ref(false) // Shows the first popup (Go to Link, Change, Remove)
const showEditLink = ref(false) // Shows the second popup (Link input)
const selectedLinks = ref(null) // Stores the selected link
const selectedText = ref<string | null | undefined>('')
const updatePopupPosition = () => {
  nextTick(() => {
    // const editSelection = editor.value?.state.selection
    // console.log(editSelection, 'editSelection')
    // if (!editSelection) return
    // const selection = window.getSelection()
    // if (selection && selection.rangeCount > 0) {
    //   const range = selection.getRangeAt(0)
    //   textSelected.value = !range.collapsed
    //   const rect = range.getBoundingClientRect()
    //   const editorElement = document.querySelector('.ProseMirror')
    //   const editorRect = editorElement?.getBoundingClientRect() ?? {
    //     top: 0,
    //     left: 0,
    //   }
    //   linkMenuPosition.value = {
    //     top: rect.bottom - editorRect.top + 10,
    //     left: rect.left - editorRect.left,
    //   }
    // }
    const selection = editor.value?.state.selection
    if (!selection) return

    const { from, to, empty } = selection
    textSelected.value = !empty // True if text is selected
    // Get link if the cursor is inside one
    const linkMark = editor.value?.getAttributes('link')
    selectedLink.value = linkMark?.href || null

    // if (textSelected.value || selectedLink.value) {
    const coords = editor.value?.view.coordsAtPos(from)
    const editorRect = editor.value?.view.dom.getBoundingClientRect()

    // const node = selection.anchorNode?.parentElement
    // const link = node?.closest('a') // Find nearest <a> tag
    // console.log('link', link, node, linkMark)
    if (linkMark?.href && !empty) {
      selectedLinks.value = linkMark.href
      selectedText.value = empty
        ? ''
        : editor.value?.state.doc.textBetween(from, to, ' ')
      showLinkOptions.value = true // Show first popup
      showEditLink.value = false
    } else {
      selectedLinks.value = null
      showLinkOptions.value = false
    }
    if (coords && editorRect) {
      linkMenuPosition.value = {
        top: coords.top - editorRect.top + 30,
        left: coords.left - editorRect.left,
      }
    }
    // linkPopupVisible.value = true
    // } else {
    //   linkPopupVisible.value = false
    // }
  })
}

const editLink = () => {
  showLinkOptions.value = false
  linkPopupVisible.value = true
  textSelected.value = false
}

const removeLink = () => {
  editor.value?.chain().focus().extendMarkRange('link').unsetLink().run()
  showLinkOptions.value = false
}
// const isTextSelected = () => {
//   if (!editor.value) return false
//   return !editor.value.state.selection.empty
// }
const closelinkPopUp = () => {
  linkPopupVisible.value = false
  showLinkOptions.value = false
  showEditLink.value = false
  selectedLinks.value = null
  selectedText.value = null
}

// ✅ Handle keydown to unset link before typing
const onKeydown = (event) => {
  console.log(event, 'event')
  if (!editor.value) return

  // Only handle Backspace key
  if (event.key === 'Backspace') return

  // We only want to unset the link if we're right after a link
  const { state } = editor.value
  const { from, empty } = state.selection
  if (!empty) return // only when caret, not selection

  const marks = state.storedMarks || state.doc.resolve(from).marks()
  const hasLink = marks.some((mark) => mark.type.name === 'link')

  if (hasLink) {
    console.log('hasLink', hasLink)
    editor.value.chain().focus().unsetMark('link').run()
  }
}
const inputModifierRef = ref<HTMLInputElement | null>(null)
</script>

<template>
  <div class="flex flex-col min-h-[312px]">
    <nav class="flex space-x-2 items-center">
      <div
        v-if="!showComposeSection"
        class="flex space-x-2 items-center pr-2 py-2.5 cursor-pointer rounded-md hover:bg-[#F1F2F6]"
      >
        <SharedIconHubEmailsReply class="w-3.5 h-2.5 min-w-3.5 min-h-2.5" />
        <SharedIconHubEmailsDownArrow class="w-2 h-1.5" />
      </div>
      <div v-if="!showComposeSection">
        <p class="text-[#333333]">
          Foodie Adventures
          <span class="text-[#707070]">&lt;<EMAIL>&gt;</span>
        </p>
      </div>
      <div v-if="showComposeSection" class="w-full text-[#707070]">
        <div class="w-full py-2 pr-4 border-b border-[#F1F2F6]">
          <input
            class="w-full outline-none border-none"
            type="text"
            placeholder="Recipients"
          />
        </div>
        <div class="w-full py-2 pr-4 mt-2 border-b border-[#F1F2F6]">
          <input
            class="w-full outline-none border-none"
            type="text"
            placeholder="Subject"
          />
        </div>
      </div>
    </nav>
    <div class="mt-3 flex-grow relative" @click="closelinkPopUp()">
      <!-- <textarea
        class="w-full h-auto no-scrollbar resize-none textarea-style outline-none border-none bg-white text-[#333333]"
        name=""
        id=""
      ></textarea> @click.stop="" -->
      <editor-content :editor="editor" @keydown="onKeydown" @click.stop="" />
      <!-- Floating Link Popup -->
      <LinkInputPopup
        v-if="linkPopupVisible"
        :editor="editor"
        :selection="currentSelection"
        :position="linkMenuPosition"
        :textSelected="textSelected"
        :selectedLinks="selectedLinks"
        :selectedText="selectedText"
        @close="closelinkPopUp()"
        @click.stop=""
      />
      <div
        v-if="showLinkOptions"
        @click.stop=""
        :style="`position: absolute; top: ${linkMenuPosition.top}px; left: ${linkMenuPosition.left}px; z-index: 100; background: #ffffff; border: 1px solid; border-color: #bbb #bbb #a8a8a8; border-radius: 2px; padding: 2px 10px; box-shadow: 0 1px 3px rgba(0, 0, 0, .2);`"
      >
        <span class="text-[13px] font-medium"
          >Go to link:
          <a
            :href="selectedLinks"
            target="_blank"
            class="text-[#15c] font-medium hover:underline"
            >{{ selectedLinks }}</a
          >
        </span>
        |
        <button
          @click="editLink"
          class="text-[#15c] text-[13px] font-medium hover:underline"
        >
          Change
        </button>
        |
        <button
          @click="removeLink"
          class="text-[#15c] text-[13px] font-medium hover:underline"
        >
          Remove
        </button>
      </div>
      <!-- <SourceHubEmailsLinkAdding /> -->
    </div>
    <LazySourceHubAttachmentFileContainer
      v-if="inputModifierRef && inputModifierRef.attachedFiles.length"
      :attachedFiles="inputModifierRef?.attachedFiles"
      :fileUrls="inputModifierRef?.fileUrls"
      :formatSize="inputModifierRef?.formatSize"
      :removeFile="inputModifierRef?.removeFile"
      :uploadProgress="inputModifierRef?.uploadProgress"
    />
    <div
      v-if="!showComposeSection"
      class="w-[30px] h-3.5 bg-[#F1F2F6] rounded-[5px] flex justify-center items-center"
    >
      <SharedIconHubEmailsThreeDotMenuIcon
        class="transform rotate-90"
        color="#525252"
      />
    </div>
    <div class="mt-4">
      <SourceHubEmailsInputModifier
        ref="inputModifierRef"
        :setShowReplyBox="setShowReplyBox"
        :deleteSpecificComposeItem="deleteSpecificComposeItem"
        :itemId="itemId"
        :editor="editor"
        :showLinkPopup="showLinkPopup"
        :hideLinkPopup="hideLinkPopup"
      />
    </div>
  </div>
</template>

<style lang="scss" scoped>
// .reply-box {
//   background: #ffffff 0% 0% no-repeat padding-box;
//   box-shadow: 1px 2px 8px #00000029;
//   border-radius: 8px;
// }
.textarea-style {
  field-sizing: content;
  width: 100%;
}
</style>
