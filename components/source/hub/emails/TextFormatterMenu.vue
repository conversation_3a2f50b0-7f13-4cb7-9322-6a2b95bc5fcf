<script setup lang="ts">
import { Editor, EditorContent } from '@tiptap/vue-3'
import SharedIconHubEmailsAlignButton from '~/components/shared/icon/hub/emails/AlignButton.vue'
import SharedIconHubEmailsMiddleAlignIcon from '~/components/shared/icon/hub/emails/MiddleAlignIcon.vue'
import SharedIconHubEmailsRightAlignIcon from '~/components/shared/icon/hub/emails/RightAlignIcon.vue'

interface Props {
  editor?: Editor | null
}

const props = withDefaults(defineProps<Props>(), {
  editor: null,
})

const fontFamilies = [
  { label: 'Sans Serif', value: 'sans-serif' },
  { label: 'Serif', value: 'serif' },
  { label: 'Fixed Width', value: 'monospace' },
  { label: 'Wide', value: 'Arial Black' },
  { label: 'Narrow', value: 'Arial Narrow' },
  { label: 'Comic Sans MS', value: 'Comic Sans MS' },
  { label: '<PERSON><PERSON><PERSON>', value: '<PERSON><PERSON><PERSON>' },
  { label: 'Georgia', value: 'Georgia' },
  { label: 'Tahoma', value: 'Tahoma' },
  { label: 'Trebuchet MS', value: 'Trebuchet MS' },
  { label: 'Verdana', value: 'Verdana' },
]
const selectedFont = ref('Sans Serif')
const updatedFontFamily = (label: string, value: string) => {
  selectedFont.value = label
  props.editor?.chain().focus().setFontFamily(value).run()
}
const alignIcons = ref([
  {
    id: 1,
    label: 'LeftAlign',
    value: markRaw(SharedIconHubEmailsAlignButton),
  },
  {
    id: 2,
    label: 'MiddleAlign',
    value: markRaw(SharedIconHubEmailsMiddleAlignIcon),
  },
  {
    id: 3,
    label: 'RightAlign',
    value: markRaw(SharedIconHubEmailsRightAlignIcon),
  },
])
const selectedAlignIcon = ref<string | Component>(
  markRaw(SharedIconHubEmailsAlignButton),
)
const setSelectedIcon = (icon: string | Component, label: string) => {
  selectedAlignIcon.value = icon
  if (label === 'LeftAlign') {
    props.editor?.chain().focus().setTextAlign('left').run()
  } else if (label === 'MiddleAlign') {
    props.editor?.chain().focus().setTextAlign('center').run()
  } else if (label === 'RightAlign') {
    props.editor?.chain().focus().setTextAlign('right').run()
  }
}
const headingArray = ref([
  {
    id: 4,
    label: 'Small',
    fontSize: 14,
    selected: false,
  },
  {
    id: 3,
    label: 'Normal',
    fontSize: 16,
    selected: true,
  },
  {
    id: 2,
    label: 'Large',
    fontSize: 18,
    selected: false,
  },
  {
    id: 1,
    label: 'Huge',
    fontSize: 22,
    selected: false,
  },
])
const setHeading = (id: any, fontSize: number) => {
  // props.editor?.chain().focus().setHeading({ level: id }).run()
  props.editor?.chain().focus().setFontSize(fontSize).run()
  headingArray.value.forEach((heading) => {
    if (heading.id === id) {
      heading.selected = true
    } else {
      heading.selected = false
    }
  })
}

const setFontFamily = (event: Event) => {
  const target = event.target as HTMLSelectElement
  const fontFamily = target.value
  props.editor?.chain().focus().setFontFamily(fontFamily).run()
}

const applyTextColor = (color) => {
  props.editor?.chain().focus().setColor(color).run()
}

const applyBackgroundColor = (color) => {
  props.editor?.chain().focus().setHighlight({ color: color }).run()
}

const showColorPicker = ref(false)
const showAlignModal = ref(false)
const showHeaderModal = ref(false)
const showFontFamilyMenu = ref(false)
onMounted(() => {
  document.addEventListener('click', () => {
    showColorPicker.value = false
    showAlignModal.value = false
    showHeaderModal.value = false
    showFontFamilyMenu.value = false
  })
})
onUnmounted(() => {
  document.removeEventListener('click', () => {
    showColorPicker.value = false
    showAlignModal.value = false
    showHeaderModal.value = false
    showFontFamilyMenu.value = false
  })
})
</script>

<template>
  <div class="text-formatter-box px-4 py-2">
    <div v-if="editor" class="flex space-x-1 items-center">
      <div class="flex items-center">
        <button
          class="px-2 py-0 hover:bg-[#F1F2F6] w-8 h-8 rounded cursor-pointer"
          :disabled="!editor.can().chain().focus().undo().run()"
          @click="
            editor.chain().focus().undo().run(),
              ((showColorPicker = false),
              (showAlignModal = false),
              (showHeaderModal = false),
              (showFontFamilyMenu = false))
          "
        >
          <SharedIconHubEmailsBackButton />
        </button>
        <button
          class="px-2 py-0 hover:bg-[#F1F2F6] w-8 h-8 rounded cursor-pointer"
          :disabled="!editor.can().chain().focus().redo().run()"
          @click="
            editor.chain().focus().redo().run(),
              ((showColorPicker = false),
              (showAlignModal = false),
              (showHeaderModal = false),
              (showFontFamilyMenu = false))
          "
        >
          <SharedIconHubEmailsForwardButton />
        </button>
      </div>
      <div class="w-[1px] h-[20px] bg-[#707070] bg-opacity-25"></div>
      <div
        id="font-family-menu"
        class="hover:bg-[#F1F2F6] w-[113px] h-8 rounded flex justify-between items-center px-2 py-0 cursor-pointer"
        :class="showFontFamilyMenu ? 'bg-[#F1F2F6]' : ''"
        @click.stop="
          (showFontFamilyMenu = !showFontFamilyMenu),
            (showColorPicker = false),
            (showAlignModal = false),
            (showHeaderModal = false)
        "
      >
        <p
          class="whitespace-nowrap text-[#525252] font-semibold text-ellipsis overflow-hidden"
        >
          {{ selectedFont }}
        </p>
        <div class=""><SharedIconHubEmailsDownArrow class="w-2 h-1" /></div>
        <SourceHubEmailsFontFamilyMenu
          v-if="showFontFamilyMenu"
          :options="fontFamilies"
          :updatedFontFamily="updatedFontFamily"
          :label="selectedFont"
        />
      </div>
      <div class="w-[1px] h-[20px] bg-[#707070] bg-opacity-25"></div>
      <div
        class="flex space-x-2 items-center justify-center hover:bg-[#F1F2F6] w-[42px] h-8 rounded cursor-pointer px-2 py-0 relative"
        :class="showHeaderModal ? 'bg-[#F1F2F6]' : ''"
        @click.stop="
          (showHeaderModal = !showHeaderModal),
            (showAlignModal = false),
            (showColorPicker = false),
            (showFontFamilyMenu = false)
        "
      >
        <div><SharedIconHubEmailsSizeButton /></div>
        <div><SharedIconHubEmailsDownArrow class="w-2 h-1" /></div>
        <SourceHubEmailsHeadingMenu
          v-if="showHeaderModal"
          @click.stop=""
          :headingArray="headingArray"
          :setHeading="setHeading"
        />
      </div>
      <div class="w-[1px] h-[20px] bg-[#707070] bg-opacity-25"></div>
      <div class="flex space-x-1.5 items-center">
        <button
          class="px-2 py-0 hover:bg-[#F1F2F6] w-7 h-8 rounded cursor-pointer flex justify-center items-center"
          :disabled="!editor.can().chain().focus().toggleBold().run()"
          :class="{ 'is-active': editor.isActive('bold') }"
          @click="
            editor.chain().focus().toggleBold().run(),
              ((showColorPicker = false),
              (showAlignModal = false),
              (showHeaderModal = false),
              (showFontFamilyMenu = false))
          "
        >
          <SharedIconHubEmailsBoldButton />
        </button>
        <button
          class="px-2 py-0 hover:bg-[#F1F2F6] w-7 h-8 rounded cursor-pointer flex justify-center items-center"
          :disabled="!editor.can().chain().focus().toggleItalic().run()"
          :class="{ 'is-active': editor.isActive('italic') }"
          @click="
            editor.chain().focus().toggleItalic().run(),
              ((showColorPicker = false),
              (showAlignModal = false),
              (showHeaderModal = false),
              (showFontFamilyMenu = false))
          "
        >
          <SharedIconHubEmailsItalicButton />
        </button>
        <button
          class="px-2 py-0 hover:bg-[#F1F2F6] w-7 h-8 rounded cursor-pointer flex justify-center items-center"
          :disabled="!editor.can().chain().focus().toggleUnderline().run()"
          :class="{ 'is-active': editor.isActive('underline') }"
          @click="
            editor.chain().focus().toggleUnderline().run(),
              ((showColorPicker = false),
              (showAlignModal = false),
              (showHeaderModal = false),
              (showFontFamilyMenu = false))
          "
        >
          <SharedIconHubEmailsUnderlineButton />
        </button>
        <div
          class="hover:bg-[#F1F2F6] w-10 h-8 rounded cursor-pointer flex items-center space-x-0.5 px-2 py-0 relative"
          :class="showColorPicker ? 'bg-[#F1F2F6]' : ''"
          @click.stop="
            (showColorPicker = !showColorPicker),
              (showAlignModal = false),
              (showHeaderModal = false),
              (showFontFamilyMenu = false)
          "
        >
          <SharedIconHubEmailsTextColorButton />
          <SharedIconHubEmailsDownArrow class="w-2 h-1" />
          <div
            v-show="showColorPicker"
            class="max-w-[396px] min-w-[396px] max-h-[252px] p-4 pt-3 color-box absolute top-[-254px] min-[1490px]:left-0 min-[1310px]:-left-[180px] right-0"
            @click.stop=""
          >
            <div class="grid grid-cols-[1fr_1fr] gap-3">
              <div class="flex flex-col space-y-3.5">
                <p class="text-[#525252]">Background color</p>
                <SourceColorPicker
                  :applyColor="applyBackgroundColor"
                  title="Background color"
                  selectedColorCode="#FFFFFF"
                />
              </div>
              <div class="flex flex-col space-y-3.5">
                <p class="text-[#525252]">Text color</p>
                <SourceColorPicker
                  :applyColor="applyTextColor"
                  title="Text color"
                  selectedColorCode="#000000"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="w-[1px] h-[20px] bg-[#707070] bg-opacity-25"></div>
      <div class="flex space-x-1.5 items-center">
        <div
          class="flex items-center justify-center hover:bg-[#F1F2F6] w-[42px] h-8 rounded cursor-pointer space-x-0.5 px-2 py-0 relative"
          :class="showAlignModal ? 'bg-[#F1F2F6]' : ''"
          @click.stop="
            (showAlignModal = !showAlignModal),
              (showColorPicker = false),
              (showHeaderModal = false),
              (showFontFamilyMenu = false)
          "
        >
          <component :is="selectedAlignIcon" />
          <SharedIconHubEmailsDownArrow class="w-2 h-1" />
          <SourceHubEmailsTextAlignModal
            v-if="showAlignModal"
            @click.stop=""
            :icons="alignIcons"
            :setSelectedIcon="setSelectedIcon"
            :editor="editor"
          />
        </div>
        <button
          class="px-2 py-0 hover:bg-[#F1F2F6] w-8 h-8 rounded cursor-pointer flex justify-center items-center"
          :class="{ 'is-active': editor.isActive('orderedList') }"
          @click="
            editor.chain().focus().toggleOrderedList().run(),
              (showColorPicker = false),
              (showAlignModal = false),
              (showHeaderModal = false),
              (showFontFamilyMenu = false)
          "
        >
          <SharedIconHubEmailsNumberListButton />
        </button>
        <button
          class="px-2 py-0 hover:bg-[#F1F2F6] w-8 h-8 rounded cursor-pointer flex justify-center items-center"
          :class="{ 'is-active': editor.isActive('bulletList') }"
          @click="
            editor.chain().focus().toggleBulletList().run(),
              ((showColorPicker = false),
              (showAlignModal = false),
              (showHeaderModal = false),
              (showFontFamilyMenu = false))
          "
        >
          <SharedIconHubEmailsBulletListButton />
        </button>
        <button
          class="px-2 py-0 hover:bg-[#F1F2F6] w-8 h-8 rounded cursor-pointer flex justify-center items-center"
          @click="
            editor.chain().focus().decreaseIndent().run(),
              ((showColorPicker = false),
              (showAlignModal = false),
              (showHeaderModal = false),
              (showFontFamilyMenu = false))
          "
        >
          <SharedIconHubEmailsIndentLessButton />
        </button>
        <button
          class="px-2 py-0 hover:bg-[#F1F2F6] w-8 h-8 rounded cursor-pointer flex justify-center items-center"
          @click="
            editor.chain().focus().increaseIndent().run(),
              ((showColorPicker = false),
              (showAlignModal = false),
              (showHeaderModal = false),
              (showFontFamilyMenu = false))
          "
        >
          <SharedIconHubEmailsIndentMoreButton />
        </button>
        <button
          class="px-2 py-0 hover:bg-[#F1F2F6] w-8 h-8 rounded cursor-pointer flex justify-center items-center"
          :class="{ 'is-active': editor.isActive('blockquote') }"
          @click="
            editor.chain().focus().toggleBlockquote().run(),
              ((showColorPicker = false),
              (showAlignModal = false),
              (showHeaderModal = false),
              (showFontFamilyMenu = false))
          "
        >
          <SharedIconHubEmailsQuoteButton />
        </button>
      </div>
      <div class="w-[1px] h-[20px] bg-[#707070] bg-opacity-25"></div>
      <div class="flex items-center">
        <button
          class="px-2 py-0 hover:bg-[#F1F2F6] w-8 h-8 rounded cursor-pointer flex justify-center items-center"
          :disabled="!editor.can().chain().focus().toggleStrike().run()"
          :class="{ 'is-active': editor.isActive('strike') }"
          @click="
            editor.chain().focus().toggleStrike().run(),
              ((showColorPicker = false),
              (showAlignModal = false),
              (showHeaderModal = false),
              (showFontFamilyMenu = false))
          "
        >
          <SharedIconHubEmailsStrikeThrough />
        </button>
      </div>
      <div class="w-[1px] h-[20px] bg-[#707070] bg-opacity-25"></div>
      <div class="flex items-center">
        <button
          class="px-2 py-0 hover:bg-[#F1F2F6] w-8 h-8 rounded cursor-pointer flex justify-center items-center"
          @click="
            editor.chain().focus().unsetAllMarks().run(),
              editor.chain().focus().clearNodes().run(),
              ((showColorPicker = false),
              (showAlignModal = false),
              (showHeaderModal = false),
              (showFontFamilyMenu = false)),
              applyTextColor('#000000'),
              applyBackgroundColor('#ffffff')
          "
        >
          <SharedIconHubEmailsRemoveFormattingButton />
        </button>
      </div>
    </div>
  </div>
</template>

<style scoped>
.text-formatter-box {
  background: #ffffff 0% 0% no-repeat padding-box;
  box-shadow: 1px 2px 8px #00000029;
  border-radius: 8px;
}
.color-box {
  background: #ffffff 0% 0% no-repeat padding-box;
  box-shadow: 2px 2px 4px #22283114;
  border-radius: 8px;
}
.is-active {
  background-color: #f1f2f6;
}
</style>
