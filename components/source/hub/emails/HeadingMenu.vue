<script setup lang="ts">
interface Props {
  headingArray?: Array<{
    id: number
    label: string
    fontSize: string
    selected: boolean
  }>
  setHeading?: Function
}

const props = withDefaults(defineProps<Props>(), {
  headingArray: [],
  setHeading: (id: number, fontSize: number) => {},
})
</script>

<template>
  <div
    class="h-[160px] min-h-[160px] w-[172px] min-w-[172px] px-1 py-1 flex flex-col space-y-0 align-box absolute -top-[162px] -left-[8px] z-10"
  >
    <div
      v-for="heading in headingArray"
      :key="heading.id"
      @click="setHeading(heading.id, heading.fontSize)"
      class="px-2 py-1.5 hover:bg-[#F1F2F6] rounded cursor-pointer grid !gap-x-2.5 grid-cols-[16px_1fr] items-center"
    >
      <fa
        class="text-[#707070]"
        :class="heading.selected ? 'opacity-100' : 'opacity-0'"
        :icon="['fa', 'fa-check']"
      />
      <p class="#525252" :style="{ fontSize: `${heading.fontSize}px` }">
        {{ heading.label }}
      </p>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.align-box {
  background: #ffffff 0% 0% no-repeat padding-box;
  box-shadow: 2px 2px 4px #22283114;
  border-radius: 8px;
}
</style>
