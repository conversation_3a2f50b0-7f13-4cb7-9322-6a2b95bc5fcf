<script setup lang="ts">
import { useStore } from 'vuex'

import Inbox from '~/components/shared/icon/hub/emails/sidebar/Inbox.vue'
import Starred from '~/components/shared/icon/hub/emails/sidebar/Starred.vue'
import Snoozed from '~/components/shared/icon/hub/emails/sidebar/Snoozed.vue'
import Sent from '~/components/shared/icon/hub/emails/sidebar/Sent.vue'
import Drafts from '~/components/shared/icon/hub/emails/sidebar/Drafts.vue'
import Spam from '~/components/shared/icon/hub/emails/sidebar/Spam.vue'
import Important from '~/components/shared/icon/hub/emails/sidebar/Important.vue'
import Scheduled from '~/components/shared/icon/hub/emails/sidebar/Scheduled.vue'
import AllMail from '~/components/shared/icon/hub/emails/sidebar/AllMail.vue'
import Trash from '~/components/shared/icon/hub/emails/sidebar/Trash.vue'

const store = useStore()
const route = useRoute()

const menuItems = ref([
  {
    id: 1,
    image: markRaw(Inbox),
    name: 'Inbox',
    path: `/source/hub/emails/${route.params.slug}/${route.params.categories}/inbox`,
    unread: 2,
  },
  {
    id: 2,
    image: markRaw(Starred),
    name: 'Starred',
    path: `/source/hub/emails/${route.params.slug}/${route.params.categories}/starred`,
    unread: 0,
  },
  {
    id: 3,
    image: markRaw(Snoozed),
    name: 'Snoozed',
    path: `/source/hub/emails/${route.params.slug}/${route.params.categories}/snoozed`,
    unread: 0,
  },
  {
    id: 4,
    image: markRaw(Sent),
    name: 'Sent',
    path: `/source/hub/emails/${route.params.slug}/${route.params.categories}/sent`,
    unread: 0,
  },
  {
    id: 5,
    image: markRaw(Drafts),
    name: 'Drafts',
    path: `/source/hub/emails/${route.params.slug}/${route.params.categories}/drafts`,
    unread: 0,
  },
  {
    id: 6,
    image: markRaw(Spam),
    name: 'Spam',
    path: `/source/hub/emails/${route.params.slug}/${route.params.categories}/spam`,
    unread: 0,
  },
  {
    id: 7,
    image: markRaw(Important),
    name: 'Important',
    path: `/source/hub/emails/${route.params.slug}/${route.params.categories}/important`,
    unread: 0,
  },
  {
    id: 8,
    image: markRaw(Scheduled),
    name: 'Scheduled',
    path: `/source/hub/emails/${route.params.slug}/${route.params.categories}/scheduled`,
    unread: 0,
  },
  {
    id: 9,
    image: markRaw(AllMail),
    name: 'All Mail',
    path: `/source/hub/emails/${route.params.slug}/${route.params.categories}/allmail`,
    unread: 0,
  },
  {
    id: 10,
    image: markRaw(Trash),
    name: 'Trash',
    path: `/source/hub/emails/${route.params.slug}/${route.params.categories}/trash`,
    unread: 0,
  },
])
onMounted(async () => {
  setTimeout(async() => {
    await nextTick
    const NuxtLink = document.querySelector('.nuxt-link')
    if (NuxtLink) {
      NuxtLink.click()
    }
  }, 100)
})
</script>

<template>
  <div class="flex flex-col p-4">
    <NuxtLink
      :to="menuItem.path"
      class="nuxt-link flex justify-between px-4 py-2 items-center rounded-full"
      v-for="menuItem in menuItems"
      :key="menuItem.id"
      @click.native="store.commit('emails/RESET_SHOW_COMPOSE_SECTION')"
    >
      <div class="menu-container flex !space-x-[18px] items-center">
        <component v-if="menuItem.image" :is="menuItem.image"></component>
        <p>{{ menuItem.name }}</p>
      </div>
      <p v-if="menuItem.unread > 0" class="text-sm">{{ menuItem.unread }}</p>
    </NuxtLink>
  </div>
</template>

<style lang="scss" scoped>
.router-link-exact-active,
.router-link-active {
  @apply bg-blue-200 text-white;
}
</style>
