<script setup lang="ts">
interface SignatureMenuOptions {
  id: number
  label: string
  selected: boolean
}

const signatureMenuOptions = ref<SignatureMenuOptions[]>([
  {
    id: 1,
    label: 'Manage signature',
    selected: false,
  },
  {
    id: 2,
    label: 'No signature',
    selected: true,
  },
])
const setSignatureSelect = (id: number) => {
  signatureMenuOptions.value.forEach(
    (signatureMenuOption: SignatureMenuOptions) => {
      if (signatureMenuOption.id === id) {
        signatureMenuOption.selected = true
      } else {
        signatureMenuOption.selected = false
      }
    },
  )
}
</script>

<template>
  <div
    class="menu-box absolute xl:left-0 right-0 top-[-79px] w-[188px] py-[1px]"
  >
    <div
      class="grid grid-cols-[16px_1fr] gap-x-2.5 items-center px-[18px] py-[7px] border-opacity-50 border-b border-[#C2C2C2] last:border-none hover:bg-[#F1F2F6] cursor-pointer"
      v-for="signatureMenuOption in signatureMenuOptions"
      :key="signatureMenuOption.id"
      @click="setSignatureSelect(signatureMenuOption.id)"
    >
      <fa
        class="text-[#707070]"
        :class="signatureMenuOption.selected ? 'opacity-100' : 'opacity-0'"
        :icon="['fa', 'fa-check']"
      />
      <p class="#525252 whitespace-nowrap">
        {{ signatureMenuOption.label }}
      </p>
    </div>
  </div>
</template>

<style scoped>
.menu-box {
  background: #ffffff 0% 0% no-repeat padding-box;
  box-shadow: 2px 2px 4px #22283114;
  border-radius: 8px;
}
</style>
