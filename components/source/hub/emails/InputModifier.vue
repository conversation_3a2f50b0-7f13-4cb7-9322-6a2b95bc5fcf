<script setup lang="ts">
import { useStore } from 'vuex'
import { Editor, EditorContent } from '@tiptap/vue-3'

const emit = defineEmits(['fileChange'])

interface Props {
  setShowReplyBox?: Function
  deleteSpecificComposeItem?: Function
  showLinkPopup?: Function
  itemId?: number
  editor?: Editor | null
  hideLinkPopup?: Function
}

const props = withDefaults(defineProps<Props>(), {
  setShowReplyBox: () => {},
  deleteSpecificComposeItem: (id: number) => {},
  showLinkPopup: () => {},
  itemId: 0,
  editor: null,
  hideLinkPopup: () => {},
})

const store = useStore()
const showTextFormattedMenu = ref(false)
const dialogElem = ref<HTMLElement | null>(null)
const showConfidentialModeModal = computed(
  () => store.state.emails.showConfidentialModeModal,
)
const showEmojiPicker = ref<boolean>(false)
const showSignatureMenu = ref<boolean>(false)
const showMoreOptions = ref<boolean>(false)
onMounted(() => {
  document.addEventListener('click', () => {
    // showTextFormattedMenu.value = false
    showEmojiPicker.value = false
    showSignatureMenu.value = false
    showMoreOptions.value = false
  })
  dialogElem.value = document.getElementById('confidentialDialog')
})
onUnmounted(() => {
  document.removeEventListener('click', () => {
    showEmojiPicker.value = false
    showSignatureMenu.value = false
    showMoreOptions.value = false
    // showTextFormattedMenu.value = false
  })
})
const toggleEmojiPicker = () => {
  showEmojiPicker.value = !showEmojiPicker.value
  showSignatureMenu.value = false
  showMoreOptions.value = false
  props.hideLinkPopup()
}
const fileInput = ref<HTMLInputElement>()
const triggerFileInput = () => {
  fileInput.value?.click()
}
const MAX_FILE_SIZE_MB = 25

const attachedFiles = ref<File[]>([])
const fileUrls = ref<string[]>([])
const uploadProgress = ref<number[]>([])
const { $toast } = useNuxtApp()
// Handle file input
const handleFileChange = (event: Event) => {
  const input = event.target as HTMLInputElement
  const files = input.files
  if (!files) return

  for (const file of files) {
    const fileSizeMB = file.size / (1024 * 1024)
    if (fileSizeMB > MAX_FILE_SIZE_MB) {
      $toast('error', {
        message: `File "${file.name}" exceeds the 25MB limit.`,
        className: 'toasted-bg-alert',
      })
      continue
    }
    const index = attachedFiles.value.length
    attachedFiles.value.push(file)
    const url = URL.createObjectURL(file)
    fileUrls.value.push(url)
    uploadProgress.value.push(0)
    simulateUploadProgress(index) // Simulated for now
  }
  if (fileInput.value) fileInput.value.value = ''
}

// Simulate file upload progress (replace this with actual upload logic)
const simulateUploadProgress = (index: number) => {
  const interval = setInterval(() => {
    if (uploadProgress.value[index] >= 100) {
      clearInterval(interval)
    } else {
      uploadProgress.value[index] += 10
    }
  }, 200)
}

// Remove file
const removeFile = (index: number) => {
  URL.revokeObjectURL(fileUrls.value[index])
  attachedFiles.value.splice(index, 1)
  fileUrls.value.splice(index, 1)
  uploadProgress.value.splice(index, 1)
}
// Format file size
const formatSize = (size: number) => {
  const mb = size / (1024 * 1024)
  return mb >= 1 ? `${mb.toFixed(2)} MB` : `${(size / 1024).toFixed(2)} KB`
}
defineExpose({
  attachedFiles,
  removeFile,
  fileUrls,
  formatSize,
  uploadProgress,
})
</script>

<template>
  <div class="flex justify-between items-center relative">
    <div class="flex space-x-4 items-center">
      <button
        class="flex justify-center items-center rounded-full h-[33px] bg-[#4A71D4] text-white text-sm font-semibold"
      >
        <div
          class="flex justify-center items-center px-6 border-r border-white"
        >
          Send
        </div>
        <div class="flex justify-center items-center px-3">
          <SharedIconHubEmailsDownArrow
            class="w-3 h-2 items-center"
            color="white"
          />
        </div>
      </button>
      <div class="flex space-x-3">
        <div
          class="hover:bg-[#F1F2F6] w-7 h-7 flex justify-center items-center rounded-md cursor-pointer"
          :class="showTextFormattedMenu ? 'bg-[#F1F2F6]' : ''"
          @click.stop="showTextFormattedMenu = !showTextFormattedMenu"
        >
          <SharedIconHubEmailsFormattingIcon />
        </div>
        <SourceHubEmailsTextFormatterMenu
          v-show="showTextFormattedMenu"
          class="absolute top-[-60px] left-0 !ml-0"
          :editor="editor"
          @click.stop=""
        />
        <div class="flex space-x-2">
          <input
            type="file"
            multiple
            @change="handleFileChange"
            class="hidden !ml-0"
            ref="fileInput"
          />
          <div
            class="hover:bg-[#F1F2F6] w-5 h-7 !ml-0 flex justify-center items-center rounded-md cursor-pointer"
            @click.stop="triggerFileInput"
          >
            <SharedIconHubEmailsAttach />
          </div>
          <button
            class="hover:bg-[#F1F2F6] w-7 h-7 flex justify-center items-center rounded-md cursor-pointer"
            @click.stop="showLinkPopup(), (showEmojiPicker = false)"
          >
            <SharedIconHubEmailsLinkIcon />
          </button>
          <div
            class="relative hover:bg-[#F1F2F6] w-7 h-7 flex justify-center items-center rounded-md cursor-pointer"
            :class="showEmojiPicker ? 'bg-[#F1F2F6]' : ''"
            @click.stop="toggleEmojiPicker"
          >
            <SharedIconHubEmailsEmojiIcon />
            <EmojiPicker
              v-if="showEmojiPicker"
              @click.stop=""
              :editor="editor"
              class="absolute top-[-344px] left-0"
            />
          </div>
          <div
            class="hover:bg-[#F1F2F6] w-7 h-7 flex justify-center items-center rounded-md cursor-pointer"
            @click="store.commit('emails/SET_DRIVE_INSERT_FILE_MODAL', true)"
          >
            <SharedIconHubEmailsDriveIcon />
          </div>
          <div
            class="hover:bg-[#F1F2F6] w-7 h-7 flex justify-center items-center rounded-md cursor-pointer"
            @click="store.commit('emails/SET_PHOTO_INSERT_FILE_MODAL', true)"
          >
            <SharedIconHubEmailsPhotoIcon />
          </div>
          <div
            class="hover:bg-[#F1F2F6] w-7 h-7 flex justify-center items-center rounded-md cursor-pointer"
            :class="showConfidentialModeModal ? 'bg-[#f1f2f6]' : ''"
            @click="store.commit('emails/SET_CONFIDENTIAL_MODE_MODAL', true)"
          >
            <SharedIconHubEmailsConfidentialIcon />
          </div>
          <div
            class="hover:bg-[#F1F2F6] w-7 h-7 flex justify-center items-center rounded-md cursor-pointer relative"
            :class="showSignatureMenu ? 'bg-[#F1F1F6]' : ''"
            @click.stop="
              (showSignatureMenu = !showSignatureMenu),
                (showMoreOptions = false),
                (showEmojiPicker = false)
            "
          >
            <SharedIconHubEmailsSignatureIcon />
            <LazySourceHubEmailsSignatureMenu
              @click.stop=""
              v-if="showSignatureMenu"
            />
          </div>
        </div>
        <div
          class="relative hover:bg-[#F1F2F6] w-5 h-7 flex justify-center items-center rounded-md cursor-pointer"
          :class="showMoreOptions ? 'bg-[#F1F2F6]' : ''"
          @click.stop="
            (showMoreOptions = !showMoreOptions),
              (showSignatureMenu = false),
              (showEmojiPicker = false)
          "
        >
          <SharedIconHubEmailsThreeDotMenuIcon color="#525252" />
          <LazySourceHubEmailsMoreOptionsMenu
            v-if="showMoreOptions"
            @click.stop=""
          />
        </div>
      </div>
    </div>
    <div
      class="hover:bg-[#F1F2F6] w-7 h-7 flex justify-center items-center rounded-md cursor-pointer"
      @click="
        setShowReplyBox(),
          deleteSpecificComposeItem(itemId),
          store.commit('emails/SET_SELECTED_COLOR', '')
      "
    >
      <SharedIconHubEmailsDeleteIcon />
    </div>
  </div>
</template>
