<script setup lang="ts">
import SharedIconHubEmailsCalenderIcon from '~/components/shared/icon/hub/emails/CalenderIcon.vue'

interface MoreMenuOptions {
  id: number
  label: string
  icon: string | Component
}

const moreMenuOptions = ref<MoreMenuOptions[]>([
  {
    id: 1,
    label: 'Default to full screen',
    icon: '',
  },
  {
    id: 2,
    label: 'Label',
    icon: '',
  },
  {
    id: 3,
    label: 'Plain text mode',
    icon: '',
  },
  {
    id: 4,
    label: 'Print',
    icon: '',
  },
  {
    id: 5,
    label: 'Check spelling',
    icon: '',
  },
  {
    id: 6,
    label: 'Set up a time to meet',
    icon: markRaw(SharedIconHubEmailsCalenderIcon),
  },
])
</script>

<template>
  <div
    class="menu-box absolute xl:left-0 right-0 top-[-233px] w-[240px] py-[1px]"
  >
    <div
      class="grid grid-cols-[16px_1fr] gap-x-2.5 items-center px-[18px] py-[7px] hover:bg-[#F1F2F6] cursor-pointer"
      v-for="moreMenuOption in moreMenuOptions"
      :key="moreMenuOption.id"
      :class="
        moreMenuOption.id === 1 ||
        moreMenuOption.id === 3 ||
        moreMenuOption.id === 5
          ? 'border-b border-[#C2C2C2] border-opacity-50'
          : ''
      "
    >
      <component v-if="moreMenuOption.icon" :is="moreMenuOption.icon" />
      <div v-else class="w-3.5"></div>
      <div class="flex justify-between items-center">
        <p class="#525252 whitespace-nowrap">
          {{ moreMenuOption.label }}
        </p>
        <SharedIconHubEmailsDownArrow
          v-if="moreMenuOption.id === 2 || moreMenuOption.id === 6"
          class="transform rotate-[-90deg] w-[8px] h-2"
        />
      </div>
    </div>
  </div>
</template>

<style scoped>
.menu-box {
  background: #ffffff 0% 0% no-repeat padding-box;
  box-shadow: 2px 2px 4px #22283114;
  border-radius: 8px;
}
</style>
