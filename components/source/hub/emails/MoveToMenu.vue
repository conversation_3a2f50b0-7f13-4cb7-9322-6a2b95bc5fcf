<script setup lang="ts">
import { useStore } from 'vuex'

const store = useStore()

interface MoveToMenuOptions {
  id: number
  title: string
}

const searchText = ref<string>('')
const moveToMenuOptions = ref<MoveToMenuOptions[]>([
  {
    id: 1,
    title: 'Social',
  },
  {
    id: 2,
    title: 'Updates',
  },
  {
    id: 3,
    title: 'Forums',
  },
  {
    id: 4,
    title: 'Promotions',
  },
  {
    id: 5,
    title: 'Spam',
  },
  {
    id: 6,
    title: 'Trash',
  },
])

const searchLabels = computed(() => {
  return moveToMenuOptions.value.filter((moveToMenuOption: MoveToMenuOptions) =>
    moveToMenuOption.title
      .toLowerCase()
      .startsWith(searchText.value.toLowerCase()),
  )
})
</script>

<template>
  <div
    class="select-extend-menu-box flex flex-col space-y-3.5 bg-white py-2 absolute top-10 left-0"
  >
    <div class="px-3.5">
      <p class="text-[#525252]">Move To:</p>
    </div>
    <div
      class="flex space-x-3 justify-between border-b border-[#4A71D4] px-3.5 pb-1"
    >
      <input
        class="flex-grow outline-none border-none"
        type="text"
        v-model="searchText"
      />
      <ClientOnly>
        <fa class="text-[#707070]" :icon="['fa', 'magnifying-glass']" />
      </ClientOnly>
    </div>
    <ul class="flex flex-col">
      <li
        v-for="moveToMenuOption in searchLabels"
        :key="moveToMenuOption.id"
        class="flex justify-between items-center !space-x-2 px-3.5 py-2 hover:bg-[#F1F2F6] cursor-pointer"
        :class="
          moveToMenuOption.id === 4 || moveToMenuOption.id === 6
            ? 'border-b border-[#C2C2C2]'
            : ''
        "
      >
        <div class="flex items-center !space-x-2">
          <p class="text-[#525252]">{{ moveToMenuOption.title }}</p>
        </div>
      </li>
    </ul>
    <ul class="flex flex-col space-y-3.5 px-3.5">
      <li>
        <p class="text-[#525252]">
          {{ searchText ? `"${searchText}"` : '' }} Create new
        </p>
      </li>
      <li><p class="text-[#525252]">Manage Labels</p></li>
    </ul>
  </div>
</template>

<style lang="scss" scoped>
.select-menu-box {
  min-width: 176px;
  max-width: 176px;
  height: fit-content;
  z-index: 1;
  background: #ffffff 0% 0% no-repeat padding-box;
  box-shadow: 2px 2px 4px #22283114;
  border-radius: 8px;
}
.select-extend-menu-box {
  min-width: 240px;
  max-width: 240px;
  height: fit-content;
  z-index: 1;
  background: #ffffff 0% 0% no-repeat padding-box;
  box-shadow: 2px 2px 4px #22283114;
  border-radius: 8px;
}
</style>
