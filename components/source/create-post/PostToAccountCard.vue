<script setup lang="ts">
import type { AccountOption } from '~/composables/useSavePostSettings'

defineProps({
  account: {
    type: Object as PropType<AccountOption>,
    required: true,
  },
})

const { accountByProvider } = useSavePostSettings()
</script>

<template>
  <li
    class="flex items-center space-x-2 text-sm font-semibold px-4 h-[34px] rounded-full border-[0.1px] border-transparent"
    :style="{ backgroundColor: accountByProvider(account).bgColor }"
  >
    <img
      :src="accountByProvider(account).icon"
      :alt="account.name"
      class="w-4 h-4"
    />
    <span>{{ account.name }}</span>
    <button
      class="rounded-full w-4 h-4 flex justify-center items-center ml-4"
      @click="$emit('remove-account')"
    >
      <ClientOnly>
        <fa class="font-bold text-[#525252] text-xl" :icon="['fas', 'times']" />
      </ClientOnly>
    </button>
  </li>
</template>

<style lang="scss" scoped></style>
