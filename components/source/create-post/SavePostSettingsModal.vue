<script setup lang="ts">
import { useVuelidate } from '@vuelidate/core'
import { maxLength, required } from '@vuelidate/validators'
import { useStore } from 'vuex'
import PostToAccountCard from '~/components/source/create-post/PostToAccountCard.vue'
import type { AccountOption } from '~/composables/useSavePostSettings'

const {
  tabs,
  targetWordCountOptions,
  targetLengthOptions,
  autoContentOptions,
  contentStyleOptions,
  accountOptions,
  currentTab,
  name,
  selectedAccounts,
  autoImage,
  teleprompter,
  autoHashtags,
  autoUsernames,
  autoShortenLinks,
  autoGenerateContent,
  wordCountRange,
  targetLengthRange,
  contentStyle,
  autoContent,
  prompt,
  setCurrentSettings,
  removeAccount,
  accountByProvider,
  handleToggleAutoImage,
  handleToggleTeleprompter,
  handleToggleAutoHashtags,
  handleToggleAutoUsernames,
  handleToggleAutoShortenLinks,
  handleToggleAutoGenerateContent,
} = useSavePostSettings()
const { errorMessages } = useInputValidations()

const store = useStore()

const showSavePostSettingsModal = computed<boolean>(
  () => store.state.createPost.showSavePostSettingsModal,
)
const savedPostSettings = computed<PostSettings[]>(
  () => store.state.createPost.savedPostSettings,
)
const singlePostSettings = computed<PostSettings | null>(
  () => store.state.createPost.singlePostSettings,
)
const runningPostSettings = computed<PostSettings | null>(
  () => store.state.createPost.runningPostSettings,
)
const currentSettings = computed<PostSettings>(() => {
  const settings: PostSettings = {
    id: singlePostSettings.value?.id || new Date().getTime(),
    postType: currentTab.value,
    name: name.value,
    postToAccounts: selectedAccounts.value,
    autoImage: autoImage.value,
    teleprompter: teleprompter.value,
    autoHashtags: autoHashtags.value,
    autoUsernames: autoUsernames.value,
    autoShortenLinks: autoShortenLinks.value,
    autoGenerateContent: autoGenerateContent.value,
    prompt: prompt.value,
    wordCountRange: wordCountRange.value?.range || null,
    targetLengthRange: targetLengthRange.value?.range || null,
    contentStyle: contentStyle.value?.label || null,
    autoContent: autoContent.value?.label || null,
  }
  return settings
})

const rules = {
  name: {
    required,
    maxLength: maxLength(100),
  },
}
const v$ = useVuelidate(rules, { name })

const handleSelectionChange = () => {
  console.log('Changed..')
}

const handleChangeSettings = (settings: PostSettings | null) => {
  store.commit('createPost/SET_SINGLE_POST_SETTINGS', settings)
}

const closeModal = () => {
  store.commit('createPost/SET_SHOW_SAVE_POST_SETTINGS_MODAL', false)
}

const deleteSettings = (id: any) => {
  store.dispatch('createPost/deletePostSettings', id)
  if (singlePostSettings.value?.id === id) {
    handleChangeSettings(null)
  }
}

const saveSettings = async () => {
  const isValid = await v$.value.$validate()
  if (!isValid) return

  if (singlePostSettings.value) {
    // update
    store.dispatch('createPost/updatePostSettings', currentSettings.value)
  } else {
    // add new
    store.dispatch('createPost/addNewPostSettings', currentSettings.value)
    name.value = ''
    v$.value.$reset()
  }
  store.dispatch('header/closeMobileHeader')
  closeModal()
}

watch(
  () => [singlePostSettings.value, runningPostSettings.value],
  ([newSingleSettings, newRunningSettings]) => {
    if (newSingleSettings) {
      setCurrentSettings(newSingleSettings)
    } else if (newRunningSettings) {
      setCurrentSettings(newRunningSettings)
    }
  },
  { deep: true, immediate: true },
)
</script>

<template>
  <section>
    <div
      v-if="showSavePostSettingsModal"
      @click="closeModal"
      class="size-full inset-0 absolute bg-[#323744]/75 z-[99]"
    ></div>
    <div
      class="z-[100] h-full fixed flex flex-col top-0 profile bg-[#222831] px-2 md:px-3 pb-4 md:pb-6 pt-6 md:pt-[32px] transition-all duration-700 ease-in-out md:rounded-l-2xl md:shadow-2xl"
      :class="[showSavePostSettingsModal ? 'show' : 'hide']"
    >
      <div
        class="w-full flex flex-row items-center justify-between relative text-[#EBEDF5] px-2 md:px-3"
      >
        <h2 class="md:text-xl md:font-bold text-xl">Save Post Settings</h2>
        <button
          class="w-8 h-8 flex items-center justify-end cursor-pointer"
          @click="closeModal()"
        >
          <ClientOnly>
            <fa
              class="xl:text-2xl md:text-xl md:font-bold text-2xl"
              :icon="['fas', 'times']"
            />
          </ClientOnly>
        </button>
      </div>

      <div
        class="flex flex-col flex-grow w-full h-[calc(100%-108px)] scroll overflow-y-auto pb-6 px-2 md:px-3"
      >
        <div
          class="w-full min-h-[212px] h-[212px] rounded-2xl overflow-hidden mt-6"
        >
          <ul
            class="w-full h-full scroll overflow-y-auto rounded-2xl bg-white p-6 space-y-2"
          >
            <li
              v-for="item in savedPostSettings"
              :key="item.id"
              @click="handleChangeSettings(item)"
              class="rounded-lg h-[35px] px-4 text-base flex items-center justify-between space-x-2 group cursor-pointer hover:bg-[#4A71D4] hover:text-white"
              :class="[
                singlePostSettings?.id === item.id
                  ? 'bg-[#4A71D4] text-white'
                  : 'bg-[#F1F2F6] text-[#333333]',
              ]"
            >
              <span class="truncate">{{ item.name }}</span>
              <SharedIconTrash
                @click.stop="deleteSettings(item.id)"
                class="text-white"
                :class="[
                  singlePostSettings?.id === item.id
                    ? 'block'
                    : 'hidden group-hover:block',
                ]"
              />
            </li>
          </ul>
        </div>
        <input
          type="text"
          v-model="name"
          placeholder="Name"
          class="w-full h-[35px] mt-6 px-6 py-2 outline-none border border-[#EBEDF5] rounded-full text-base bg-[#EBEDF5]"
          @blur="v$.name.$touch()"
        />
        <template v-if="v$.name.$error">
          <p
            class="text-red-500 text-xs mb-0 pl-2 pt-1"
            v-for="error in v$.name.$errors"
            :key="error.$uid"
          >
            {{ errorMessages(error, name) }}
          </p>
        </template>
        <div class="mt-4">
          <div class="flex items-center justify-between">
            <BaseTabs
              v-model="currentTab"
              :tabs="tabs"
              :is-route-enable="false"
              tabsClass="w-full max-w-[240px] h-[35px] bg-[#EBEDF5] shadow-md"
              circleClass="bg-[#4A71D4]"
            />
            <BaseMultiSelectDropsDown
              v-model="selectedAccounts"
              placeholder="Select Account"
              labelKey="name"
              :options="accountOptions"
              @change="handleSelectionChange"
            >
              <template #option="{ option }: { option: AccountOption }">
                <div class="flex items-center space-x-2">
                  <img
                    :src="accountByProvider(option).icon"
                    :alt="option.name"
                    class="size-4.1 max-h-4.1"
                  />
                  <span>{{ option.name }}</span>
                </div>
              </template>
            </BaseMultiSelectDropsDown>
          </div>
          <ul
            v-if="selectedAccounts.length > 0"
            class="mt-4 flex items-center gap-y-4 gap-x-2 flex-wrap"
          >
            <PostToAccountCard
              v-for="account in selectedAccounts"
              :key="account?.id"
              :account="account"
              @remove-account="removeAccount(account)"
            />
          </ul>
        </div>
        <div class="mt-4 space-y-[15px]">
          <div v-if="currentTab === 'Text'" class="setting-item">
            <span>Auto Image</span>
            <InputsToggleInput
              :id="10"
              :select="autoImage"
              uncheckedLabelBgColor="#EBEDF5"
              @toggle-select="handleToggleAutoImage"
            />
          </div>
          <div v-else class="setting-item">
            <span>Teleprompter</span>
            <InputsToggleInput
              :id="11"
              :select="teleprompter"
              uncheckedLabelBgColor="#EBEDF5"
              @toggle-select="handleToggleTeleprompter"
            />
          </div>
          <div class="setting-item">
            <span>Auto #Hashtags</span>
            <InputsToggleInput
              :id="12"
              :select="autoHashtags"
              uncheckedLabelBgColor="#EBEDF5"
              @toggle-select="handleToggleAutoHashtags"
            />
          </div>
          <div class="setting-item">
            <span>Auto @Usernames</span>
            <InputsToggleInput
              :id="13"
              :select="autoUsernames"
              uncheckedLabelBgColor="#EBEDF5"
              @toggle-select="handleToggleAutoUsernames"
            />
          </div>
          <div class="setting-item">
            <span>Auto Shorten Links</span>
            <InputsToggleInput
              :id="14"
              :select="autoShortenLinks"
              uncheckedLabelBgColor="#EBEDF5"
              @toggle-select="handleToggleAutoShortenLinks"
            />
          </div>
          <div class="setting-item">
            <span>Auto Generate Content</span>
            <InputsToggleInput
              :id="15"
              :select="autoGenerateContent"
              uncheckedLabelBgColor="#EBEDF5"
              @toggle-select="handleToggleAutoGenerateContent"
            />
          </div>
        </div>
        <template v-if="autoGenerateContent">
          <div class="flex items-center justify-between mt-3.5">
            <div v-if="currentTab === 'Text'" class="space-y-2">
              <h6 class="text-white">Target Word Count</h6>
              <BaseDropsDown
                :options="targetWordCountOptions"
                v-model="wordCountRange"
                labelKey="range"
                placeholder="Select Word Range"
                :dorpdownPlaceholder="true"
                :menuWidth="240"
                :menuHeight="35"
                menuBgColor="#4A71D4"
                menuTextColor="#fff"
                arrowColor="#fff"
                :dropdownWidth="240"
                :dropdownMaxHeight="290"
                dropsdownTextColor="#525252"
                scrollbarTrackColor="#a1cdff50"
                scrollbarThumbColor="#a1cdff"
              />
            </div>
            <div v-else class="space-y-2">
              <h6 class="text-white">Target Length</h6>
              <BaseDropsDown
                :options="targetLengthOptions"
                v-model="targetLengthRange"
                labelKey="range"
                placeholder="Select Target Length"
                :dorpdownPlaceholder="true"
                :menuWidth="240"
                :menuHeight="35"
                menuBgColor="#4A71D4"
                menuTextColor="#fff"
                arrowColor="#fff"
                :dropdownWidth="240"
                :dropdownMaxHeight="290"
                dropsdownTextColor="#525252"
                scrollbarTrackColor="#a1cdff50"
                scrollbarThumbColor="#a1cdff"
              />
            </div>
            <div class="space-y-2">
              <h6 class="text-white">Content Style</h6>
              <BaseDropsDown
                :options="contentStyleOptions"
                v-model="contentStyle"
                labelKey="label"
                placeholder="Select Content Style"
                :dorpdownPlaceholder="true"
                :menuWidth="240"
                :menuHeight="35"
                menuBgColor="#4A71D4"
                menuTextColor="#fff"
                arrowColor="#fff"
                :dropdownWidth="240"
                :dropdownMaxHeight="290"
                dropsdownTextColor="#525252"
                scrollbarTrackColor="#a1cdff50"
                scrollbarThumbColor="#a1cdff"
              />
            </div>
          </div>
          <div class="space-y-2 mt-3.5">
            <h6 class="text-white">Auto Content</h6>
            <BaseDropsDown
              :options="autoContentOptions"
              v-model="autoContent"
              labelKey="label"
              placeholder="Select Content Type"
              :dorpdownPlaceholder="true"
              :menuWidth="240"
              :menuHeight="35"
              menuBgColor="#4A71D4"
              menuTextColor="#fff"
              arrowColor="#fff"
              :dropdownWidth="240"
              :dropdownMaxHeight="290"
              dropsdownTextColor="#525252"
              scrollbarTrackColor="#a1cdff50"
              scrollbarThumbColor="#a1cdff"
            />
          </div>
          <textarea
            name="optional"
            id="optional"
            v-model="prompt"
            rows="2"
            placeholder="Describe what you want (Optional)"
            class="resize-none w-full mt-4 bg-[#E3EFFF] rounded-lg text-[#333333] text-base p-4 focus:outline-none placeholder:text-[#525252] min-h-[78px]"
          ></textarea>
        </template>
      </div>

      <div
        class="w-full flex justify-center md:space-x-4 space-x-3 md:px-10 px-4 pt-1"
      >
        <button
          class="w-26 h-[35px] bg-[#222831] text-[#C2C2C2] rounded-full border-[1.5px] border-[#C2C2C2] outline-none font-bold md:text-base text-sm"
          @click="closeModal"
        >
          <span>Cancel</span>
        </button>
        <button
          class="w-26 h-[35px] bg-[#4A71D4] text-white rounded-full outline-none font-bold md:text-base text-sm"
          @click="saveSettings"
        >
          <span>{{ singlePostSettings ? 'Save' : 'Add' }}</span>
        </button>
      </div>
    </div>
  </section>
</template>

<style lang="scss" scoped>
.profile {
  width: 550px;
  height: 100%;
  right: -550px;
}

.show {
  width: 550px;
  right: 0px;
}

.hide {
  width: 550px;
  right: -550px;
}

@media (max-width: 767px) {
  .profile {
    width: 100%;
    height: 85%;
    right: -100%;
    border-radius: 0 0 1.563rem 1.563rem;
  }
  .show {
    right: 0%;
  }
  .hide {
    right: -100%;
  }
}

.scroll {
  overflow-x: hidden !important;
  scrollbar-color: #a1cdff #ececec; /* Firefox 64 */
  /* Handle */
  &::-webkit-scrollbar-thumb {
    background: #a1cdff;
  }
  /* Handle on hover */
  &::-webkit-scrollbar-thumb:hover {
    background: #a1cdff;
  }
}

.setting-item {
  @apply flex items-center justify-between;
  span {
    @apply text-base text-white;
  }
}
</style>
