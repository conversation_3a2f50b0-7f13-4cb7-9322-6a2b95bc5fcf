<script setup lang="ts">
import { format } from 'date-fns'
import { useStore } from 'vuex'
import SavedSettingsMenu from '~/components/source/create-post/SavedSettingsMenu.vue'

const emit = defineEmits<{
  (e: 'toggle-skew', value: boolean): void
}>()

const store = useStore()

const showDatePicker = ref(false)
const selectedDate = ref<Date | null>(null)
const calendarContainerRef = ref<HTMLElement | null>(null)

const skew = ref<boolean>(false)
const showEditFeed = ref<boolean>(false)
const showSkew = ($event: boolean) => {
  emit('toggle-skew', $event)
  skew.value = $event
}

const handleClickOutsideOfCalendar = (event: Event) => {
  if (
    calendarContainerRef.value &&
    !calendarContainerRef.value.contains(event.target as Node) &&
    showDatePicker.value
  ) {
    showDatePicker.value = false
  }
}

const openPublishPostModal = () => {
  store.commit('createPost/SET_IS_OPEN_PUBLISH_POST_MODAL', true)
}

onMounted(() => {
  document.addEventListener('click', handleClickOutsideOfCalendar)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutsideOfCalendar)
})
</script>

<template>
  <div class="flex flex-row justify-between items-center px-2 space-x-4">
    <SavedSettingsMenu
      :active="false"
      :height="100"
      source="savedSettings"
      @expand="showSkew($event)"
      @show-edit-feed="showEditFeed = true"
    />

    <div class="flex items-center space-x-6 text-base">
      <div class="flex items-center space-x-4">
        <span v-if="selectedDate" class="text-white font-semibold">{{
          format(selectedDate, 'dd MMMM, yyyy')
        }}</span>
        <div ref="calendarContainerRef" class="relative">
          <button
            class="flex justify-center items-center size-10 rounded-full transition-all duration-300 ease-in-out"
            :class="[
              selectedDate
                ? 'bg-[#4A71D4] text-white'
                : 'bg-white text-[#4a71d4]',
            ]"
            @click="showDatePicker = !showDatePicker"
          >
            <SharedIconCalendar />
          </button>
          <div
            v-if="showDatePicker"
            class="absolute top-full right-0 mt-1 bg-white shadow-[2px_2px_4px_#22283114] p-2 rounded-lg z-10"
          >
            <VDatePicker v-model="selectedDate" borderless />
          </div>
        </div>
      </div>
      <button
        class="h-10 w-[150px] rounded-full flex justify-center items-center text-base bg-[#4A71D4] text-white font-semibold"
        @click="openPublishPostModal"
      >
        {{ selectedDate ? 'Schedule Post' : 'Post Now' }}
      </button>
      <button
        class="flex justify-center items-center size-10 bg-white rounded-full"
        @click="$router.back()"
      >
        <ClientOnly>
          <fa class="text-[#525252] text-2xl" :icon="['fas', 'times']" />
        </ClientOnly>
      </button>
    </div>
  </div>
</template>

<style scoped lang="scss">
:deep(.vc-monthly .is-not-in-month *) {
  opacity: 0.5;
}
</style>
