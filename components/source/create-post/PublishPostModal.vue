<script setup lang="ts">
import { useStore } from 'vuex'
import type { AccountOption } from '~/composables/useSavePostSettings'

const store = useStore()
const isPostDeleteModal = computed<boolean>(
  () => store.state.createPost.isOpenPublishPostModal,
)
const postToAccounts = computed<AccountOption[] | null>(
  () => store.state.createPost.runningPostSettings?.postToAccounts || [],
)
const readyToPostAccounts = ref<AccountOption[]>([])

const closeModal = () => {
  store.commit('createPost/SET_IS_OPEN_PUBLISH_POST_MODAL', false)
}

const isChecked = (account: AccountOption) => {
  return readyToPostAccounts.value.some((acc) => acc.id === account.id)
}

const toggleOption = (account: AccountOption) => {
  const index = readyToPostAccounts.value.findIndex(
    (acc) => acc.id === account.id,
  )
  if (index !== -1) {
    readyToPostAccounts.value.splice(index, 1)
  } else {
    readyToPostAccounts.value.push(account)
  }
}

const publishPost = () => {
  if (readyToPostAccounts.value.length === 0) {
    return
  }
  console.log('Publish Post', readyToPostAccounts.value)
  closeModal()
}

watch(isPostDeleteModal, (newValue) => {
  if (newValue && postToAccounts.value) {
    readyToPostAccounts.value = [...postToAccounts.value]
  }
})
</script>

<template>
  <BaseModal
    :isOpen="isPostDeleteModal"
    @closeModal="closeModal"
    exitButtonClass="size-5 text-[#505050] mr-6 mt-3.5"
    containerClass="w-full max-w-[640px] rounded-2xl bg-[#FFFFFF]"
  >
    <div class="w-full px-6 pt-3.5 pb-4 border-b-2 border-[#2e2b2b13]">
      <h3 class="text-[#505050] text-lg font-semibold">Review</h3>
    </div>
    <div
      v-if="postToAccounts && postToAccounts.length > 0"
      class="text-lg w-full"
    >
      <p class="text-[#333333] px-6 py-4">
        This will post to the following accounts.
      </p>
      <div class="px-6 pb-4 space-y-4 h-[352px] overflow-y-auto custom-scroll">
        <div
          v-for="account in postToAccounts"
          :key="account.id"
          class="flex items-center justify-between px-4 py-3 bg-[#EBEDF5] rounded-lg"
        >
          <div class="flex items-center space-x-2.5">
            <SourceAccountLogo
              :account-profile-pic="account.profilePic"
              :account-provider="account.provider"
              :class="[isChecked(account) ? '' : 'grayscale']"
            />
            <div class="flex flex-col space-y-0.5">
              <p
                class="font-semibold"
                :class="
                  isChecked(account) ? 'text-[#333333]' : 'text-[#707070]'
                "
              >
                {{ account.name }}
              </p>
              <p
                class="text-sm"
                :class="
                  isChecked(account) ? 'text-[#525252]' : 'text-[#C2C2C2]'
                "
              >
                {{ account.username }}
              </p>
            </div>
          </div>
          <InputsCheckBoxInput
            :id="account.id"
            :modelValue="isChecked(account)"
            checkColor="#4A71D4"
            borderColor="#4A71D4"
            @update:modelValue="() => toggleOption(account)"
          />
        </div>
      </div>
    </div>
    <div v-else class="px-6 pb-4 space-y-4 h-[348px]">
      <p class="text-[#333333] py-4">Please select account to publish</p>
    </div>
    <div
      class="pt-[18px] pb-4 px-6 w-full flex justify-end border-t-2 border-[#2e2b2b13] rounded-b-2xl"
    >
      <button
        @click="publishPost"
        class="h-[35px] px-6 rounded-full text-base bg-[#4A71D4] text-white font-semibold"
      >
        Submit
      </button>
    </div>
  </BaseModal>
</template>
