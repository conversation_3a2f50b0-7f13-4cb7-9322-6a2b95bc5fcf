<script setup lang="ts">
import { useStore } from 'vuex'
import WebcamRecorder from './WebcamRecorder.vue'

const emit = defineEmits<{
  videoDone: []
}>()

const store = useStore()

const scriptText = ref('')
const capturedVideo = ref<CapturedVideo | null>(null)

const recordDone = (video: CapturedVideo | null) => {
  capturedVideo.value = video
}

const handleVideoDone = () => {
  if (!capturedVideo.value) return
  store.commit('createPost/SET_CAPTURED_VIDEO', capturedVideo.value)
  store.commit('createPost/UPDATE_PREVIEW_CONTENT', {
    video: capturedVideo.value,
  })
  emit('videoDone')
}
</script>

<template>
  <div class="w-full flex flex-col">
    <div class="w-full h-[90px] rounded-lg overflow-hidden">
      <textarea
        name="scriptText"
        id="scriptText"
        v-model="scriptText"
        rows="3"
        placeholder="Type your script"
        class="resize-none w-full h-full bg-[#F1F2F6] rounded-lg text-[#525252] text-base p-4 focus:outline-none placeholder:text-[#707070] custom-scroll"
      ></textarea>
    </div>
    <button
      v-if="scriptText"
      class="ml-auto mt-4 h-[35px] px-6 rounded-full flex justify-center items-center text-base bg-[#4A71D4] text-white font-semibold"
    >
      Open Teleprompter
    </button>
    <ClientOnly>
      <WebcamRecorder @recordDone="recordDone" />
    </ClientOnly>
    <div v-if="capturedVideo" class="w-full flex justify-end pt-4">
      <button
        @click="handleVideoDone"
        class="h-[35px] px-8 rounded-full flex justify-center items-center text-base bg-[#4A71D4] text-white font-semibold"
      >
        Done
      </button>
    </div>
  </div>
</template>

<style lang="scss" scoped></style>
