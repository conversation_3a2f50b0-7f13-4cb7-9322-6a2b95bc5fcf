<template>
  <div
    class="relative w-full h-auto bg-[#333333] rounded-lg mt-4"
    :class="[!permissionGranted ? 'aspect-[880/634]' : 'aspect-auto']"
  >
    <!-- Permission Modal -->
    <Transition name="page">
      <PermissionModal
        v-if="showPermissionModal"
        @allow="requestPermission"
        @exit="rejectPermission"
      />
    </Transition>

    <!-- Header -->
    <div class="pt-4 pb-5 grid grid-cols-3">
      <span></span>
      <div class="flex justify-center items-center">
        <div
          v-if="isRecording || isPaused"
          class="px-4 text-white text-sm bg-[#E21F3F] rounded-lg leading-8 font-semibold"
        >
          <span>{{ formattedTime }}</span>
        </div>
      </div>
      <div class="flex items-center justify-end pr-[34px] space-x-1">
        <template v-if="!showPermissionModal">
          <button
            :disabled="controlsDisabled"
            class="rounded-full"
            :class="[
              isLandscape ? 'text-[#4A71D4]' : 'text-[#C2C2C2]',
              controlsDisabled ? 'opacity-50' : 'hover:bg-gray-100/10',
            ]"
            @click="setOrientation(true)"
          >
            <SharedIconTabletLandscape />
          </button>
          <div class="h-5 w-[1px] bg-[#707070] rounded-[4px]"></div>
          <button
            :disabled="controlsDisabled"
            class="rounded-full"
            :class="[
              !isLandscape ? 'text-[#4A71D4]' : 'text-[#C2C2C2]',
              controlsDisabled ? 'opacity-50' : 'hover:bg-gray-100/10',
            ]"
            @click="setOrientation(false)"
          >
            <SharedIconTabletLandscape class="rotate-90" />
          </button>
        </template>
      </div>
    </div>

    <!-- Video Display Area -->
    <div
      v-if="permissionGranted"
      class="relative w-full h-auto bg-black overflow-hidden"
      :class="[
        isLandscape
          ? 'aspect-[880/500]' // aspect ratio when landscape
          : 'aspect-[3/4] max-w-[50%] mx-auto', // aspect ratio when portrait
      ]"
    >
      <!-- Live webcam feed (hidden when playing recording) -->
      <video
        ref="videoElement"
        class="w-full h-full"
        :class="[isPlayingRecording ? 'hidden' : '']"
        autoplay
        playsinline
        muted
      ></video>

      <!-- Recorded video playback (shown only when playing) -->
      <video
        v-if="isPlayingRecording"
        ref="playbackElement"
        class="w-full h-full"
        controls
      ></video>

      <!-- Countdown timer to start recording  -->
      <Transition name="fade">
        <div
          v-if="isCountingDown"
          class="absolute inset-0 w-full h-full flex items-center justify-center"
        >
          <div
            class="size-[200px] m-xl:size-[240px] rounded-full bg-white/50 text-[#333333] text-[120px] m-xl:text-[160px] font-semibold flex justify-center items-center"
          >
            <span>{{ countdownValue }}</span>
          </div>
        </div>
      </Transition>

      <!-- Recording Indicator -->
      <div
        v-if="isRecording && !isPaused"
        class="absolute top-4 left-4 flex items-center"
      >
        <span class="h-3 w-3 bg-red-500 rounded-full animate-pulse mr-2"></span>
        <span class="text-white bg-black bg-opacity-70 px-2 py-1 rounded-full"
          >REC</span
        >
      </div>

      <!-- Hidden canvas for thumbnail generation -->
      <canvas ref="thumbnailCanvas" class="hidden"></canvas>
    </div>

    <!-- Controls -->
    <div
      v-if="permissionGranted"
      class="w-full flex items-center justify-center pb-4 pt-4.1 space-x-2"
    >
      <button
        v-if="isRecording || isPaused"
        @click="togglePause"
        :title="isPaused ? 'Continue Recording' : 'Pause Recording'"
        class="size-[34px] rounded-full bg-white flex justify-center items-center hover:bg-gray-100"
      >
        <SharedIconPlay v-if="isPaused" class="h-4 text-[#525252]" />
        <SharedIconPause v-else class="w-4 text-[#525252]" />
      </button>
      <button
        v-if="!isRecording && !isPaused && !isPlayingRecording"
        :disabled="isCountingDown"
        title="Start Recording"
        class="size-[34px] rounded-full bg-white flex justify-center items-center"
        :class="[isCountingDown ? 'opacity-50' : 'hover:bg-gray-100']"
        @click="startCountdown"
      >
        <div class="size-4 rounded-full bg-[#E21F3F]"></div>
      </button>
      <button
        v-if="isRecording || isPaused"
        @click="stopRecording"
        title="Stop Recording"
        class="size-[34px] rounded-full bg-white flex justify-center items-center hover:bg-gray-100"
      >
        <div class="size-4 rounded-sm bg-[#E21F3F]"></div>
      </button>
      <button
        v-if="isPlayingRecording"
        @click="resetToWebcam"
        title="Record Again"
        class="size-[34px] rounded-full bg-white flex justify-center items-center hover:bg-gray-100"
      >
        <fa :icon="['fas', 'repeat']" class="text-[#525252] w-4" />
      </button>
    </div>

    <!-- Status Message -->
    <p v-if="statusMessage" class="text-center mt-4 text-gray-600 hidden">
      {{ statusMessage }}
    </p>
    <div v-else-if="permissionDenied" class="text-center mt-8">
      <p class="text-red-500 mb-4">
        Permission to access camera and microphone was denied.
      </p>
      <button
        @click="showPermissionModal = true"
        class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
      >
        Try Again
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import RecordRTC from 'recordrtc'
import PermissionModal from './PermissionModal.vue'

const emit = defineEmits<{
  recordDone: [video: CapturedVideo | null]
}>()

const videoElement = ref<HTMLVideoElement | null>(null)
const playbackElement = ref<HTMLVideoElement | null>(null)
const thumbnailCanvas = ref<HTMLCanvasElement | null>(null)

const showPermissionModal = ref(true)
const permissionGranted = ref(false)
const permissionDenied = ref(false)
const isRecording = ref(false)
const isPaused = ref(false)
const isPlayingRecording = ref(false)
const recordingTime = ref(0)
const statusMessage = ref('')
const isLandscape = ref(true)

// Countdown timer related state
const isCountingDown = ref(false)
const countdownValue = ref(3)

// Computed properties
const controlsDisabled = computed(
  () => isRecording.value || isPaused.value || isCountingDown.value,
)

const formattedTime = computed(() => formatTime(recordingTime.value))

// Cleanup references
let stream: MediaStream | null = null
let recorder: RecordRTC | null = null
let timerInterval: number | null = null
let countdownInterval: number | null = null
let recordedBlob: Blob | null = null
let recordedBlobUrl: string | null = null
let thumbnailUrl: string | null = null

const recordOptions: RecordRTC.Options = {
  type: 'video',
  mimeType: 'video/webm',
  disableLogs: true,
  recorderType: RecordRTC.MediaStreamRecorder,
  numberOfAudioChannels: 2,
  frameRate: 30,
  videoBitsPerSecond: 2500000,
  timeSlice: 1000,
}

const getVideoConstraints = (): MediaTrackConstraints => ({
  width: { ideal: isLandscape.value ? 1280 : 540 },
  height: { ideal: isLandscape.value ? 720 : 720 },
  aspectRatio: { ideal: isLandscape.value ? 16 / 9 : 3 / 4 },
  facingMode: 'user',
})

const stopMediaTracks = () => {
  if (stream) {
    stream.getTracks().forEach((track) => track.stop())
    stream = null
  }
}

const clearCountdownInterval = () => {
  if (countdownInterval) {
    clearInterval(countdownInterval)
    countdownInterval = null
  }
}

const clearTimerInterval = () => {
  if (timerInterval) {
    clearInterval(timerInterval)
    timerInterval = null
  }
}

const setVideoSource = async () => {
  await nextTick()
  if (!videoElement.value || !stream) return

  // Clear previous srcObject if exists
  if (videoElement.value.srcObject instanceof MediaStream) {
    const oldStream = videoElement.value.srcObject as MediaStream
    if (oldStream !== stream) {
      oldStream.getTracks().forEach((track) => track.stop())
    }
  }

  videoElement.value.srcObject = stream
  try {
    await videoElement.value.play()
  } catch (err) {
    console.error('Error playing video:', err)
  }
}

const updateMediaStream = async () => {
  if (!permissionGranted.value) return
  stopMediaTracks()

  try {
    stream = await navigator.mediaDevices.getUserMedia({
      video: getVideoConstraints(),
      audio: true,
    })
    await setVideoSource()
  } catch (err) {
    console.error('Error updating media stream:', err)
    statusMessage.value = 'Failed to update camera settings.'
  }
}

const formatTime = (seconds: number): string => {
  const hrs = Math.floor(seconds / 3600)
  const mins = Math.floor((seconds % 3600) / 60)
  const secs = seconds % 60

  return `${String(hrs).padStart(2, '0')}:${String(mins).padStart(2, '0')}:${String(secs).padStart(2, '0')}`
}

const generateThumbnail = (videoBlob: Blob): Promise<string> =>
  new Promise((resolve, reject) => {
    const tempVideo = document.createElement('video')
    const blobUrl = URL.createObjectURL(videoBlob)

    tempVideo.onloadedmetadata = () => {
      tempVideo.currentTime = Math.min(1, tempVideo.duration / 2)
    }

    tempVideo.onseeked = () => {
      if (!thumbnailCanvas.value) {
        URL.revokeObjectURL(blobUrl)
        return reject('Canvas not available')
      }

      const canvas = thumbnailCanvas.value
      canvas.width = tempVideo.videoWidth
      canvas.height = tempVideo.videoHeight

      const ctx = canvas.getContext('2d')
      if (!ctx) {
        URL.revokeObjectURL(blobUrl)
        return reject('No canvas context')
      }

      ctx.drawImage(tempVideo, 0, 0, canvas.width, canvas.height)
      const thumbnailData = canvas.toDataURL('image/jpeg', 0.8)

      URL.revokeObjectURL(blobUrl)
      resolve(thumbnailData)
    }

    tempVideo.onerror = () => {
      URL.revokeObjectURL(blobUrl)
      reject('Error loading video')
    }

    tempVideo.src = blobUrl
  })

const setOrientation = async (landscape: boolean) => {
  if (isLandscape.value !== landscape) {
    isLandscape.value = landscape
    if (
      permissionGranted.value &&
      !isRecording.value &&
      !isPlayingRecording.value
    ) {
      await updateMediaStream()
    }
  }
}

const requestPermission = async () => {
  try {
    stream = await navigator.mediaDevices.getUserMedia({
      video: getVideoConstraints(),
      audio: true,
    })
    permissionGranted.value = true
    showPermissionModal.value = false
    await setVideoSource()
    statusMessage.value = 'Camera ready. Click "Start Recording" to begin.'
  } catch (err) {
    console.error('Permission error:', err)
    permissionDenied.value = true
    showPermissionModal.value = false
    statusMessage.value = 'Failed to access camera or microphone.'
  }
}

const rejectPermission = () => {
  permissionDenied.value = true
  showPermissionModal.value = false
}

const ensureWebcamVisible = async (): Promise<boolean> => {
  if (!stream || !videoElement.value) return false
  if (!videoElement.value.srcObject) {
    await setVideoSource()
  }
  return true
}

const startCountdown = async () => {
  const webcamReady = await ensureWebcamVisible()
  if (!webcamReady) {
    statusMessage.value = 'Could not access webcam.'
    return
  }

  isCountingDown.value = true
  countdownValue.value = 3

  clearCountdownInterval()

  countdownInterval = window.setInterval(() => {
    countdownValue.value--

    if (countdownValue.value <= 0) {
      clearCountdownInterval()
      isCountingDown.value = false
      startRecording()
    }
  }, 1000)
}

const startRecording = async () => {
  if (!stream) return
  const webcamReady = await ensureWebcamVisible()
  if (!webcamReady) {
    statusMessage.value = 'Could not access webcam.'
    return
  }

  try {
    recorder = new RecordRTC(stream, recordOptions)
    recorder.startRecording()
    isRecording.value = true
    isPaused.value = false
    recordingTime.value = 0

    clearTimerInterval()
    timerInterval = window.setInterval(() => {
      if (!isPaused.value) recordingTime.value++
    }, 1000)

    statusMessage.value = 'Recording...'
  } catch (err) {
    console.error('Recording error:', err)
    statusMessage.value = `Error starting recording: ${err instanceof Error ? err.message : String(err)}`
  }
}

const stopRecording = () => {
  if (!recorder) return
  clearTimerInterval()

  recorder.stopRecording(async () => {
    try {
      recordedBlob = recorder!.getBlob()

      // Clean up previous URLs
      if (recordedBlobUrl) {
        URL.revokeObjectURL(recordedBlobUrl)
        recordedBlobUrl = null
      }

      recordedBlobUrl = URL.createObjectURL(recordedBlob)

      try {
        thumbnailUrl = await generateThumbnail(recordedBlob)
      } catch (err) {
        console.error('Thumbnail generation error:', err)
        thumbnailUrl = '/images/landing/about.png'
      }

      emit('recordDone', { videoUrl: recordedBlobUrl, thumbnailUrl })
      isRecording.value = false
      isPaused.value = false
      isPlayingRecording.value = true
      await nextTick()

      if (playbackElement.value) {
        playbackElement.value.src = recordedBlobUrl
        playbackElement.value.muted = false
      }

      statusMessage.value = `Recording saved (${(recordedBlob.size / 1024 / 1024).toFixed(2)} MB)`
    } catch (err) {
      console.error('Error in stopRecording callback:', err)
      statusMessage.value = 'Error finalizing recording'
    } finally {
      if (recorder) {
        recorder.destroy()
        recorder = null
      }
    }
  })
}

const togglePause = () => {
  if (!recorder) return

  if (isPaused.value) {
    recorder.resumeRecording()
    statusMessage.value = 'Recording resumed...'
  } else {
    recorder.pauseRecording()
    statusMessage.value = 'Recording paused. Click to resume.'
  }

  isPaused.value = !isPaused.value
}

const resetToWebcam = async () => {
  // Clean up URLs
  if (recordedBlobUrl) {
    URL.revokeObjectURL(recordedBlobUrl)
    recordedBlobUrl = null
  }

  recordedBlob = null
  thumbnailUrl = null
  isPlayingRecording.value = false

  emit('recordDone', null)

  // Reset playback element
  if (playbackElement.value) {
    playbackElement.value.src = ''
    playbackElement.value.load()
  }

  await updateMediaStream()
  statusMessage.value = 'Camera ready. Click "Start Recording" to begin.'
}

onUnmounted(() => {
  clearTimerInterval()
  clearCountdownInterval()

  if (recorder) {
    recorder.destroy()
    recorder = null
  }

  stopMediaTracks()

  if (recordedBlobUrl) {
    recordedBlobUrl = null
  }

  // Clear video elements
  if (videoElement.value) {
    const oldStream = videoElement.value.srcObject as MediaStream
    if (oldStream instanceof MediaStream) {
      oldStream.getTracks().forEach((track) => track.stop())
    }
    videoElement.value.srcObject = null
    videoElement.value.src = ''
    videoElement.value.load()
  }

  if (playbackElement.value) {
    playbackElement.value.src = ''
    playbackElement.value.load()
  }
})
</script>

<style scoped>
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s;
}
.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
