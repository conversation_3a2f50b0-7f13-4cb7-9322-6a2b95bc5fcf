<script setup lang="ts">
import { useStore } from 'vuex'
import CaptureVideo from './CaptureVideo.vue'
import Hashtags from './Hashtags.vue'
import PostTextInput from './PostTextInput.vue'
import PostToAccount from './PostToAccount.vue'
import Tags from './Tags.vue'

const store = useStore()

const showCaptureVideoComp = ref(true)
const capturedVideo = computed<CapturedVideo | null>(
  () => store.state.createPost.capturedVideo,
)

const handleCaptureVideoDone = () => {
  showCaptureVideoComp.value = false
}
const revokeVideoAndThumbnailURL = () => {
  if (capturedVideo.value) {
    URL.revokeObjectURL(capturedVideo.value?.thumbnailUrl)
    URL.revokeObjectURL(capturedVideo.value?.videoUrl)
  }
}
const removeCapturedVideo = () => {
  revokeVideoAndThumbnailURL()
  store.commit('createPost/SET_CAPTURED_VIDEO', null)
  store.commit('createPost/UPDATE_PREVIEW_CONTENT', {
    video: null,
  })
  showCaptureVideoComp.value = true
}

onUnmounted(() => {
  revokeVideoAndThumbnailURL()
  store.commit('createPost/SET_CAPTURED_VIDEO', null)
  store.commit('createPost/SET_GENERATED_CONTENT', null)
  store.commit('createPost/SET_PREVIEW_CONTENT', null)
})
</script>

<template>
  <div class="size-full flex flex-col p-4 overflow-y-auto custom-scroll">
    <CaptureVideo
      v-if="showCaptureVideoComp"
      @videoDone="handleCaptureVideoDone"
    />
    <template v-else>
      <h3 class="font-semibold">Post to</h3>
      <PostToAccount />
      <div v-if="capturedVideo?.thumbnailUrl" class="mt-4 w-full">
        <div class="w-full h-auto max-w-[212px] relative group">
          <img
            :src="capturedVideo.thumbnailUrl"
            alt="Video"
            class="w-full h-auto rounded-lg aspect-[212/120] object-cover bg-gray-300"
          />
          <div
            class="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 space-x-2 hidden group-hover:flex"
          >
            <button
              class="size-[35px] rounded-full bg-white flex justify-center items-center"
            >
              <SharedIconPencil class="text-[#525252] size-[18px]" />
            </button>
            <button
              @click="removeCapturedVideo"
              class="size-[35px] rounded-full bg-[#E21F3F] flex justify-center items-center"
            >
              <ClientOnly>
                <fa class="text-2xl text-white" :icon="['fas', 'times']" />
              </ClientOnly>
            </button>
          </div>
        </div>
      </div>
      <PostTextInput placeholder="Caption video" />
      <Hashtags class="mt-4" />
      <Tags />
    </template>
  </div>
</template>

<style lang="scss" scoped></style>
