<script setup lang="ts">
import { useStore } from 'vuex'
import PostToAccountCard from '~/components/source/create-post/PostToAccountCard.vue'
import type { AccountOption } from '~/composables/useSavePostSettings'

const {
  accountOptions,
  selectedAccounts,
  accountByProvider,
  removeAccount,
  updateRunningSettings,
} = useSavePostSettings()

const store = useStore()

const postToAccounts = computed<AccountOption[]>(
  () => store.state.createPost.runningPostSettings?.postToAccounts || [],
)

const handleSelectionChange = (account: AccountOption) => {
  updateRunningSettings({ postToAccounts: selectedAccounts.value })

  const groupedAccount = selectedAccounts.value.filter(
    (item) => item.provider === account.provider,
  )
  if (groupedAccount.length === 0 && selectedAccounts.value.length > 0) {
    store.commit(
      'createPost/SET_SOCIAL_PREVIEW_COMP',
      selectedAccounts.value[0].provider,
    )
  }
}
const handleRemoveAndUpdate = (account: AccountOption) => {
  removeAccount(account)
  handleSelectionChange(account)
}

onMounted(() => {
  selectedAccounts.value = postToAccounts.value
})
</script>

<template>
  <div class="py-4 border-b border-[#2E2B2B]/20">
    <BaseMultiSelectDropsDown
      v-model="selectedAccounts"
      placeholder="Select Account"
      labelKey="name"
      :options="accountOptions"
      @change="handleSelectionChange"
    >
      <template #option="{ option }: { option: AccountOption }">
        <div class="flex items-center space-x-2">
          <img
            :src="accountByProvider(option).icon"
            :alt="option.name"
            class="size-4.1 max-h-4.1"
          />
          <span>{{ option.name }}</span>
        </div>
      </template>
    </BaseMultiSelectDropsDown>
    <ul
      v-if="selectedAccounts.length > 0"
      class="mt-6 flex items-center gap-2 flex-wrap"
    >
      <PostToAccountCard
        v-for="account in selectedAccounts"
        :key="account?.id"
        :account="account"
        @remove-account="handleRemoveAndUpdate(account)"
      />
    </ul>
  </div>
</template>

<style lang="scss" scoped></style>
