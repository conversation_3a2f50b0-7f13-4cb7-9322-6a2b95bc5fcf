<script setup lang="ts">
import { useStore } from 'vuex'

const emit = defineEmits<{
  allow: []
  exit: []
}>()

const store = useStore()

const handleAllow = () => {
  emit('allow')
}
const handleExit = () => {
  store.commit('createPost/SET_CURRENT_TAB', 'Text')
  emit('exit')
}
</script>

<template>
  <div class="absolute inset-0 flex justify-center items-center px-10">
    <div
      class="flex items-center flex-col max-w-[640px] w-full rounded-2xl bg-[#FFFFFF] p-4 pt-3.5"
    >
      <div
        class="w-full flex items-center justify-between text-[#505050] text-lg font-semibold"
      >
        <p>Allow App to use your Camera and Microphone</p>
        <ClientOnly>
          <fa
            class="text-xl font-normal cursor-pointer"
            :icon="['fas', 'times']"
            @click="handleExit"
          />
        </ClientOnly>
      </div>

      <div class="w-full pt-8">
        <p class="text-black text-lg text-start">
          App needs access of your camera and microphone to record your video.
        </p>
      </div>

      <div class="w-full flex justify-end pt-[30px]">
        <button
          @click="handleAllow"
          class="h-[35px] px-8 rounded-full flex justify-center items-center text-base bg-[#4A71D4] text-white font-semibold"
        >
          Allow
        </button>
      </div>
    </div>
  </div>
</template>
