<script setup lang="ts">
interface Props {
  placeholder: string
}

withDefaults(defineProps<Props>(), {
  placeholder: 'Type your post',
})
const { text } = useGeneratePost()
</script>

<template>
  <div class="w-full relative rounded-lg min-h-20 h-20 mt-4 overflow-hidden">
    <textarea
      name="post"
      id="post"
      v-model="text"
      rows="3"
      :placeholder="placeholder"
      class="resize-none w-full h-full bg-[#F1F2F6] rounded-lg text-[#525252] text-base px-4 py-3.5 focus:outline-none placeholder:text-[#707070] custom-scroll"
    ></textarea>
    <SharedIconEmojiSmileFace class="absolute right-4 bottom-4 size-5" />
  </div>
</template>

<style lang="scss" scoped></style>
