<script setup lang="ts">
interface DropsDownProps {
  placeholder?: string
  options: Record<string, any>[]
  labelKey?: string
  idKey?: string
  menuWidth?: number
  menuHeight?: number
  menuBgColor?: string
  menuTextColor?: string
  dropsdownTextColor?: string
  arrowColor?: string
  dropdownWidth?: number
  dropdownBgColor?: string
  dropdownMaxHeight?: number
  scrollbarTrackColor?: string
  scrollbarThumbColor?: string
  dorpdownPlaceholder?: boolean
}

type OptionType = Record<string, any> | null

const props = withDefaults(defineProps<DropsDownProps>(), {
  placeholder: 'Select an option',
  options: () => [],
  labelKey: 'label',
  idKey: 'id',
  menuWidth: 200,
  menuHeight: 40,
  menuBgColor: '#fff',
  menuTextColor: '#525252',
  dropsdownTextColor: '#525252',
  arrowColor: '#4A71D4',
  dropdownWidth: 250,
  dropdownBgColor: '#fff',
  dropdownMaxHeight: 200,
  scrollbarTrackColor: '#a1cdff50',
  scrollbarThumbColor: '#a1cdff',
  dorpdownPlaceholder: false,
})

const isOpen = ref(false)
const model = defineModel<OptionType>({ default: null })
const dropdownRef = ref<HTMLDivElement | null>(null)
const emit = defineEmits(['change'])

const selectedOption = computed(() => model.value)

const toggleDropdown = () => {
  isOpen.value = !isOpen.value
}

const selectOption = (option: OptionType) => {
  model.value = option
  isOpen.value = false
  emit('change', option)
}

const handleClickOutside = (event: Event) => {
  if (dropdownRef.value && !dropdownRef.value.contains(event.target as Node)) {
    isOpen.value = false
  }
}

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})
onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>

<template>
  <div ref="dropdownRef" class="relative">
    <!-- Dropdown button -->
    <div
      class="flex items-center justify-between px-4 py-2 cursor-pointer rounded-full transition-all font-semibold"
      :style="{
        color: menuTextColor,
        width: `${menuWidth}px`,
        height: `${menuHeight}px`,
        backgroundColor: menuBgColor,
      }"
      @click="toggleDropdown"
    >
      <span>{{ selectedOption ? selectedOption[labelKey] : placeholder }}</span>
      <ClientOnly>
        <fa
          class="text-xl"
          :style="{ color: arrowColor }"
          :icon="['fas', isOpen ? 'caret-up' : 'caret-down']"
        />
      </ClientOnly>
    </div>

    <!-- Dropdown menu -->
    <div v-if="isOpen" class="absolute mt-1 rounded-lg z-10 h-auto pb-3">
      <div
        class="py-2 rounded-lg shadow-xl overflow-y-auto dropdown-scroll"
        :style="{
          color: dropsdownTextColor,
          width: `${dropdownWidth}px`,
          backgroundColor: dropdownBgColor,
          maxHeight: `${dropdownMaxHeight}px`,
          '--scrollbar-track-color': props.scrollbarTrackColor,
          '--scrollbar-thumb-color': props.scrollbarThumbColor,
        }"
      >
        <div
          v-if="dorpdownPlaceholder"
          class="px-4 py-2 cursor-pointer hover:bg-[#F1F2F6] transition-all duration-200 opacity-80"
          @click="selectOption(null)"
        >
          {{ placeholder }}
        </div>
        <div
          v-for="option in options"
          :key="option[props.idKey] || JSON.stringify(option)"
          class="px-4 py-2 cursor-pointer hover:bg-[#F1F2F6] transition-all duration-200"
          @click="selectOption(option)"
        >
          {{ option[props.labelKey] || '[Missing Label]' }}
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.dropdown-scroll {
  overflow-y: auto;
  overflow-x: auto;
  -ms-overflow-style: none; /* IE 11 */
  scrollbar-width: thin;
  scrollbar-color: var(--scrollbar-thumb-color) var(--scrollbar-track-color);

  &::-webkit-scrollbar {
    width: 4px !important;
    height: 4px !important;
  }

  /* Track */
  &::-webkit-scrollbar-track {
    border-radius: 3px;
    background: var(--scrollbar-track-color);
  }

  /* Handle */
  &::-webkit-scrollbar-thumb {
    border-radius: 3px;
    background: var(--scrollbar-thumb-color);
  }
}
@media (max-width: 767px) {
  .dropdown-scroll {
    &::-webkit-scrollbar {
      width: 3px;
      height: 3px;
    }
  }
}
</style>
