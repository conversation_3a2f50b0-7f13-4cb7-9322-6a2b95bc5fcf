<script setup>
const props = defineProps({
  modelValue: <PERSON><PERSON><PERSON>,
  showMinus: <PERSON><PERSON><PERSON>,
  checkColor: {
    type: String,
    default: '#007aff',
  },
  borderColor: {
    type: String,
    default: '#707070',
  },
})

// Emit event to update the value
const emit = defineEmits(['update:modelValue'])

const toggleCheckbox = () => {
  emit('update:modelValue', !props.modelValue)
}
</script>

<template>
  <label class="custom-checkbox">
    <input type="checkbox" :checked="modelValue" @change="toggleCheckbox" />
    <span class="checkmark" :class="showMinus ? 'check-minus' : ''">
      <ClientOnly v-if="!showMinus">
        <fa
          class="w-2.5 h-2.5 text-white absolute left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2 font-normal cursor-pointer opacity-1"
          :icon="['fas', 'check']"
        />
      </ClientOnly>
      <ClientOnly v-else>
        <fa
          class="w-2.5 h-2.5 text-white absolute left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2 font-normal cursor-pointer opacity-1"
          :icon="['fas', 'minus']"
        />
      </ClientOnly>
    </span>
  </label>
</template>

<style scoped>
/* Hide default checkbox */
input[type='checkbox'] {
  display: none;
}

/* Custom checkbox */
.custom-checkbox {
  display: inline-block;
  position: relative;
  width: 16px;
  height: 16px;
  cursor: pointer;
}

/* Checkbox background */
.checkmark {
  width: 16px;
  height: 16px;
  background-color: white;
  border: 2px solid v-bind(borderColor);
  border-radius: 2px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

/* When checked */
input:checked + .checkmark {
  background-color: v-bind(checkColor);
  border-color: transparent;
}

.check-minus {
  background-color: v-bind(checkColor);
  border-color: transparent;
}
/* Checkmark symbol */
/* .checkmark::after {
  content: "✓";
  font-size: 12px;
  color: white;
  font-weight: bold;
  display: none;
} */

input:checked + .checkmark::after {
  display: block;
}
</style>
