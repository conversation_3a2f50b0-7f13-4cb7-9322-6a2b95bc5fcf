<script setup lang="ts">
const emit = defineEmits<{
  (e: 'toggle-select', id: number): void
}>()

const props = defineProps({
  bgColor: {
    type: String,
    default: '#393e46',
  },
  checkedBgColor: {
    type: String,
    default: '#ffffff',
  },
  labelBgColor: {
    type: String,
    default: '#4A71D4',
  },
  uncheckedLabelBgColor: {
    type: String,
    default: '#ffffff',
  },
  id: {
    type: Number,
    default: 0,
  },
  select: {
    type: Boolean,
    default: false,
  },
})
</script>

<template>
  <div
    class="relative inline-block w-9 align-middle select-none transition duration-200 ease-in"
    :style="{
      '--bg-color': bgColor,
      '--checked-bg-color': checkedBgColor,
      '--label-bg-color': labelBgColor,
      '--unchecked-label-bg-color': uncheckedLabelBgColor,
    }"
  >
    <input
      :id="id"
      :checked="select ? true : false"
      type="checkbox"
      name="toggle"
      class="toggle-checkbox absolute block rounded-full bg-white appearance-none cursor-pointer"
      @click="emit('toggle-select', id)"
    />
    <label
      :for="id"
      class="toggle-label block overflow-hidden h-5 transition-all duration-800 ease-in-out rounded-full cursor-pointer"
    ></label>
  </div>
</template>

<style lang="scss" scoped>
.toggle-checkbox {
  width: 16px;
  height: 16px;
  border: 0px;
  top: 2px;
  left: 2px;
  transition: all 0.5s ease-in-out;
  background-color: var(--bg-color);
  + .toggle-label {
    background-color: var(--unchecked-label-bg-color);
  }
  &:checked {
    @apply right-0;
    left: 18px;
    transition: all 0.5s ease-in-out;
    background-color: var(--checked-bg-color);
  }
  &:checked + .toggle-label {
    // @apply bg-purple-midlight;
    transition: all 0.5s ease-in-out;
    background-color: var(--label-bg-color);
  }
}
</style>
