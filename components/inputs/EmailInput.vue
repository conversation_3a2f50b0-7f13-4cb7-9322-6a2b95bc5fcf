<template>
  <div class="input" :style="{
    '--color': color,
    '--background': background,
    '--labelColor': labelColor,
  }">
    <label class="mb-1.5" :class="[labelClass]" :for="id">{{ label }}</label>
    <input :id="id" :class="[placeHolderClass, textInput]" type="email" :value="modelValue" :placeHolder="placeHolder"
      v-bind="$attrs" @change="emailCheck($event.target.value)"
      @input="$emit('update:modelValue', $event.target.value)" />
    <div v-if="error && emailExist" class="error text-sm text-red-900">
      <span v-for="error in errorMessage" :key="error.$uid">
        {{ errorMessages(error, modelValue) }}
      </span>
    </div>
    <div v-if="!emailExist" class="error text-sm text-red-900">
      This email is already taken
    </div>
  </div>
</template>
<script setup>
import { useInputValidations } from '~/composables/useValidations'

defineProps({
  id: {
    type: String,
    required: false,
    default: null,
  },
  label: {
    type: String,
    required: false,
    default: null,
  },
  labelColor: {
    type: String,
    required: false,
    default: '#FFFFFF',
  },
  labelClass: {
    type: String,
    required: false,
    default: '',
  },
  placeHolderClass: {
    type: String,
    required: false,
    default: 'placeholder-orange-600 placeholder-opacity-50',
  },
  value: {
    type: String,
    required: false,
    default: null,
  },
  placeHolder: {
    type: String,
    required: false,
    default: 'Type something',
  },
  errorMessage: {
    type: Array,
    required: false,
    default: () => [],
  },
  error: {
    type: Boolean,
    required: false,
    default: false,
  },
  color: {
    type: String,
    required: false,
    default: '#4D4D4D',
  },
  background: {
    type: String,
    required: false,
    default: '#fff',
  },
  emailExist: {
    type: Boolean,
    required: true,
    default: true,
  },
  textInput: {
    type: String,
    required: false,
    default: '',
  },
  modelValue: {
    type: String,
    default: '',
  },
})

// methods
const { errorMessages } = useInputValidations()

const emit = defineEmits(['onchangeEmail']);

const emailCheck = (value) => {
  emit('onchangeEmail', value);
};

</script>
<style lang="scss" scoped>
.input {
  $color: var(--color);
  $background-color: var(--background);

  label {
    color: var(--labelColor);
    @apply block;
  }

  input {
    color: $color;
    background-color: $background-color;
    box-shadow: 0px 1px 3px rgba(142, 82, 0, 0.7);
    @apply w-full rounded-full py-2 px-4 focus:outline-none;

    &::placeholder {
      color: #e4801d;
      opacity: 0.5;
    }
  }
}

@media (min-height: 540px) and (max-height: 703px) and (min-width: 768px) {
  .textInput {
    height: 36px !important;
  }
}
</style>
