<template>
  <div class="selectType text-center">
    <p class="text-gray-1100 text-lg pt-1 my-2 md:mt-7 md:mb-3.5">
      If you have access, select type:
    </p>

    <!-- button-wrapper -->
    <div class="flex flex-wrap space-x-4 space-y-4 w-full justify-center">
      <button
        :style="{ '--color': globalColorPanel.backgroundColor }"
        class="mt-4"
        :class="
          currentSelectedAccessType === 'AccessWithEmail'
            ? 'button-style-select'
            : 'button-style'
        "
        @click="changeAccessType('AccessWithEmail')"
      >
        Email
      </button>
      <button
        :style="{ '--color': globalColorPanel.backgroundColor }"
        class="space-x-0 space-y-0 mt-4"
        :class="
          currentSelectedAccessType === 'AccessWithSocialAcnt'
            ? 'button-style-select'
            : 'button-style'
        "
        @click="changeAccessType('AccessWithSocialAcnt')"
      >
        Social Media
      </button>
      <button
        v-if="!hideOption"
        :style="{ '--color': globalColorPanel.backgroundColor }"
        :class="
          currentSelectedAccessType === 'AccessWithWebsite'
            ? 'button-style-select'
            : 'button-style'
        "
        @click="changeAccessType('AccessWithWebsite')"
      >
        Website
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useStore } from 'vuex'

defineProps({
  hideOption: {
    type: Boolean,
    default: false,
  },
})
const store = useStore()
const route = useRoute()
const currentSelectedAccessType = ref<string>('SelectedAccessType')
const changeAccessType = (selectType: string) => {
  currentSelectedAccessType.value = selectType
  store.commit('home/SET_SELECT_ACCESS_TYPE', selectType)
}

// Access state and getters
const globalColorPanel = computed(() => store.state.globalColorPanel)
const getSelectAccessType = computed(
  () => store.getters['home/getSelectAccessType'],
)

onMounted(() => {
  const fullPath = route.fullPath
  if (
    fullPath === '/home?addMoreFeed=true&provider=Twitter' ||
    fullPath === '/home?addMoreFeed=true&provider=Facebook' ||
    fullPath === '/home?addMoreFeed=true&provider=LinkedIn' ||
    fullPath === '/home?addMoreFeed=true&provider=Instagram' ||
    fullPath === '/home?addMoreFeed=true&provider=YouTube'
  ) {
    store.commit('socialFeed/SET_ARCHIVE_FEED', true)
    store.commit('home/SET_SELECT_ACCESS_TYPE', 'AccessWithSocialAcnt')
  } else if (fullPath === '/home?addMoreFeed=true&provider=Web') {
    store.commit('socialFeed/SET_ARCHIVE_FEED', true)
    store.commit('home/SET_SELECT_ACCESS_TYPE', 'AccessWithWebsite')
  } else if (
    fullPath === '/home?addMoreFeed=true&provider=Microsoft' ||
    fullPath === '/home?addMoreFeed=true&provider=Google'
  ) {
    store.commit('socialFeed/SET_ARCHIVE_FEED', true)
    store.commit('home/SET_SELECT_ACCESS_TYPE', 'AccessWithEmail')
  }
})
</script>

<style lang="scss" scoped>
$color: var(--color);
.button-style {
  @apply h-10 w-34 md:w-38 text-center leading-3 md:leading-5 whitespace-nowrap bg-white text-orange-600 font-bold rounded-full;
  color: $color;
}
.button-style-select {
  @apply h-10 w-34 md:w-38 text-center leading-3 md:leading-5 whitespace-nowrap font-bold rounded-full;
  color: #ffffff;
  background-color: $color;
}
.button-style:hover {
  color: #ffffff;
  background-color: $color;
}
.button-wrapper {
  margin-left: -9px !important;
}
.placeholder-dynamic-color::placeholder {
  --tw-placeholder-opacity: 1;
  color: rgba(228, 128, 29, var(--color));
}
</style>
