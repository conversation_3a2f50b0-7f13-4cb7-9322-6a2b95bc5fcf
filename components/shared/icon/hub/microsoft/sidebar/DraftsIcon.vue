<script setup lang="ts">
const props = defineProps({
  color: {
    type: String,
    default: '#525252',
  },
})
</script>

<template>
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="15"
    height="16"
    viewBox="0 0 15 16"
  >
    <path
      id="Path_2543"
      data-name="Path 2543"
      d="M81.5-864a1.468,1.468,0,0,1-1.059-.427A1.38,1.38,0,0,1,80-865.455a1.38,1.38,0,0,1,.441-1.027,1.467,1.467,0,0,1,1.059-.427h12a1.467,1.467,0,0,1,1.059.427A1.38,1.38,0,0,1,95-865.455a1.38,1.38,0,0,1-.441,1.027A1.468,1.468,0,0,1,93.5-864Zm1.5-5.818h1.05l5.85-5.655L89.356-876l-.525-.509L83-870.836Zm-1.5.727v-2.055a.732.732,0,0,1,.056-.282.725.725,0,0,1,.169-.245l8.175-7.909a1.461,1.461,0,0,1,.478-.309A1.521,1.521,0,0,1,90.95-880a1.593,1.593,0,0,1,.581.109,1.5,1.5,0,0,1,.506.327l1.031,1.018a1.187,1.187,0,0,1,.328.473,1.58,1.58,0,0,1,.1.564,1.461,1.461,0,0,1-.1.536,1.36,1.36,0,0,1-.328.482l-8.156,7.909a.752.752,0,0,1-.***********,0,0,1-.291.055H82.25a.737.737,0,0,1-.534-.209A.693.693,0,0,1,81.5-869.091ZM92-877.527l-1.05-1.018Zm-2.1,2.055L89.356-876l-.525-.509Z"
      transform="translate(-80 880)"
      :fill="color"
    />
  </svg>
</template>

<style scoped>
.router-link-exact-active > .menu-container > svg > path,
.router-link-active > .menu-container > svg > path {
  fill: #fff;
}
</style>
