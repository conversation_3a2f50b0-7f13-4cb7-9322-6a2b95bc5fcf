<script setup lang="ts">
const props = defineProps({
  color: {
    type: String,
    default: '#525252',
  },
})
</script>

<template>
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="14"
    height="16"
    viewBox="0 0 14 16"
  >
    <path
      id="Path_2545"
      data-name="Path 2545"
      d="M85.433-874.272v11.733h6.533v-3.2a1.113,1.113,0,0,1,.268-.76.849.849,0,0,1,.665-.307h2.8v-7.467ZM90.567-868.406Zm-7,5.867V-874.3a2.135,2.135,0,0,1,.56-1.493,1.743,1.743,0,0,1,1.33-.613H95.7a1.693,1.693,0,0,1,1.318.627,2.21,2.21,0,0,1,.548,1.507v7.653a2.37,2.37,0,0,1-.14.813,2.116,2.116,0,0,1-.4.68l-3.593,4.107a1.818,1.818,0,0,1-.6.453,1.644,1.644,0,0,1-.712.16h-6.7a1.693,1.693,0,0,1-1.318-.627A2.21,2.21,0,0,1,83.567-862.539Z"
      transform="translate(-83.567 876.406)"
      :fill="color"
    />
  </svg>
</template>

<style scoped>
.router-link-exact-active > .menu-container > svg > path,
.router-link-active > .menu-container > svg > path {
  fill: #fff;
}
</style>
