<script setup lang="ts">
const props = defineProps({
  color: {
    type: String,
    default: '#525252',
  },
})
</script>

<template>
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="16"
    height="13"
    viewBox="0 0 16 13"
  >
    <path
      id="Path_2542"
      data-name="Path 2542"
      d="M81.6-787a1.529,1.529,0,0,1-1.13-.477,1.577,1.577,0,0,1-.47-1.148v-9.75a1.577,1.577,0,0,1,.47-1.148A1.529,1.529,0,0,1,81.6-800h4.14a1.559,1.559,0,0,1,.61.122,1.554,1.554,0,0,1,.51.345L88-798.375h6.4a1.529,1.529,0,0,1,1.13.477A1.577,1.577,0,0,1,96-796.75v8.125a1.577,1.577,0,0,1-.47,1.148A1.529,1.529,0,0,1,94.4-787Zm0-1.625H94.4v-8.125H87.34l-1.6-1.625H81.6Zm0,0v0Zm6.4-.812a3.058,3.058,0,0,0,2.26-.955,3.154,3.154,0,0,0,.94-2.3,3.154,3.154,0,0,0-.94-2.3,3.058,3.058,0,0,0-2.26-.955,3.058,3.058,0,0,0-2.26.955,3.154,3.154,0,0,0-.94,2.3,3.154,3.154,0,0,0,.94,2.3A3.058,3.058,0,0,0,88-789.437Zm-.92-5.038a3.939,3.939,0,0,1,.44-.173,1.554,1.554,0,0,1,.48-.071,1.916,1.916,0,0,1,1.42.589,1.977,1.977,0,0,1,.58,1.442,1.623,1.623,0,0,1-.07.487,4.076,4.076,0,0,1-.17.447Zm.92,3.819a1.916,1.916,0,0,1-1.42-.589,1.977,1.977,0,0,1-.58-1.442,1.624,1.624,0,0,1,.07-.488,4.083,4.083,0,0,1,.17-.447l2.68,2.722a3.95,3.95,0,0,1-.44.173A1.553,1.553,0,0,1,88-790.656Z"
      transform="translate(-80 800)"
      :fill="color"
    />
  </svg>
</template>

<style scoped>
.router-link-exact-active > .menu-container > svg > path,
.router-link-active > .menu-container > svg > path {
  fill: #fff;
}
</style>
