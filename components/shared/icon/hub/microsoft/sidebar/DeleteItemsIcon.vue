<script setup lang="ts">
const props = defineProps({
  color: {
    type: String,
    default: '#525252',
  },
})
</script>

<template>
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="14"
    height="16"
    viewBox="0 0 14 16"
  >
    <path
      id="Path_2541"
      data-name="Path 2541"
      d="M162.625-824a1.672,1.672,0,0,1-1.236-.522,1.726,1.726,0,0,1-.514-1.256v-11.556a.84.84,0,0,1-.623-.256.867.867,0,0,1-.252-.633.867.867,0,0,1,.252-.633.84.84,0,0,1,.623-.256h3.5a.867.867,0,0,1,.252-.633.84.84,0,0,1,.623-.256h3.5a.84.84,0,0,1,.623.256.867.867,0,0,1,.252.633h3.5a.84.84,0,0,1,.623.256.867.867,0,0,1,.252.633.867.867,0,0,1-.252.633.84.84,0,0,1-.623.256v11.556a1.726,1.726,0,0,1-.514,1.256,1.672,1.672,0,0,1-1.236.522Zm8.75-13.333h-8.75v11.556h8.75Zm-6.125,9.778a.84.84,0,0,0,.623-.256.867.867,0,0,0,.252-.633v-6.222a.867.867,0,0,0-.252-.633.84.84,0,0,0-.623-.256.84.84,0,0,0-.623.256.867.867,0,0,0-.252.633v6.222a.867.867,0,0,0,.252.633A.84.84,0,0,0,165.25-827.556Zm3.5,0a.84.84,0,0,0,.623-.256.867.867,0,0,0,.252-.633v-6.222a.867.867,0,0,0-.252-.633.84.84,0,0,0-.623-.256.84.84,0,0,0-.623.256.867.867,0,0,0-.252.633v6.222a.867.867,0,0,0,.252.633A.84.84,0,0,0,168.75-827.556Zm-6.125-9.778v0Z"
      transform="translate(-160 840)"
      :fill="color"
    />
  </svg>
</template>

<style scoped>
.router-link-exact-active > .menu-container > svg > path,
.router-link-active > .menu-container > svg > path {
  fill: #fff;
}
</style>
