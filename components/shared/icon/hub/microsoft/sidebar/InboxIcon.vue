<script setup lang="ts">
const props = defineProps({
  color: {
    type: String,
    default: '#525252',
  },
})
</script>

<template>
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="16"
    height="16"
    viewBox="0 0 16 16"
  >
    <path
      id="Path_2435"
      data-name="Path 2435"
      d="M121.778-824a1.712,1.712,0,0,1-1.256-.522,1.712,1.712,0,0,1-.522-1.256v-12.444a1.712,1.712,0,0,1,.522-1.256,1.712,1.712,0,0,1,1.256-.522h12.444a1.712,1.712,0,0,1,1.256.522,1.712,1.712,0,0,1,.522,1.256v12.444a1.712,1.712,0,0,1-.522,1.256,1.712,1.712,0,0,1-1.256.522Zm0-1.778h12.444v-2.667h-2.667a4.73,4.73,0,0,1-1.589,1.311,4.3,4.3,0,0,1-1.967.467,4.3,4.3,0,0,1-1.967-.467,4.73,4.73,0,0,1-1.589-1.311h-2.667ZM128-828.444a2.467,2.467,0,0,0,1.311-.367,2.974,2.974,0,0,0,.978-.967,1.064,1.064,0,0,1,.333-.322.836.836,0,0,1,.444-.122h3.156v-8H121.778v8h3.156a.836.836,0,0,1,.444.122,1.064,1.064,0,0,1,.333.322,2.974,2.974,0,0,0,.978.967A2.467,2.467,0,0,0,128-828.444Zm-6.222,2.667h0Z"
      transform="translate(-120 840)"
      :fill="color"
    />
  </svg>
</template>

<style scoped>
.router-link-exact-active > .menu-container > svg > path,
.router-link-active > .menu-container > svg > path {
  fill: #fff;
}
</style>
