<script setup lang="ts">
const props = defineProps({
  color: {
    type: String,
    default: '#525252',
  },
})
</script>

<template>
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="16"
    height="14"
    viewBox="0 0 16 14"
  >
    <path
      id="Path_2438"
      data-name="Path 2438"
      d="M135.426-772.419l-14.141,6.061a.9.9,0,0,1-.872-.082.874.874,0,0,1-.413-.781v-12.122a.873.873,0,0,1,.413-.781.9.9,0,0,1,.872-.082l14.141,6.061a.883.883,0,0,1,.574.863A.883.883,0,0,1,135.426-772.419Zm-13.59,3.8,10.881-4.662-10.881-4.662v3.264l5.509,1.4-5.509,1.4Zm0,0v0Z"
      transform="translate(-120 780.282)"
      :fill="color"
    />
  </svg>
</template>

<style scoped>
.router-link-exact-active > .menu-container > svg > path,
.router-link-active > .menu-container > svg > path {
  fill: #fff;
}
</style>
