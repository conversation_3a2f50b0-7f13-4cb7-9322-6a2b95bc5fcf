<script setup lang="ts">
const props = defineProps({
  color: {
    type: String,
    default: '#525252',
  },
})
</script>

<template>
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="16"
    height="16"
    viewBox="0 0 16 16"
  >
    <path
      id="Path_2557"
      data-name="Path 2557"
      d="M121.778-824a1.712,1.712,0,0,1-1.256-.522,1.712,1.712,0,0,1-.522-1.256v-12.444a1.712,1.712,0,0,1,.522-1.256,1.712,1.712,0,0,1,1.256-.522h12.444a1.712,1.712,0,0,1,1.256.522,1.712,1.712,0,0,1,.522,1.256v12.444a1.712,1.712,0,0,1-.522,1.256,1.712,1.712,0,0,1-1.256.522ZM128-828.444a2.467,2.467,0,0,0,1.311-.367,2.974,2.974,0,0,0,.978-.967,1.064,1.064,0,0,1,.333-.322.836.836,0,0,1,.444-.122h3.156v-8H121.778v8h3.156a.836.836,0,0,1,.444.122,1.064,1.064,0,0,1,.333.322,2.974,2.974,0,0,0,.978.967A2.467,2.467,0,0,0,128-828.444Z"
      transform="translate(-120 840)"
      :fill="color"
    />
  </svg>
</template>

<style scoped>
.router-link-exact-active > svg > path,
.router-link-active > svg > path {
  fill: #fff;
}
</style>
