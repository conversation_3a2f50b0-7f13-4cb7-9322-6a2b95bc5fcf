<template>
  <div class="flex flex-col mt-3">
    <div class="table min-width-22">
      <div class="header min-width-22 flex w-full">
        <div
          v-for="(item, index) in table.th"
          :key="index"
          scope="col"
          class="text-left text-base font-bold th w-3/12"
          :class="index === 1 || index === 2 ? 'min-width-6' : 'min-width-10'"
        >
          {{ item.name }}
        </div>
      </div>

      <div class="tbody min-width-22">
        <div
          v-for="rows in searchTableItems"
          :key="rows.id"
          class="inner-body min-width-22 flex"
        >
          <div
            class="relative has-tooltip min-width-10 cursor-pointer td"
            @click="selectUserData(rows)"
          >
            <div class="w-full">
              <p class="text-xl block">{{ $strLimit(rows.username, 18) }}</p>
              <span
                v-if="rows.username.length > 18"
                class="tooltip w-54 break-words"
                >{{ rows.username }}</span
              >
            </div>
          </div>
          <div
            class="min-width-6 cursor-pointer td"
            @click="selectUserData(rows)"
          >
            <div class="flex flex-row items-center">
              <div class="flex-shrink-0 w-6 h-6 mr-2">
                <img
                  v-if="rows.profileImageUrl"
                  class="w-6 h-6 mt-0 rounded-full border border-gray-400"
                  :src="rows.profileImageUrl"
                  :alt="`${rows.username} Profile Picture`"
                />
                <template v-else>
                  <facebook-icon
                    v-if="rows.provider === 'Facebook'"
                    class="w-6 h-6 mt-0 border border-gray-400"
                  ></facebook-icon>
                  <instagram-icon
                    v-if="rows.provider === 'Instagram'"
                    class="w-6 h-6 mt-0 border border-gray-400 rounded-full"
                  ></instagram-icon>
                  <!-- <twitter-icon
                    v-if="rows.provider === 'Twitter'"
                    class="w-6 h-6 mt-0 border border-gray-400"
                  ></twitter-icon> -->
                  <div
                    v-if="rows.provider === 'Twitter'"
                    class="w-6 h-6 mt-0 border border-gray-400 twitter-icon"
                  ></div>
                  <linkedin-icon
                    v-if="rows.provider === 'LinkedIn'"
                    class="w-6 h-6 mt-0 border border-gray-400"
                  ></linkedin-icon>
                  <gmail-icon
                    v-if="rows.provider === 'Google'"
                    class="w-6 h-6 mt-0 border border-gray-400"
                  ></gmail-icon>
                  <microsoft-icon
                    v-if="rows.provider === 'Microsoft'"
                    class="w-6 h-6 mt-0 border border-gray-400 rounded-full"
                  ></microsoft-icon>
                  <PinterestIcon
                    v-if="rows.provider === 'Pinterest'"
                    class="w-6 h-6 mt-0 border border-gray-400 rounded-full"
                  ></PinterestIcon>
                  <RedditIcon
                    v-if="rows.provider === 'Reddit'"
                    class="w-6 h-6 mt-0 border border-gray-400 rounded-full"
                  ></RedditIcon>
                  <TikTokIcon
                    v-if="rows.provider === 'TikTok'"
                    class="w-6 h-6 mt-0 border border-gray-400 rounded-full"
                  ></TikTokIcon>
                </template>
              </div>
              <div class="text-xl whitespace-nowrap">
                {{
                  rows.provider === 'Twitter' ? 'X (Twitter)' : rows.provider
                }}
              </div>
            </div>
          </div>

          <div
            class="text-right whitespace-nowrap relative has-tooltip min-width-6 td"
            @click="downloadLatestArchive(rows.id)"
          >
            <a>
              <ClientOnly>
                <fa
                  :disabled="downloadProcess"
                  class="mr-0.875 cursor-pointer text-green-1100"
                  :icon="['fas', 'download']"
                />
              </ClientOnly>
              <span class="download_tooltip">Download</span>
            </a>
          </div>
        </div>

        <div
          v-if="searchTableItems.length < 1"
          class="text-center test-xl mt-24 font-medium"
        >
          No Data Found
        </div>
      </div>
    </div>
  </div>
</template>



<script setup lang="ts">
import FacebookIcon from '~/components/shared/icon/FacebookIcon.vue'
import LinkedinIcon from '~/components/shared/icon/LinkedinIcon.vue'
import InstagramIcon from '~/components/shared/icon/InstagramIcon.vue'
import GmailIcon from '~/components/shared/icon/GmailIcon.vue'
import MicrosoftIcon from '~/components/shared/icon/MicrosoftIcon.vue'
import PinterestIcon from '~/components/shared/icon/PinterestIcon.vue'
import RedditIcon from '~/components/shared/icon/RedditIcon.vue'
import TikTokIcon from '~/components/shared/icon/tiktok/TiktokIcon.vue'
import { ARCHIVE_DOWNLOAD } from '~/constants/urls'
import { useFetched } from '~/composables/useFetched'

import { useNuxtApp } from '#app'
import { useStore } from 'vuex'
const props = defineProps({
  table: {
    type: Object,
    default: () => {},
  },
  searchItem: {
    type: String,
    default: '',
  },
})

const nuxtApp = useNuxtApp()
const { fetch } = useFetched()
const store = useStore()

const downloadProcess = ref<boolean>(false) 
//  computed
const socialFeeds = computed(() => store.state.socialFeed.socialFeeds)
const showSinglePost = computed(() => store.state.socialFeed.showSinglePost)
const showSingleImagePost = computed(
  () => store.state.socialFeed.showSingleImagePost,
)

const searchTableItems = computed(() => {
  return props.table.tbody.filter((item:{provider:string,username:string}) => {
    return (
      item.provider.toUpperCase().match(props.searchItem.toUpperCase()) ||
      item.username.toUpperCase().match(props.searchItem.toUpperCase())
    )
  })
})

// methods
const selectUserData = (user:{name:string,username:string,provider:string,createdAt:string}) => {
  let username = ''
  if (user.provider === 'Facebook') {
    username = user.name ? user.name : user.username
  } else {
    username = user.username ? user.username : user.name
  }
  nuxtApp.$bus.$emit('clear-all-date-range')
  store.commit('home/RESET_START_END_DATE')
  if (showSinglePost.value || showSingleImagePost.value) {
    setTimeout(() => {
      socialFeeds.value.forEach((element:{username:string,id:number}) => {
        if (user.username === element.username) {
          store.commit('home/SET_CURRENT_SOCIAL_COMPONENT', {
            provider: user.provider,
            username,
            id: element.id,
            selectedFeed: user,
            archiveDate: user.createdAt,
          })
        }
      })
    }, 305)
  } else if (user.provider === 'Google' || user.provider === 'Microsoft') {
    store.commit('home/SET_EMAIL_DYNAMIC_COMP', {
      comp: 'EmailContent',
    })
    store.commit('home/EXPAND_FULL_IMAGE', false)
    store.commit('home/SET_TEMP_ARRAY', [])
    setTimeout(() => {
      socialFeeds.value.forEach((element:{username:string,id:number}) => {
        if (user.username === element.username) {
          store.commit('home/SET_CURRENT_SOCIAL_COMPONENT', {
            provider: user.provider,
            username,
            id: element.id,
            selectedFeed: user,
          })
        }
      })
    }, 500)
  } else {
    socialFeeds.value.forEach((element:{username:string,id:number}) => {
      if (user.username === element.username) {
        store.commit('home/SET_CURRENT_SOCIAL_COMPONENT', {
          provider: user.provider,
          username,
          id: element.id,
          selectedFeed: user,
          archiveDate: user.createdAt,
        })
      }
    })
  }
  store.commit('home/SET_SHOW_COMP', false)
  store.commit('home/SET_CURRENT_TAB', 'All')
  store.dispatch('socialFeed/singlePostClose', false)
  store.dispatch('socialFeed/singleImagePostClose', false)
}

interface ResponseType {
  success: boolean;
  file:string;
}

const downloadLatestArchive = async (id:number) => {
  nuxtApp.$toast('clear')
  downloadProcess.value = true
  store.commit('archive/SET_DOWNLOAD_LOADER', true)
  try {
    nuxtApp.$toast('success', {
      message: 'Download is processing',
      className: 'toasted-bg-archive',
    })
    const response:ResponseType = await fetch(ARCHIVE_DOWNLOAD, {
      body: { id: id },
      method: 'POST',
    })
    if (response.success) {
      const url = response.file
      const link = document.createElement('a')
      link.href = url
      link.setAttribute('download', url)
      document.body.appendChild(link)
      link.click()
      link.remove()
      downloadProcess.value = false
      store.commit('archive/SET_DOWNLOAD_LOADER', false)
    } else {
      nuxtApp.$toast('error', {
        message: 'No data to be downloaded',
        className: 'toasted-bg-alert',
      })
    }
  } catch (err) {
    downloadProcess.value = false
    store.commit('archive/SET_DOWNLOAD_LOADER', false)
    console.log(err)
  } finally {
    downloadProcess.value = false
    store.commit('archive/SET_DOWNLOAD_LOADER', false)
  }
}

</script>

<style lang="scss" scoped>
.twitter-icon {
  background-size: cover;
  animation: twitter 6s ease-in-out;
  animation-iteration-count: infinite;
}
@keyframes twitter {
  0% {
    background-image: url('~/assets/img/icon/TwitterIcon/twitter.svg');
  }
  50% {
    background-image: url('~/assets/img/icon/TwitterIcon/X_logo.png');
  }
  100% {
    background-image: url('~/assets/img/icon/TwitterIcon/twitter.svg');
  }
}
.table_wrapper {
  @apply -my-2 sm:-mx-6 lg:-mx-8;
}
.table-body_wrapper {
  @apply py-0
          align-middle
          inline-block
          min-w-full
          sm:px-6
          md:px-8;
}
/*  Table Head  */
// .table .thead .header {
//   position: sticky;
//   top: 0;
//   background: white;
//   z-index: 10;
// }
.table .header .th {
  font-style: normal;
  font-variant: normal;
  font-weight: bold;
  color: #505050;
  letter-spacing: 0;
  position: sticky;
  top: 0;
  background: white;
  z-index: 10;
  @apply px-4 py-3 pb-0 text-base w-4/12;
}

.table .header .th:last-child {
  @apply text-right;
}

/*  Table body  */
.table .tbody .inner-body .td {
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  font-size: 0.875rem;
  color: #656565;
  letter-spacing: 0;
  @apply px-4 py-2 text-base w-4/12;
}

.table .tbody .inner-body:hover {
  @apply min-width-22;
  background-color: #e4801d;
  color: #ffffff;
  @apply rounded-full;
}

.table .tbody .inner-body:hover .td {
  color: #ffffff;
}

.scroll {
  // overflow-y: hidden;
  // overflow-x: hidden;
  scrollbar-color: #ff8308 #ececec; /* Firefox 64 */
  /* Handle */
  &::-webkit-scrollbar-thumb {
    background: #ff8308;
  }
  /* Handle on hover */
  &::-webkit-scrollbar-thumb:hover {
    background: #ff8308;
  }
}

.tooltip {
  @apply absolute
  bg-offwhite-200
  text-yellow-primary
  z-100
  left-0
  -top-5
  text-left
  invisible
  p-1.5
  px-4
  rounded-xl 
  shadow-lg;
}
.download_tooltip {
  width: auto;
  @apply absolute
  bg-offwhite-200
  text-yellow-primary
  z-100
  right-0
  -top-5
  text-left
  invisible
  p-1.5
  px-4
  rounded-xl 
  shadow-lg;
}
.has-tooltip:hover .tooltip {
  @apply visible;
  transition: all 0.3s linear;
}
.has-tooltip:hover .download_tooltip {
  @apply visible;
  transition: all 0.3s linear;
}
.min-width-6 {
  min-width: 8rem;
}
.min-width-10 {
  min-width: 16rem;
}
.min-width-22 {
  min-width: 34rem;
}
@media (min-width: 2560px) {
  .min-width-10 {
    min-width: 20rem;
  }
  .min-width-22 {
    min-width: 36rem;
  }
}
@media (min-width: 1026px) and (max-width: 1400px) {
  .min-width-6 {
    min-width: 8rem;
  }
  .min-width-10 {
    min-width: 30rem;
  }
  .min-width-22 {
    min-width: 46rem;
  }
}
@media (min-width: 1024px) and (max-width: 1025px) {
  .min-width-6 {
    min-width: 8rem;
  }
  .min-width-10 {
    min-width: 24rem;
  }
  .min-width-22 {
    min-width: 40rem;
  }
}
</style>
