<template>
  <div class="w-full flex flex-nowrap bg-white my-3 whole-card">
    <div
      :id="article.id"
      class="card h-auto cursor-pointer"
      :class="article.type !== 'reel' ? 'py-3' : 'py-0'"
    >
      <div
        v-if="article.type !== 'reel'"
        class="flex px-3"
        @click="
          ;(article.statusType === 'mobile_status_update' ||
            article.statusType === 'added_video' ||
            article.type === 'status' ||
            article.type === 'video' ||
            article.type === 'reel' ||
            article.linkName ||
            article.linkName.includes('giphy') ||
            article.linkName.includes('tenor') ||
            article.archivedSourceUrl.includes('http') ||
            article.sourceUrl.includes('http') ||
            (article.event &&
              !(
                article.attachmentImages && article.attachmentImages.length > 0
              ) &&
              !(
                article.archivedAttachmentImages &&
                article.archivedAttachmentImages.length > 0
              ))) &&
          article.type !== 'photo'
            ? showSinglePost(article)
            : showSingleImagePost(article, 0)
        "
      >
        <SharedIconFacebookIcon
          v-if="!article.profileImageUrl"
          class="w-13 h-13 rounded-full border border-gray-400"
        ></SharedIconFacebookIcon>
        <img
          height="42"
          width="42"
          :src="article.profileImageUrl"
          class="rounded-full profile-image"
          alt="dp"
          srcset=""
        />
        <div class="ml-2 lineHeight-1">
          <h2 class="profile-name">
            {{ articles.name }}
            <span v-if="article.placeName"
              ><span
                v-if="article.placeName.includes(article.placeCity)"
                class="text-gray-light text-base"
                >is in </span
              ><span v-else class="text-gray-light text-base">is at </span
              >{{ article.placeName }}</span
            >
          </h2>

          <date-time
            class="text-gray-light text-xs"
            :datetime="
              article.updatedAt
                ? article.updatedAt
                : article.createdAt
                  ? article.createdAt
                  : ''
            "
            format="MMMM dd, yyyy"
            :show-time="false"
            :friendly="false"
          ></date-time>
          <span
            v-if="
              article.placeCity &&
              !article.placeName.includes(article.placeCity)
            "
            class="text-xs text-gray-light"
          >
            <span> · </span>{{ article.placeCity }}
          </span>
          <span v-if="article.privacy" class="text-sm text-gray-light">
            <span> · </span
            ><span>
              <ClientOnly>
                <fa
                  v-if="article.privacy === 'Public'"
                  class="text-gray-light text-xs font-normal"
                  :icon="['fas', 'earth-americas']"
                />
                <fa
                  v-if="
                    article.privacy === 'Friends of friends' ||
                    article.privacy === 'Custom' ||
                    article.privacy === 'Friends'
                  "
                  class="text-gray-light text-xs font-normal"
                  :icon="['fas', 'gear']"
                />
                <fa
                  v-if="article.privacy === 'Only me'"
                  class="text-gray-light text-xs font-normal"
                  :icon="['fas', 'lock']"
                />
                <fa
                  v-if="
                    article.privacy === 'Your friends' ||
                    article.privacy === 'Your friends of friends'
                  "
                  class="text-gray-light text-xs font-normal"
                  :icon="['fas', 'user-group']"
                />
              </ClientOnly>
            </span>
          </span>
        </div>
      </div>
      <div
        v-if="article.type !== 'reel'"
        class="px-3"
        @click="
          ;(article.statusType === 'mobile_status_update' ||
            article.statusType === 'added_video' ||
            article.type === 'status' ||
            article.type === 'video' ||
            article.type === 'reel' ||
            article.archivedSourceUrl.includes('http') ||
            article.sourceUrl.includes('http')) &&
          article.type !== 'photo'
            ? showSinglePost(article)
            : showSingleImagePost(article, 0)
        "
      >
        <p
          v-if="article.text && article.text !== article.link"
          v-see-more.right="200"
          class="comment break-words"
          v-html="addLineBreak(article.text, article.provider)"
        ></p>
        <div
          v-if="
            article.statusType !== 'created_event' &&
            article.type !== 'event' &&
            !article.event
          "
        >
          <p
            v-if="article.description && !article.link"
            v-see-more.right="200"
            class="comment break-words"
            v-html="addLineBreak(article.description, article.provider)"
          ></p>
          <!-- <button class="mt-2 block">See Media</button> -->
          <a
            v-if="article.link && article.linkName !== 'Born'"
            :href="article.link"
            target="_blank"
            rel="noopener noreferrer"
            class="link break-words line-clamp-1"
            >{{ article.link }}</a
          >
        </div>
      </div>
      <div
        v-if="
          (article.archivedAttachmentImages.length > 0 &&
            article.archivedAttachmentImages.length < 5 &&
            !article.archivedSourceUrl.includes('http') &&
            !article.sourceUrl.includes('http')) ||
          (article.attachmentImages.length > 0 &&
            article.attachmentImages.length < 5 &&
            !article.archivedSourceUrl.includes('http') &&
            !article.sourceUrl.includes('http'))
        "
        class="flex justify-center flex-wrap"
      >
        <div
          v-for="(feature, imageIndex) in article.archivedAttachmentImages
            ? article.archivedAttachmentImages.slice(0, 5)
            : article.attachmentImages.slice(0, 5)"
          :key="feature"
          class="flex justify-center items-center cursor-pointer"
          :class="imageIndex < 2 ? 'column-2' : 'column-3'"
          @click="showSingleImagePost(article, imageIndex)"
        >
          <img class="w-full h-full hover-border" :src="feature" />
        </div>
      </div>
      <div
        v-else-if="
          (article.archivedAttachmentImages.length > 0 &&
            article.archivedAttachmentImages.length >= 5 &&
            !article.archivedSourceUrl.includes('http') &&
            !article.sourceUrl.includes('http')) ||
          (article.attachmentImages.length > 0 &&
            article.attachmentImages.length >= 5 &&
            !article.archivedSourceUrl.includes('http') &&
            !article.sourceUrl.includes('http'))
        "
        class="flex justify-center flex-nowrap"
      >
        <div class="flex flex-col w-1/2">
          <div
            v-for="(feature, imageIndex) in article.archivedAttachmentImages
              ? article.archivedAttachmentImages.slice(0, 2)
              : article.attachmentImages.slice(0, 2)"
            :key="feature"
            class="flex justify-center items-center cursor-pointer"
            :class="imageIndex < 2 ? 'column-2' : 'column-3'"
            @click.stop="showSingleImagePost(article, imageIndex)"
          >
            <img class="w-full h-full hover-border" :src="feature" />
          </div>
        </div>
        <div class="flex flex-col w-1/2">
          <div
            v-for="(feature, imageIndex) in article.archivedAttachmentImages
              ? article.archivedAttachmentImages.slice(2, 5)
              : article.attachmentImages.slice(2, 5)"
            :key="feature"
            class="flex justify-center items-center cursor-pointer"
            :class="imageIndex < 3 ? 'column-2' : 'column-3'"
            @click.stop="showSingleImagePost(article, imageIndex + 2)"
          >
            <img class="w-full h-full hover-border" :src="feature" />
            <div
              v-if="imageIndex === 2"
              class="w-full h-full absolute z-0 post-overlay"
            >
              <div class="sub-overlay"></div>
              <p
                class="xl:text-5xl md:text-3xl text-xl relative opacity-100 text-white font-bold"
              >
                {{
                  `+${
                    article.archivedAttachmentImages
                      ? article.archivedAttachmentImages.length - 4
                      : article.attachmentImages.length - 4
                  }`
                }}
              </p>
            </div>
          </div>
        </div>
      </div>
      <div
        v-else-if="
          (article.archivedFullPicture || article.fullPicture) &&
          !article.archivedSourceUrl &&
          !article.sourceUrl &&
          (article.type === 'photo' || article.statusType === 'added_photos') &&
          article.archivedAttachmentImages.length === 0 &&
          article.attachmentImages.length === 0
        "
        class="w-full pt-6"
        @click="showSingleImagePost(article, 0)"
      >
        <img
          class="w-full posted-image"
          :src="
            article.linkName.includes('giphy') ||
            article.linkName.includes('tenor')
              ? article.link
              : article.archivedFullPicture
                ? article.archivedFullPicture
                : article.fullPicture
          "
          :alt="`${article.name} user Posted Image`"
        />
      </div>
      <div
        v-else-if="
          (article.archivedFullPicture || article.fullPicture) &&
          !article.archivedSourceUrl &&
          !article.sourceUrl &&
          (article.statusType === 'shared_story' ||
            article.statusType === 'mobile_status_update') &&
          article.archivedAttachmentImages.length === 0 &&
          article.attachmentImages.length === 0
        "
        class="w-full pt-6"
        @click="
          article.linkName.includes('giphy') ||
          article.linkName.includes('tenor')
            ? showSinglePost(article)
            : showSingleImagePost(article, 0)
        "
      >
        <a
          v-if="
            !article.linkName.includes('giphy') &&
            !article.linkName.includes('tenor') &&
            article.link
          "
          :href="article.link"
          target="_blank"
          rel="noopener noreferrer"
        >
          <img
            class="w-full posted-image"
            :src="
              article.linkName.includes('giphy') ||
              article.linkName.includes('tenor')
                ? article.link
                : article.archivedFullPicture
                  ? article.archivedFullPicture
                  : article.fullPicture
            "
            :alt="`${article.name} user Posted Image`"
          />
        </a>

        <div v-else>
          <img
            class="w-full posted-image"
            :src="
              article.linkName.includes('giphy') ||
              article.linkName.includes('tenor')
                ? article.link
                : article.archivedFullPicture
                  ? article.archivedFullPicture
                  : article.fullPicture
            "
            :alt="`${article.name} user Posted Image`"
          />
        </div>
      </div>
      <div
        v-else-if="
          (article.archivedSourceUrl || article.sourceUrl) &&
          (article.type === 'video' ||
            article.statusType === 'added_video' ||
            article.statusType === 'shared_story' ||
            article.statusType === 'mobile_status_update') &&
          !article.archivedSourceUrl.includes('youtube') &&
          !article.sourceUrl.includes('youtube')
        "
        class="w-full py-6"
      >
        <video class="w-full" height="400" controls="controls">
          <source
            :src="
              article.archivedSourceUrl &&
              article.archivedSourceUrl.includes('http')
                ? article.archivedSourceUrl
                : article.sourceUrl
            "
          />
          <source
            :src="
              article.archivedFullPicture
                ? article.archivedFullPicture
                : article.fullPicture
            "
          />
        </video>
      </div>
      <div
        v-else-if="
          (article.archivedSourceUrl || article.sourceUrl) &&
          (article.type === 'video' ||
            article.statusType === 'added_video' ||
            article.statusType === 'shared_story' ||
            article.statusType === 'mobile_status_update') &&
          (article.archivedSourceUrl.includes('youtube') ||
            article.sourceUrl.includes('youtube'))
        "
        class="w-full py-6 h-96"
      >
        <iframe
          class="w-full"
          height="370"
          :src="
            article.archivedSourceUrl &&
            article.archivedSourceUrl.includes('http')
              ? article.archivedSourceUrl
              : article.sourceUrl
          "
        >
        </iframe>
      </div>
      <div
        v-else-if="
          (article.archivedSourceUrl || article.sourceUrl) &&
          article.type === 'reel' &&
          !article.archivedSourceUrl.includes('youtube') &&
          !article.sourceUrl.includes('youtube')
        "
        class="w-full py-0 reel main-reel relative drop-shadow-xl"
      >
        <div
          class="flex px-3 absolute top-4 left-0 z-1"
          @click="
            ;(article.statusType === 'mobile_status_update' ||
              article.statusType === 'added_video' ||
              article.type === 'status' ||
              article.type === 'video' ||
              article.type === 'reel' ||
              article.linkName ||
              article.linkName.includes('giphy') ||
              article.linkName.includes('tenor') ||
              article.archivedSourceUrl.includes('http') ||
              article.sourceUrl.includes('http') ||
              (article.event &&
                !(
                  article.attachmentImages &&
                  article.attachmentImages.length > 0
                ) &&
                !(
                  article.archivedAttachmentImages &&
                  article.archivedAttachmentImages.length > 0
                ))) &&
            article.type !== 'photo'
              ? showSinglePost(article)
              : showSingleImagePost(article, 0)
          "
        >
          <SharedIconFacebookIcon
            v-if="!article.profileImageUrl"
            class="w-13 h-13 rounded-full border border-gray-400"
          ></SharedIconFacebookIcon>
          <img
            height="42"
            width="42"
            :src="article.profileImageUrl"
            class="rounded-full profile-image"
            alt="dp"
            srcset=""
          />
          <div class="ml-2 lineHeight-1">
            <h2 class="text-xl text-white">
              {{ articles.name }}
              <span v-if="article.placeName"
                ><span
                  v-if="article.placeName.includes(article.placeCity)"
                  class="text-white text-base"
                  >is in </span
                ><span v-else class="text-white text-base">is at </span
                >{{ article.placeName }}</span
              >
            </h2>
            <span class="text-xs text-white">Reels</span
            ><span class="text-white text-xs mt-2"> · </span>
            <date-time
              class="text-white text-xs"
              :datetime="
                article.updatedAt
                  ? article.updatedAt
                  : article.createdAt
                    ? article.createdAt
                    : ''
              "
              format="MMMM dd, yyyy"
              :show-time="false"
              :friendly="false"
            ></date-time>
            <span
              v-if="
                article.placeCity &&
                !article.placeName.includes(article.placeCity)
              "
              class="text-xs text-white"
            >
              <span> · </span>{{ article.placeCity }}
            </span>
            <span v-if="article.privacy" class="text-sm text-white">
              <span> · </span
              ><span
                ><fa
                  v-if="article.privacy === 'Public'"
                  class="text-white text-xs font-normal"
                  :icon="['fas', 'earth-americas']"
                />
                <fa
                  v-if="
                    article.privacy === 'Friends of friends' ||
                    article.privacy === 'Custom' ||
                    article.privacy === 'Friends'
                  "
                  class="text-white text-xs font-normal"
                  :icon="['fas', 'gear']"
                />
                <fa
                  v-if="article.privacy === 'Only me'"
                  class="text-white text-xs font-normal"
                  :icon="['fas', 'lock']"
                />
                <fa
                  v-if="
                    article.privacy === 'Your friends' ||
                    article.privacy === 'Your friends of friends'
                  "
                  class="text-white text-xs font-normal"
                  :icon="['fas', 'user-group']"
                />
              </span>
            </span>
          </div>
        </div>
        <div
          class="px-3 absolute bottom-4 left-0 z-1"
          @click="
            ;(article.statusType === 'mobile_status_update' ||
              article.statusType === 'added_video' ||
              article.type === 'status' ||
              article.type === 'video' ||
              article.type === 'reel' ||
              article.archivedSourceUrl.includes('http') ||
              article.sourceUrl.includes('http')) &&
            article.type !== 'photo'
              ? showSinglePost(article)
              : showSingleImagePost(article, 0)
          "
        >
          <p
            v-if="article.text && article.text !== article.link"
            v-see-more.right="200"
            class="text-lg text-white break-words"
            v-html="addLineBreak(article.text, article.provider)"
          ></p>
          <div
            v-if="
              article.statusType !== 'created_event' &&
              article.type !== 'event' &&
              !article.event
            "
          >
            <p
              v-if="article.description && !article.link"
              v-see-more.right="200"
              class="text-lg text-white break-words"
              v-html="addLineBreak(article.description, article.provider)"
            ></p>
            <!-- <button class="mt-2 block">See Media</button> -->
            <!-- <a
            v-if="article.link && article.linkName !== 'Born'"
            :href="article.link"
            target="_blank"
            rel="noopener noreferrer"
            class="link break-words line-clamp-1"
            >{{ article.link }}</a
          > -->
          </div>
        </div>
        <div
          class="absolute right-4 bottom-4 flex flex-col space-y-6 text-white z-1"
        >
          <div class="flex flex-col space-y-2 items-center">
            <SharedIconReelLikeIcon
              v-if="article.reactionsCount === 0"
              class="w-10 h-10 drop-shadow-md"
            />
            <SharedIconReelLikeBgIcon class="w-10 h-10 drop-shadow-md" v-else />
            <p>{{ article.reactionsCount }}</p>
          </div>
          <div
            class="flex flex-col space-y-2 items-center"
            @click="
              ;[
                articles.type === 'Page' && article.commentsCount > 0
                  ? loadFeedComment()
                  : '',
              ]
            "
          >
            <SharedIconReelCommentIcon class="w-10 h-10 drop-shadow-md" />
            <p>{{ article.commentsCount }}</p>
          </div>
          <div class="flex flex-col space-y-2 items-center">
            <SharedIconReelShareIcon class="w-10 h-10 drop-shadow-md" />
            <p>{{ article.sharesCount }}</p>
          </div>
        </div>
        <div class="video-container">
          <video
            :id="`${article.id}-custom-video`"
            class="reel reel-video"
            @click="togglePlayPause(article.id)"
          >
            <source
              :src="
                article.archivedSourceUrl &&
                article.archivedSourceUrl.includes('http')
                  ? article.archivedSourceUrl
                  : article.sourceUrl
              "
            />
            <source
              :src="
                article.archivedFullPicture
                  ? article.archivedFullPicture
                  : article.fullPicture
              "
            />
          </video>
          <div class="custom-controls">
            <button
              :id="`${article.id}-play-pause`"
              class="w-9 h-9 reel-play-pause"
              @click="togglePlayPause(article.id)"
            >
              <img class="w-4 h-4" :src="pause" />
            </button>
            <button
              :id="`${article.id}-mute-unmute`"
              class="w-9 h-9"
              @click="toggleMuteUnmute(article.id)"
            >
              <img class="w-4 h-4" :src="unmute" />
            </button>
          </div>
        </div>
      </div>
      <div
        v-if="
          article.event ||
          article.statusType === 'created_event' ||
          article.type === 'event'
        "
        class="px-3.5 py-2 event_bg_color"
        @click="
          ;(article.attachmentImages && article.attachmentImages.length > 0) ||
          (article.archivedAttachmentImages &&
            article.archivedAttachmentImages.length > 0)
            ? showSingleImagePost(article, 0)
            : showSinglePost(article)
        "
      >
        <date-time
          class="text-red-soft text-xs md:text-sm"
          :datetime="article.event.start_time"
          :end-time="article.event.end_time"
          :show-time="false"
          :friendly="false"
          :show-day="true"
        ></date-time>
        <p
          v-if="article.event.name"
          v-see-more.right="200"
          class="text_color text-lg break-words"
          v-html="addLineBreak(article.event.name, article.provider)"
        ></p>
        <p
          v-if="article.placeName"
          class="description_color text-base break-words"
        >
          {{ article.placeName }}
        </p>
        <p
          v-if="article.description"
          v-see-more.right="200"
          class="description_color text-base break-words"
          v-html="addLineBreak(article.description, article.provider)"
        ></p>
        <!-- <button class="mt-2 block">See Media</button> -->
        <!-- <a
          v-if="article.link"
          :href="article.link"
          target="_blank"
          rel="noopener noreferrer"
          class="link break-words line-clamp-1"
          >{{ article.link }}</a
        > -->
      </div>
      <div
        v-if="
          article.linkName &&
          !article.linkName.includes('giphy') &&
          !article.linkName.includes('tenor') &&
          article.linkName !== 'Born' &&
          article.type !== 'event'
        "
        class="link-preview"
      >
        <p class="text-sm text-gray-400">{{ article.caption.toUpperCase() }}</p>
        <p v-if="article.linkName" class="text-lg font-bold text-gray-2300">
          {{ article.linkName }}
        </p>
        <p class="text-md text-gray-400 line-clamp-1">
          {{ article.description }}
        </p>
      </div>
      <div
        v-if="
          article.linkName &&
          article.linkName === 'Born' &&
          article.type !== 'event'
        "
        class="flex flex-col justify-center items-center"
        @click="showSinglePost(article)"
      >
        <div class="born-icon flex justify-center items-center rounded-full">
          <SharedIconBornIcon class="h-7 w-7" />
        </div>
        <p class="text-gray-2300 text-xl">
          Born on
          <date-time
            :datetime="article.createdAt"
            format="dd MMMM yyyy"
            :formatting="false"
            :show-time="false"
            :friendly="false"
          ></date-time>
        </p>
      </div>
      <div
        v-if="article.type !== 'reel'"
        class="flex justify-between px-3 pt-4"
      >
        <div class="social-icons">
          <img src="@/assets/img/icon/like.svg" alt="" />
          <SharedIconFacebookLoveIcon />
          <img src="@/assets/img/icon/wow.svg" alt="" />
          <span class="reaction-conter">{{ article.reactionsCount }}</span>
        </div>
        <div class="flex space-x-3">
          <div
            class="comments-conter"
            :class="
              articles.type === 'Profile'
                ? 'cursor-default'
                : 'cursor-pointer hover:underline'
            "
            @click="
              ;[
                articles.type === 'Page' && article.commentsCount > 0
                  ? loadFeedComment()
                  : '',
              ]
            "
          >
            <span>{{ article.commentsCount }} </span>
            <span class="ml-1">Comments</span>
          </div>

          <div
            class="shares-counter"
            :class="
              articles.type === 'Profile' ? 'cursor-default' : 'cursor-pointer'
            "
          >
            <span>{{ article.sharesCount }} </span>
            <span class="ml-1">Shares</span>
          </div>
        </div>
      </div>
      <HomeRealtimeFeedRssFacebookComments
        v-if="feedComments"
        class="px-3"
        :comments="feedComments"
      ></HomeRealtimeFeedRssFacebookComments>
    </div>
    <!-- <facebook-message
      :card-height="cardHeight"
      :article="article"
    ></facebook-message> -->
  </div>
</template>

<script setup lang="ts">
import { useStore } from 'vuex'
import { useFeedComment } from '../../../../composables/feeds/useComment'
import {
  playVideos,
  pauseVideos,
} from '../../../../composables/feeds/playVideos'
import Pause from '~/assets/img/icon/pauseAsset_14.webp'
import Play from '~/assets/img/icon/playAsset_13.webp'
import Mute from '~/assets/img/icon/mic_offAsset_10.webp'
import Unmute from '~/assets/img/icon/micAsset_9.webp'

const props = defineProps({
  article: {
    type: Object,
    default: () => {},
  },
  articles: {
    type: Object,
    default: () => {},
  },
})

const store = useStore()
const { loadComment } = useFeedComment()
const { addLineBreak } = useLineBreak()
const feedComments = ref<any>(null)
const collapseable = ref<boolean>(false)
const loadFeedComment = async () => {
  if (collapseable.value) {
    feedComments.value = null
    collapseable.value = false
  } else {
    try {
      const { data } = await loadComment({
        provider: props.article.provider,
        id: props.article.id,
      })
      if (data.length) {
        feedComments.value = data
        collapseable.value = true
      }
    } catch (error) {
      console.log(error)
    }
  }
}
const pause = ref<string>(Pause)
const play = ref<string>(Play)
const mute = ref<string>(Mute)
const unmute = ref<string>(Unmute)

onMounted(() => {
  playVideos()
  setTimeout(() => {
    document
      .getElementById('desktop-body-wrapper')
      ?.addEventListener('scroll', pauseVideos)
  }, 1000)
  props.articles.items.forEach((item: any) => {
    const video = document.getElementById(`${item.id}-custom-video`)
    if (video) {
      video.addEventListener('ended', () => {
        togglePlayPause(item.id)
      })
      video.addEventListener('pause', () => {
        const videoPaused = document.getElementById(
          `${item.id}-play-pause`,
        ) as HTMLButtonElement | null
        if (videoPaused) {
          videoPaused.innerHTML = `<img class="w-4 h-4" src="${pause.value}"/>`
        }
      })
    }
  })
})
onUnmounted(() => {
  window.removeEventListener('scroll', pauseVideos)
})

const togglePlayPause = (id: number) => {
  document.querySelectorAll('.reel-play-pause').forEach((item) => {
    // console.log(!item.paused, 'hello')
    // if (!item.paused) {
    item.innerHTML = `<img class="w-4 h-4" src="${pause.value}"/>`
    // }
  })
  const video = document.getElementById(
    `${id}-custom-video`,
  ) as HTMLVideoElement | null
  const playPauseButton = document.getElementById(
    `${id}-play-pause`,
  ) as HTMLButtonElement | null
  // const muteUnmuteButton = document.getElementById('mute-unmute')
  if (video && playPauseButton) {
    video.controls = false
    if (video.paused) {
      video.play()
      playPauseButton.innerHTML = `<img class="w-4 h-4" src="${play.value}"/>`
    } else if (!video.paused || video.ended) {
      video.pause()
      playPauseButton.innerHTML = `<img class="w-4 h-4" src="${pause.value}"/>`
    }
  }
}

const toggleMuteUnmute = (id: number) => {
  const video = document.getElementById(
    `${id}-custom-video`,
  ) as HTMLVideoElement | null
  // const playPauseButton = document.getElementById('play-pause')
  const muteUnmuteButton = document.getElementById(
    `${id}-mute-unmute`,
  ) as HTMLButtonElement | null
  if (video && muteUnmuteButton) {
    video.controls = false
    if (video.muted) {
      video.muted = false
      muteUnmuteButton.innerHTML = `<img class="w-4 h-4" src="${unmute.value}"/>`
    } else {
      video.muted = true
      muteUnmuteButton.innerHTML = `<img class="w-4 h-4" src="${mute.value}"/>`
    }
  }
}
const showSinglePost = (singleItem: any) => {
  store.dispatch('socialFeed/singlePostShow', {
    singleItem,
    show: true,
  })
}
const showSingleImagePost = (singleItem: any, index: number) => {
  store.dispatch('socialFeed/singleImagePostShow', {
    singleItem,
    show: true,
    index,
  })
}
</script>

<style lang="scss" scoped>
.whole-card {
  box-shadow: 0px 1px 2px #22283126;
  border-radius: 10px;
}
.card {
  @apply bg-white my-0 w-full z-1;
  box-shadow: 0px 1px 2px #22283126;
  border-radius: 10px;
  // min-width: 400px;
  // max-width: 690px;
}

.profile-name {
  @apply text-xl;
  color: #222831;
}

.profile-image {
  @apply w-10.1 h-10.1 rounded-full;
}

.lineHeight-1 {
  line-height: 1px;
}

.profile-date {
  @apply text-xs text-gray-light;
}

.comment {
  @apply text-xl mt-4 mb-2;
  color: #222831;
}

.link {
  @apply pt-2;
  color: #1263cc;
}
.posted-image {
  @apply max-h-116;
}
.link-preview {
  @apply my-0 py-2 px-3 bg-gray-1400;
}

.link-preview p {
  @apply text-gray-light;
}

.social-icons {
  @apply flex items-center;
}

.social-icons > img {
  @apply inline-block -mr-0.5;
  height: 18px;
  width: 18px;
}

.reaction-conter {
  @apply text-base ml-1.5 text-gray-light;
}

.comments-conter {
  @apply inline-block text-gray-light;
}
.shares-counter {
  @apply inline-block text-gray-light;
}
button {
  @apply py-1 mb-2 px-2.5 rounded-md text-gray-1200;
  background: #e6e6e6;
}
.column-2 {
  flex: 48%;
  margin: 2px;
  overflow: hidden;
  text-align: center;
  clear: both;
  position: relative;
}

.column-3 {
  margin: 2px;
  flex: 32%;
  overflow: hidden;
  clear: both;
  text-align: center;
  position: relative;
}
.hover-border {
  border: 1px solid transparent;
}

.post-overlay {
  align-items: center;
  border-radius: inherit;
  display: flex;
  justify-content: center;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: auto;
  z-index: 150;
  color: #fff;
  transition:
    0.3s cubic-bezier(0.25, 0.8, 0.5, 1),
    z-index 1ms;
}
.event_bg_color {
  background-color: rgb(240, 242, 245);
}
.text_color {
  color: #050505;
}
.description_color {
  color: #65676b;
}
.born-icon {
  width: 48px;
  height: 48px;
  background-color: #1876f2;
  margin-bottom: 16px;
}
.video-container {
  position: relative;
  // max-width: 480px;
  width: 100%;
  margin: 0 auto;
}

/* Style for the video element */
video {
  width: 100%;
}

/* Style for the custom control buttons */
.custom-controls {
  position: absolute;
  // bottom: 10px;
  // left: 50%;
  // transform: translateX(-50%);
  top: 8px;
  right: 0px;
  background-color: transparent;
  border: none;
  border-radius: 5px;
  padding: 5px 10px;
}

/* Style for individual control buttons */
.custom-controls button {
  background: none;
  border: none;
  cursor: pointer;
  color: white;
  font-size: 18px;
  margin: 0 5px;
}
.reel {
  height: 638px !important;
}
.main-reel {
  background-color: #63635c;
  border-radius: 10px;
  text-shadow:
    2px 7px 5px rgba(0, 0, 0, 0.3),
    0px -4px 10px rgba(255, 255, 255, 0.3);
}
@media (max-width: 500px) {
  .custom-controls {
    top: 32px;
    right: 0px;
  }

  /* Style for individual control buttons */
  .custom-controls button {
    width: 20px;
    padding: 0 2px;
  }
}
</style>
