<template>
  <div ref="nested shadow" class="balance__menu relative font-serif">
    <div
      ref="dropdown"
      class="dropdown-btn bg-yellow-primary transition-border-radius duration-500 rounded-t-3xl outline-none"
      :class="[`${source}`, round ? 'rounded-3xl' : '']"
    >
      <button
        class="w-11/12 text-left border-none outline-none"
        @click="enableClick ? expand($event) : ''"
      >
        <span class="text-lg font-bold font-sans">Feeds</span>
      </button>
      <svg
        v-if="!menuOpen"
        width="15"
        height="15"
        class="fill-current text-gray-default transition-all duration-300 ease-in-out cursor-pointer"
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 292.362 292.362"
        @click="enableClick ? expand($event) : ''"
      >
        <path
          d="M286.935,69.377c-3.614-3.617-7.898-5.424-12.848-5.424H18.274c-4.952,0-9.233,1.807-12.85,5.424
    C1.807,72.998,0,77.279,0,82.228c0,4.948,1.807,9.229,5.424,12.847l127.907,127.907c3.621,3.617,7.902,5.428,12.85,5.428
    s9.233-1.811,12.847-5.428L286.935,95.074c3.613-3.617,5.427-7.898,5.427-12.847C292.362,77.279,290.548,72.998,286.935,69.377z"
        />
      </svg>
      <svg
        v-else
        width="15"
        height="15"
        class="fill-current text-gray-default transition-all duration-300 ease-in-out cursor-pointer z-100"
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 24 24"
        @click.stop="showAddFeedsComp"
      >
        <path
          fill-rule="evenodd"
          d="M24 10h-10v-10h-4v10h-10v4h10v10h4v-10h10z"
        />
      </svg>
    </div>
    <!-- Desktop -->
    <div
      class="hidden md:flex dropdown fixed flex-col rounded-b-3xl bg-yellow-primary"
      :style="{ '--height': getHeight() }"
      :class="[increaseHeight ? 'expand feedsShadow' : '', `${source}`]"
    >
      <div
        class="menu-wrapper flex-grow relative scroll overall_scroll"
        :class="[`scroll__${source}`]"
      >
        <div class="menu-content">
          <div class="menu-item list-wrapper">
            <div>
              <div
                class="list-item cursor-pointer flex border-b border-b-0 group-archive"
                :class="[`group-archive__${source}`]"
              >
                <template v-if="socialTop">
                  <HomeFeedsType
                    :visible="isSocialVisible"
                    :type="'Social Media'"
                    :selected-type="socialType"
                    :feeds-list="socialFeedsList"
                    :show-feeds="showFeeds"
                    :source="source"
                    @expand-type="expandSocial"
                    @selected-feed="selectedFeed($event)"
                    @selected-item="selectedItem($event)"
                  ></HomeFeedsType>
                  <HomeFeedsType
                    :visible="isEmailVisible"
                    :type="'Email'"
                    :selected-type="emailType"
                    :feeds-list="emailFeedsList"
                    :show-feeds="showFeeds"
                    :source="source"
                    @expand-type="expandEmail"
                    @selected-feed="selectedFeed($event)"
                    @selected-item="selectedItem($event)"
                  >
                  </HomeFeedsType>
                  <HomeFeedsType
                    :visible="isWebVisible"
                    :type="'Website'"
                    :selected-type="webType"
                    :feeds-list="webFeedsList"
                    :show-feeds="showFeeds"
                    :source="source"
                    @expand-type="expandWeb"
                    @selected-feed="selectedFeed($event)"
                    @selected-item="selectedItem($event)"
                  >
                  </HomeFeedsType>
                </template>
                <template v-if="emailTop">
                  <HomeFeedsType
                    :visible="isEmailVisible"
                    :type="'Email'"
                    :selected-type="emailType"
                    :feeds-list="emailFeedsList"
                    :show-feeds="showFeeds"
                    :source="source"
                    @expand-type="expandEmail"
                    @selected-feed="selectedFeed($event)"
                    @selected-item="selectedItem($event)"
                  >
                  </HomeFeedsType>
                  <HomeFeedsType
                    :visible="isSocialVisible"
                    :type="'Social Media'"
                    :selected-type="socialType"
                    :feeds-list="socialFeedsList"
                    :show-feeds="showFeeds"
                    :source="source"
                    @expand-type="expandSocial"
                    @selected-feed="selectedFeed($event)"
                    @selected-item="selectedItem($event)"
                  ></HomeFeedsType>
                  <HomeFeedsType
                    :visible="isWebVisible"
                    :type="'Website'"
                    :selected-type="webType"
                    :feeds-list="webFeedsList"
                    :show-feeds="showFeeds"
                    :source="source"
                    @expand-type="expandWeb"
                    @selected-feed="selectedFeed($event)"
                    @selected-item="selectedItem($event)"
                  >
                  </HomeFeedsType>
                </template>
                <template v-if="webTop">
                  <HomeFeedsType
                    :visible="isWebVisible"
                    :type="'Website'"
                    :selected-type="webType"
                    :feeds-list="webFeedsList"
                    :show-feeds="showFeeds"
                    :source="source"
                    @expand-type="expandWeb"
                    @selected-feed="selectedFeed($event)"
                    @selected-item="selectedItem($event)"
                  >
                  </HomeFeedsType>
                  <HomeFeedsType
                    :visible="isSocialVisible"
                    :type="'Social Media'"
                    :selected-type="socialType"
                    :feeds-list="socialFeedsList"
                    :show-feeds="showFeeds"
                    :source="source"
                    @expand-type="expandSocial"
                    @selected-feed="selectedFeed($event)"
                    @selected-item="selectedItem($event)"
                  ></HomeFeedsType>
                  <HomeFeedsType
                    :visible="isEmailVisible"
                    :type="'Email'"
                    :selected-type="emailType"
                    :feeds-list="emailFeedsList"
                    :show-feeds="showFeeds"
                    :source="source"
                    @expand-type="expandEmail"
                    @selected-feed="selectedFeed($event)"
                    @selected-item="selectedItem($event)"
                  >
                  </HomeFeedsType>
                </template>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        v-if="
          (menuOpen && !socialType && !emailType) ||
          (socialType && socialTotalPages === 1) ||
          (emailType && socialTotalPages === 1)
        "
        class="equity cursor-pointer sticky bottom-0 left-0"
        :class="[`${source}`]"
        @click="showEditFeed(), hideMobileHaeder()"
      >
        <span class="text-lg text-bold font-sans">Edit Feeds</span>
      </div>
      <div
        v-if="
          menuOpen &&
          ((socialType && socialTotalPages > 1) ||
            (emailType && socialTotalPages > 1))
        "
        class="equity sticky bottom-0 left-0 flex items-center justify-between rounded-b-3xl"
        :class="[`${source}`]"
      >
        <div class="flex items-center space-x-5">
          <button
            v-if="socialType || emailType"
            :disabled="isSocialFirstPage"
            class="flex justify-center items-center w-8 h-8 rounded-full"
            :class="
              isSocialFirstPage
                ? 'bg-white-opasity-50 cursor-default'
                : 'bg-white cursor-pointer'
            "
            @click="clickPreviousPage(false)"
          >
            <ClientOnly>
              <fa
                :icon="['fas', 'chevron-left']"
                class="feeds-button"
                :class="[`feeds-button__${source}`]"
              />
            </ClientOnly>
          </button>
          <span
            v-if="socialType || emailType"
            class="text-lg font-sans text-normal"
            >{{ socialCurrentPage }}/{{ socialTotalPages }}</span
          >
          <button
            v-if="socialType || emailType"
            :disabled="isSocialLastPage"
            class="flex justify-center items-center w-8 h-8 rounded-full"
            :class="
              isSocialLastPage
                ? 'bg-white-opasity-50  cursor-default'
                : 'bg-white cursor-pointer'
            "
            @click="clickNextPage(false)"
          >
            <ClientOnly>
              <fa
                :icon="['fas', 'chevron-right']"
                class="feeds-button"
                :class="[`feeds-button__${source}`]"
              />
            </ClientOnly>
          </button>
        </div>
        <span
          class="cursor-pointer"
          @click="showEditFeed(), hideMobileHaeder()"
        >
          <ClientOnly>
            <fa :icon="['fas', 'pencil-alt']" />
          </ClientOnly>
        </span>
      </div>
    </div>
    <!-- Mobile -->
    <div
      class="md:hidden dropdown flex flex-col rounded-b-3xl bg-yellow-primary"
      :style="[
        { '--maxHeight': `${windowHeight - 130}px` },
        { '--height': getHeight() },
      ]"
      :class="[increaseHeight ? 'expand' : '', `${source}`]"
    >
      <div
        class="menu-wrapper flex-grow relative scroll overall_scroll"
        :class="[`scroll__${source}`]"
      >
        <div class="menu-content">
          <div class="menu-item list-wrapper">
            <div>
              <div
                class="list-item cursor-pointer flex border-b border-b-0 group-archive"
                :class="[`group-archive__${source}`]"
              >
                <template v-if="socialTop">
                  <HomeFeedsType
                    :visible="isSocialVisible"
                    :type="'Social Media'"
                    :selected-type="socialType"
                    :feeds-list="socialFeedsList"
                    :show-feeds="showFeeds"
                    :source="source"
                    @expand-type="expandSocial"
                    @selected-feed="selectedFeed($event)"
                    @selected-item="selectedItem($event)"
                  ></HomeFeedsType>
                  <HomeFeedsType
                    :visible="isEmailVisible"
                    :type="'Email'"
                    :selected-type="emailType"
                    :feeds-list="emailFeedsList"
                    :show-feeds="showFeeds"
                    :source="source"
                    @expand-type="expandEmail"
                    @selected-feed="selectedFeed($event)"
                    @selected-item="selectedItem($event)"
                  >
                  </HomeFeedsType>
                  <HomeFeedsType
                    :visible="isWebVisible"
                    :type="'Website'"
                    :selected-type="webType"
                    :feeds-list="webFeedsList"
                    :show-feeds="showFeeds"
                    :source="source"
                    @expand-type="expandWeb"
                    @selected-feed="selectedFeed($event)"
                    @selected-item="selectedItem($event)"
                  >
                  </HomeFeedsType>
                </template>
                <template v-if="emailTop">
                  <HomeFeedsType
                    :visible="isEmailVisible"
                    :type="'Email'"
                    :selected-type="emailType"
                    :feeds-list="emailFeedsList"
                    :show-feeds="showFeeds"
                    :source="source"
                    @expand-type="expandEmail"
                    @selected-feed="selectedFeed($event)"
                    @selected-item="selectedItem($event)"
                  >
                  </HomeFeedsType>
                  <HomeFeedsType
                    :visible="isSocialVisible"
                    :type="'Social Media'"
                    :selected-type="socialType"
                    :feeds-list="socialFeedsList"
                    :show-feeds="showFeeds"
                    :source="source"
                    @expand-type="expandSocial"
                    @selected-feed="selectedFeed($event)"
                    @selected-item="selectedItem($event)"
                  ></HomeFeedsType>
                  <HomeFeedsType
                    :visible="isWebVisible"
                    :type="'Website'"
                    :selected-type="webType"
                    :feeds-list="webFeedsList"
                    :show-feeds="showFeeds"
                    :source="source"
                    @expand-type="expandWeb"
                    @selected-feed="selectedFeed($event)"
                    @selected-item="selectedItem($event)"
                  >
                  </HomeFeedsType>
                </template>
                <template v-if="webTop">
                  <HomeFeedsType
                    :visible="isWebVisible"
                    :type="'Website'"
                    :selected-type="webType"
                    :feeds-list="webFeedsList"
                    :show-feeds="showFeeds"
                    :source="source"
                    @expand-type="expandWeb"
                    @selected-feed="selectedFeed($event)"
                    @selected-item="selectedItem($event)"
                  >
                  </HomeFeedsType>
                  <HomeFeedsType
                    :visible="isSocialVisible"
                    :type="'Social Media'"
                    :selected-type="socialType"
                    :feeds-list="socialFeedsList"
                    :show-feeds="showFeeds"
                    :source="source"
                    @expand-type="expandSocial"
                    @selected-feed="selectedFeed($event)"
                    @selected-item="selectedItem($event)"
                  ></HomeFeedsType>
                  <HomeFeedsType
                    :visible="isEmailVisible"
                    :type="'Email'"
                    :selected-type="emailType"
                    :feeds-list="emailFeedsList"
                    :show-feeds="showFeeds"
                    :source="source"
                    @expand-type="expandEmail"
                    @selected-feed="selectedFeed($event)"
                    @selected-item="selectedItem($event)"
                  >
                  </HomeFeedsType>
                </template>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        v-if="
          (menuOpen && !socialType && !emailType) ||
          (socialType && socialTotalPages === 1) ||
          (emailType && socialTotalPages === 1)
        "
        class="equity cursor-pointer sticky bottom-0 left-0"
        :class="[`${source}`]"
        @click="showEditFeed(), hideMobileHaeder()"
      >
        <span class="text-lg text-bold font-sans">Edit Feeds</span>
      </div>
      <div
        v-if="
          menuOpen &&
          ((socialType && socialTotalPages > 1) ||
            (emailType && socialTotalPages > 1))
        "
        class="equity sticky bottom-0 left-0 flex items-center justify-between rounded-b-3xl"
        :class="[`${source}`]"
      >
        <div class="flex items-center space-x-5">
          <button
            v-if="socialType || emailType"
            :disabled="isSocialFirstPage"
            class="flex justify-center items-center w-8 h-8 rounded-full"
            :class="
              isSocialFirstPage
                ? 'bg-white-opasity-50 cursor-default'
                : 'bg-white cursor-pointer'
            "
            @click="clickPreviousPage(false)"
          >
            <ClientOnly>
              <fa
                :icon="['fas', 'chevron-left']"
                class="feeds-button"
                :class="[`feeds-button__${source}`]"
              />
            </ClientOnly>
          </button>
          <span
            v-if="socialType || emailType"
            class="text-lg font-sans text-normal"
            >{{ socialCurrentPage }}/{{ socialTotalPages }}</span
          >
          <button
            v-if="socialType || emailType"
            :disabled="isSocialLastPage"
            class="flex justify-center items-center w-8 h-8 rounded-full"
            :class="
              isSocialLastPage
                ? 'bg-white-opasity-50  cursor-default'
                : 'bg-white cursor-pointer'
            "
            @click="clickNextPage(false)"
          >
            <ClientOnly>
              <fa
                :icon="['fas', 'chevron-right']"
                class="feeds-button"
                :class="[`feeds-button__${source}`]"
              />
            </ClientOnly>
          </button>
        </div>
        <span
          class="cursor-pointer"
          @click="showEditFeed(), hideMobileHaeder()"
        >
          <ClientOnly>
            <fa :icon="['fas', 'pencil-alt']" />
          </ClientOnly>
        </span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useStore } from 'vuex'
import { breakpointsTailwind, useBreakpoints } from '@vueuse/core'

const emit = defineEmits([
  'define-height',
  'clear-all-date-range',
  'expand',
  'collapse-header',
  'change-feed',
])
const props = defineProps({
  source: {
    type: String,
    required: false,
    default: '',
  },
  windowHeight: {
    type: Number,
    default: 0,
  },
})

const feedBodyHeight = ref(0)
const breakpoints = useBreakpoints(breakpointsTailwind)
const nuxtApp = useNuxtApp()
const isDesktop = breakpoints.greaterOrEqual('md')

const menuOpen = ref(false)
const increaseHeight = ref(false)
const toggle = ref(false)
const scroll = ref(false)
const round = ref(true)
const progress = ref(false)
const socialType = ref(true)
const emailType = ref(true)
const webType = ref(true)
const socialFeedsList = ref([])
const emailFeedsList = ref([])
const webFeedsList = ref([])
const menuHeight = ref(false)
const showFeeds = ref(true)
const social = ref([])
const email = ref([])
const socialTop = ref(false)
const emailTop = ref(false)
const webTop = ref(false)
const socialStatus = ref(false)
const emailStatus = ref(false)
const webStatus = ref(false)
const oldScrollY = ref(0)
const windowWidth = ref('')

// Access Vuex store
const store = useStore()
const router = useRouter()

// Destructure state using `mapState` functionality
const socialFeeds = computed(() => store.state.socialFeed.socialFeeds)
const showSinglePost = computed(() => store.state.socialFeed.showSinglePost)
const showSingleImagePost = computed(
  () => store.state.socialFeed.showSingleImagePost,
)

const socialInitialItem = computed(
  () => store.state.feedsDropdown.socialInitialItem,
)
const socialLoadCount = computed(
  () => store.state.feedsDropdown.socialLoadCount,
)
const socialPerPage = computed(() => store.state.feedsDropdown.socialPerPage)
const socialCurrentPage = computed(
  () => store.state.feedsDropdown.socialCurrentPage,
)
const emailCurrentPage = computed(
  () => store.state.feedsDropdown.emailCurrentPage,
)
const socialTotalPages = computed(
  () => store.state.feedsDropdown.socialTotalPages,
)
const emailTotalPages = computed(
  () => store.state.feedsDropdown.emailTotalPages,
)
const oldScrollTop = computed(() => store.state.feedsDropdown.oldScrollTop)

const socialPage = computed({
  get() {
    social.value = []
    for (const item of socialFeedsList.value.slice(
      socialInitialItem.value,
      socialLoadCount.value,
    )) {
      social.value.push(item)
    }
    return social.value
  },
  set(newValue) {
    social.value = []
    social.value = newValue
  },
})

const isSocialFirstPage = computed(() => socialCurrentPage.value === 1)
const isSocialLastPage = computed(
  () => socialCurrentPage.value === socialTotalPages.value,
)
const enableClick = computed(() => socialFeeds.value.length > 0)
const isSocialVisible = computed(() => socialFeedsList.value.length > 0) // Adjust based on `socialFeedsList` logic
const isEmailVisible = computed(() => emailFeedsList.value.length > 0)
const isWebVisible = computed(() => webFeedsList.value.length > 0)

watch(
  () => socialFeeds.value,
  (data) => {
    if (data) {
      resizeWindow()
      setTotalPages()
    }
  },
)
watch(
  () => windowWidth.value,
  (data) => {
    setTotalPages()
  },
)
nuxtApp.$bus.$on('expand', () => {
  finalCollapsed()
})

onMounted(() => {
  windowWidth.value = window.innerWidth
  window.addEventListener('resize', resizeWindow)
  window.addEventListener('resize', getWindowWindth)
  if (socialFeeds.value && socialFeeds.value.length > 0) {
    resizeWindow()
  }
})

onUnmounted(() => {
  setTimeout(() => {
    window.removeEventListener('resize', resizeWindow)
    window.removeEventListener('resize', getWindowWindth)
  }, 2000)
})

const getWindowWindth = () => {
  windowWidth.value = window.innerWidth
}
const getHeight = () => {
  let totalType = 0
  if (
    socialFeedsList.value.length > 0 &&
    emailFeedsList.value.length > 0 &&
    webFeedsList.value.length > 0
  ) {
    totalType = 3
  } else if (
    (socialFeedsList.value.length > 0 && emailFeedsList.value.length > 0) ||
    (socialFeedsList.value.length > 0 && webFeedsList.value.length > 0) ||
    (webFeedsList.value.length > 0 && emailFeedsList.value.length > 0)
  ) {
    totalType = 2
  } else if (
    socialFeedsList.value.length > 0 ||
    emailFeedsList.value.length > 0 ||
    webFeedsList.value.length > 0
  ) {
    totalType = 1
  } else {
    totalType = 0
  }
  if (socialTotalPages.value === 1) {
    if (
      menuOpen.value &&
      socialType.value &&
      emailType.value &&
      webType.value
    ) {
      return `${
        socialFeedsList.value.length * 42 +
        emailFeedsList.value.length * 42 +
        webFeedsList.value.length * 42 +
        totalType * 42 +
        40
      }px`
    } else if (
      menuOpen.value &&
      socialType.value &&
      emailType.value &&
      !webType.value
    ) {
      return `${
        socialFeedsList.value.length * 42 +
        emailFeedsList.value.length * 42 +
        totalType * 42 +
        40
      }px`
    } else if (
      menuOpen.value &&
      socialType.value &&
      !emailType.value &&
      webType.value
    ) {
      return `${
        socialFeedsList.value.length * 42 +
        webFeedsList.value.length * 42 +
        totalType * 42 +
        40
      }px`
    } else if (
      menuOpen.value &&
      !socialType.value &&
      emailType.value &&
      webType.value
    ) {
      return `${
        emailFeedsList.value.length * 42 +
        webFeedsList.value.length * 42 +
        totalType * 42 +
        40
      }px`
    } else if (
      menuOpen.value &&
      socialType.value &&
      !emailType.value &&
      !webType.value
    ) {
      return `${socialFeedsList.value.length * 42 + totalType * 42 + 40}px`
    } else if (
      menuOpen.value &&
      !socialType.value &&
      emailType.value &&
      !webType.value
    ) {
      return `${emailFeedsList.value.length * 42 + totalType * 42 + 40}px`
    } else if (
      menuOpen.value &&
      !socialType.value &&
      !emailType.value &&
      webType.value
    ) {
      return `${webFeedsList.value.length * 42 + totalType * 42 + 40}px`
    } else if (
      menuOpen.value &&
      !socialType.value &&
      !emailType.value &&
      !webType.value
    ) {
      return `${totalType * 42 + 40}px`
    }
  } else if (socialTotalPages.value > 1) {
    if (
      menuOpen.value &&
      socialType.value &&
      emailType.value &&
      webType.value
    ) {
      return `${socialPerPage.value * 42 + totalType * 42 + 40}px`
    } else if (
      menuOpen.value &&
      socialType.value &&
      emailType.value &&
      !webType.value
    ) {
      return `${socialPerPage.value * 42 + totalType * 42 + 40}px`
    } else if (
      menuOpen.value &&
      socialType.value &&
      !emailType.value &&
      webType.value
    ) {
      return `${socialPerPage.value * 42 + totalType * 42 + 40}px`
    } else if (
      menuOpen.value &&
      !socialType.value &&
      emailType.value &&
      webType.value
    ) {
      return `${socialPerPage.value * 42 + totalType * 42 + 40}px`
    } else if (
      menuOpen.value &&
      socialType.value &&
      !emailType.value &&
      !webType.value
    ) {
      return `${socialPerPage.value * 42 + totalType * 42 + 40}px`
    } else if (
      menuOpen.value &&
      !socialType.value &&
      emailType.value &&
      !webType.value
    ) {
      return `${socialPerPage.value * 42 + totalType * 42 + 40}px`
    } else if (
      menuOpen.value &&
      !socialType.value &&
      !emailType.value &&
      webType.value
    ) {
      return `${socialPerPage.value * 42 + totalType * 42 + 40}px`
    } else if (
      menuOpen.value &&
      !socialType.value &&
      !emailType.value &&
      !webType.value
    ) {
      return `${totalType * 42 + 40}px`
    }
  }
  if (menuOpen.value && menuHeight.value) {
    return `${totalType * 42 + 40}px`
  } else {
    return ''
  }
}
const handleScroll = () => {
  const allItem = document.querySelectorAll('.overall_scroll')
  allItem.forEach((item) => {
    if (item.offsetHeight !== 0) {
      if (oldScrollY.value < item.scrollTop) {
        if (
          item.scrollTop >= oldScrollTop.value + socialPerPage.value * 42 &&
          socialTotalPages.value - socialCurrentPage.value !== 1
        ) {
          store.commit(
            'feedsDropdown/SET_OLD_SCROLL_TOP',
            oldScrollTop.value + socialPerPage.value * 42,
          )
          clickNextPage(true)
        } else if (
          item.scrollTop + item.clientHeight === item.scrollHeight &&
          socialTotalPages.value !== socialCurrentPage.value
        ) {
          const lastPageTotalItem =
            (socialFeedsList.value.length +
              emailFeedsList.value.length +
              webFeedsList.value.length +
              3 -
              (socialTotalPages.value - 1) * socialPerPage.value) *
            42
          store.commit(
            'feedsDropdown/SET_OLD_SCROLL_TOP',
            oldScrollTop.value + lastPageTotalItem,
          )
          clickNextPage(true)
        }
      } else if (!(oldScrollY.value < item.scrollTop)) {
        if (socialTotalPages.value === socialCurrentPage.value) {
          const lastPageTotalItem =
            (socialFeedsList.value.length +
              emailFeedsList.value.length +
              webFeedsList.value.length +
              3 -
              (socialTotalPages.value - 1) * socialPerPage.value) *
            42
          if (item.scrollTop <= oldScrollTop.value - lastPageTotalItem) {
            store.commit(
              'feedsDropdown/SET_OLD_SCROLL_TOP',
              oldScrollTop.value - lastPageTotalItem,
            )
            clickPreviousPage(true)
          }
        } else if (
          socialTotalPages.value !== socialCurrentPage.value &&
          socialCurrentPage.value !== 1
        ) {
          if (item.scrollTop === 0) {
            store.commit(
              'feedsDropdown/SET_OLD_SCROLL_TOP',
              oldScrollTop.value - socialPerPage.value * 42,
            )
            clickPreviousPage(true)
          } else if (
            item.scrollTop <=
            oldScrollTop.value - socialPerPage.value * 42
          ) {
            store.commit(
              'feedsDropdown/SET_OLD_SCROLL_TOP',
              oldScrollTop.value - socialPerPage.value * 42,
            )
            clickPreviousPage(true)
          }
        }
      }
      oldScrollY.value = item.scrollTop <= 0 ? 0 : item.scrollTop
    }
  })
}
const activeFeed = () => {
  socialTop.value = false
  emailTop.value = false
  webTop.value = false
  socialStatus.value = false
  emailStatus.value = false
  webStatus.value = false
  if (
    (socialFeedsList.value && socialFeedsList.value.length > 0) ||
    (emailFeedsList.value && emailFeedsList.value.length > 0) ||
    (webFeedsList.value && webFeedsList.value.length > 0)
  ) {
    if (
      socialFeeds.value[0].provider !== 'Microsoft' &&
      socialFeeds.value[0].provider !== 'Google' &&
      socialFeeds.value[0].provider !== 'Web'
    ) {
      socialType.value = true
      emailType.value = true
      webType.value = true
      socialTop.value = true
    } else if (
      socialFeeds.value[0].provider === 'Microsoft' ||
      socialFeeds.value[0].provider === 'Google'
    ) {
      emailType.value = true
      socialType.value = true
      webType.value = true
      emailTop.value = true
    } else if (socialFeeds.value[0].provider === 'Web') {
      emailType.value = true
      socialType.value = true
      webType.value = true
      webTop.value = true
    }
  }
}
const selectedItem = (item) => {
  socialFeedsList.value.forEach((element) => {
    element.backgroundColor = false
  })
  emailFeedsList.value.forEach((element) => {
    element.backgroundColor = false
  })
  webFeedsList.value.forEach((element) => {
    element.backgroundColor = false
  })
  if (socialType.value || emailType.value || webType.value) {
    socialFeedsList.value.forEach((element, i) => {
      if (element.id === item) {
        element.backgroundColor = true
      } else {
        element.backgroundColor = false
      }
      socialFeedsList.value[i] = element
      // set(socialFeedsList, i, socialFeedsList[i])
    })
    emailFeedsList.value.forEach((element, i) => {
      if (element.id === item) {
        element.backgroundColor = true
      } else {
        element.backgroundColor = false
      }
      emailFeedsList.value[i] = element
      // set(emailFeedsList, i, emailFeedsList[i])
    })
    webFeedsList.value.forEach((element, i) => {
      if (element.id === item) {
        element.backgroundColor = true
      } else {
        element.backgroundColor = false
      }
      webFeedsList.value[i] = element
      // set(webFeedsList, i, webFeedsList[i])
    })
    socialPage.value = socialFeedsList.value
  }
}
const resizeWindow = () => {
  if (isDesktop.value) {
    feedBodyHeight.value = window.innerHeight - (60 + 16 + 16)
  } else {
    feedBodyHeight.value = props.windowHeight - 84
  }
  if (feedBodyHeight.value >= 202) {
    store.commit(
      'feedsDropdown/SET_SOCIAL_PER_PAGE',
      Math.floor((feedBodyHeight.value - 40 * 2) / 42),
    )
  } else {
    store.commit('feedsDropdown/SET_SOCIAL_PER_PAGE', 1)
    store.commit('feedsDropdown/SET_EMAIL_PER_PAGE', 1)
  }
  if (socialFeeds.value && socialFeeds.value.length > 0) {
    groupFeedByType()
  }
}
const setTotalPages = () => {
  if (increaseHeight.value) {
    setTimeout(() => {
      collapsed()
    }, 100)
  }
  store.commit('feedsDropdown/SET_SOCIAL_LOAD_COUNT', socialPerPage.value)
  store.commit('feedsDropdown/SET_SOCIAL_CURRENT_PAGE', 1)
  store.commit(
    'feedsDropdown/SET_SOCIAL_SELECT_ITEM_PAGE',
    socialCurrentPage.value,
  )
  store.commit('feedsDropdown/SET_SOCIAL_INITIAL_ITEM', 0)
  store.commit(
    'feedsDropdown/SET_SOCIAL_CURRENT_INITIAL_ITEM',
    socialInitialItem.value,
  )
  store.commit(
    'feedsDropdown/SET_SOCIAL_CURRENT_LOAD_COUNT',
    socialLoadCount.value,
  )
  store.commit(
    'feedsDropdown/SET_SOCIAL_TOTAL_PAGES',
    Math.ceil(
      (socialFeedsList.value.length +
        emailFeedsList.value.length +
        webFeedsList.value.length +
        3) /
        socialPerPage.value,
    ),
  )
}
const setSocialEmailTotalPages = () => {
  document.querySelectorAll('.overall_scroll').forEach((item) => {
    item.removeEventListener('scroll', handleScroll)
    item.scrollTop = 0
  })
  store.commit('feedsDropdown/SET_SOCIAL_CURRENT_PAGE', 1)
  store.commit('feedsDropdown/SET_OLD_SCROLL_TOP', 0)
  if (!emailType.value && socialType.value && webType.value) {
    store.commit(
      'feedsDropdown/SET_SOCIAL_TOTAL_PAGES',
      Math.ceil(
        (socialFeedsList.value.length + webFeedsList.value.length + 3) /
          socialPerPage.value,
      ),
    )
  } else if (!socialType.value && emailType.value && webType.value) {
    store.commit(
      'feedsDropdown/SET_SOCIAL_TOTAL_PAGES',
      Math.ceil(
        (emailFeedsList.value.length + webFeedsList.value.length + 3) /
          socialPerPage.value,
      ),
    )
  } else if (!webType.value && socialType.value && emailType.value) {
    store.commit(
      'feedsDropdown/SET_SOCIAL_TOTAL_PAGES',
      Math.ceil(
        (socialFeedsList.value.length + emailFeedsList.value.length + 3) /
          socialPerPage.value,
      ),
    )
  } else if (!emailType.value && !socialType.value && webType.value) {
    store.commit(
      'feedsDropdown/SET_SOCIAL_TOTAL_PAGES',
      Math.ceil((webFeedsList.value.length + 2) / socialPerPage.value),
    )
  } else if (!webType.value && !socialType.value && emailType.value) {
    store.commit(
      'feedsDropdown/SET_SOCIAL_TOTAL_PAGES',
      Math.ceil((emailFeedsList.value.length + 2) / socialPerPage.value),
    )
  } else if (!emailType.value && !webType.value && socialType.value) {
    store.commit(
      'feedsDropdown/SET_SOCIAL_TOTAL_PAGES',
      Math.ceil((socialFeedsList.value.length + 2) / socialPerPage.value),
    )
  } else if (!socialType.value && !emailType.value && !webType.value) {
    store.commit(
      'feedsDropdown/SET_SOCIAL_TOTAL_PAGES',
      Math.ceil(2 / socialPerPage.value),
    )
  } else if (emailType.value && socialType.value && webType.value) {
    store.commit(
      'feedsDropdown/SET_SOCIAL_TOTAL_PAGES',
      Math.ceil(
        (socialFeedsList.value.length +
          emailFeedsList.value.length +
          webFeedsList.value.length +
          2) /
          socialPerPage.value,
      ),
    )
  }
  setTimeout(() => {
    document.querySelectorAll('.overall_scroll').forEach((item) => {
      item.addEventListener('scroll', handleScroll)
    })
  }, 500)
}
const clickPreviousPage = (value) => {
  if (socialCurrentPage.value > 0) {
    if (socialType.value || emailType.value) {
      if (!value) {
        const allItem = document.querySelectorAll('.overall_scroll')
        allItem.forEach((item) => {
          item.removeEventListener('scroll', handleScroll)
          if (
            socialTotalPages.value !== socialCurrentPage.value &&
            item.offsetHeight !== 0
          ) {
            item.scrollTop = oldScrollTop.value - socialPerPage.value * 42
            if (item.offsetHeight !== 0) {
              store.commit(
                'feedsDropdown/SET_OLD_SCROLL_TOP',
                oldScrollTop.value - socialPerPage.value * 42,
              )
            }
          } else if (
            socialTotalPages.value === socialCurrentPage.value &&
            item.offsetHeight !== 0
          ) {
            const lastPageTotalItem =
              (socialFeedsList.value.length +
                emailFeedsList.value.length +
                webFeedsList.value.length +
                3 -
                (socialTotalPages.value - 1) * socialPerPage.value) *
              42
            item.scrollTop = oldScrollTop.value - lastPageTotalItem
            if (item.offsetHeight !== 0) {
              store.commit(
                'feedsDropdown/SET_OLD_SCROLL_TOP',
                oldScrollTop.value - lastPageTotalItem,
              )
            }
          }
        })
      }
      store.commit('feedsDropdown/SET_SOCIAL_PREVIOUS_PAGE', 1)
      store.commit(
        'feedsDropdown/SET_SOCIAL_LOAD_COUNT',
        socialInitialItem.value,
      )
      store.commit(
        'feedsDropdown/SET_SET_SOCIAL_INITIAL_ITEM_PREVIOUS',
        socialPerPage.value,
      )
    }
  }
  setTimeout(() => {
    const allItem = document.querySelectorAll('.overall_scroll')
    allItem.forEach((item) => {
      item.addEventListener('scroll', handleScroll)
    })
  }, 500)
}
const clickNextPage = (value) => {
  if (socialCurrentPage.value < socialTotalPages.value) {
    if (socialType.value || emailType.value || webType.value) {
      if (!value) {
        const allItem = document.querySelectorAll('.overall_scroll')
        allItem.forEach((item) => {
          item.removeEventListener('scroll', handleScroll)
          if (
            socialTotalPages.value - socialCurrentPage.value !== 1 &&
            item.offsetHeight !== 0
          ) {
            item.scrollTop = oldScrollTop.value + socialPerPage.value * 42
            if (item.offsetHeight !== 0) {
              store.commit(
                'feedsDropdown/SET_OLD_SCROLL_TOP',
                oldScrollTop.value + socialPerPage.value * 42,
              )
            }
          } else if (
            socialTotalPages.value - socialCurrentPage.value === 1 &&
            item.offsetHeight !== 0
          ) {
            const lastPageTotalItem =
              (socialFeedsList.value.length +
                emailFeedsList.value.length +
                webFeedsList.value.length +
                3 -
                (socialTotalPages.value - 1) * socialPerPage.value) *
              42
            item.scrollTop = oldScrollTop.value + lastPageTotalItem
            if (item.offsetHeight !== 0) {
              store.commit(
                'feedsDropdown/SET_OLD_SCROLL_TOP',
                oldScrollTop.value + lastPageTotalItem,
              )
            }
          }
        })
      }
      store.commit('feedsDropdown/SET_SOCIAL_NEXT_PAGE', 1)
      store.commit(
        'feedsDropdown/SET_SOCIAL_INITIAL_ITEM',
        socialLoadCount.value,
      )
      store.commit(
        'feedsDropdown/SET_SOCIAL_LOAD_COUNT_NEXT',
        socialPerPage.value,
      )
    }
  }
  setTimeout(() => {
    const allItem = document.querySelectorAll('.overall_scroll')
    allItem.forEach((item) => {
      item.addEventListener('scroll', handleScroll)
    })
  }, 500)
}
const groupFeedByType = () => {
  socialFeedsList.value = []
  emailFeedsList.value = []
  webFeedsList.value = []
  for (const item of socialFeeds.value) {
    if (item.provider === 'Microsoft' || item.provider === 'Google') {
      emailFeedsList.value.push(item)
    } else if (item.provider === 'Web') {
      webFeedsList.value.push(item)
    } else {
      socialFeedsList.value.push(item)
    }
  }
  activeFeed()
}
const expandSocial = () => {
  let totalType = 0
  if (
    socialFeedsList.value.length > 0 &&
    emailFeedsList.value.length > 0 &&
    webFeedsList.value.length > 0
  ) {
    totalType = 3
  } else if (
    (socialFeedsList.value.length > 0 && emailFeedsList.value.length > 0) ||
    (socialFeedsList.value.length > 0 && webFeedsList.value.length > 0) ||
    (webFeedsList.value.length > 0 && emailFeedsList.value.length > 0)
  ) {
    totalType = 2
  } else if (
    socialFeedsList.value.length > 0 ||
    emailFeedsList.value.length > 0 ||
    webFeedsList.value.length > 0
  ) {
    totalType = 1
  } else {
    totalType = 0
  }
  if (!socialType.value) {
    if (emailType.value && webType.value) {
      emit(
        'define-height',
        socialFeedsList.value.length * 42 +
          emailFeedsList.value.length * 42 +
          webFeedsList.value.length * 42 +
          totalType * 42 +
          40,
      )
    } else if (emailType.value && !webType.value) {
      emit(
        'define-height',
        socialFeedsList.value.length * 42 +
          emailFeedsList.value.length * 42 +
          totalType * 42 +
          40,
      )
    } else if (!emailType.value && webType.value) {
      emit(
        'define-height',
        socialFeedsList.value.length * 42 +
          webFeedsList.value.length * 42 +
          totalType * 42 +
          40,
      )
    } else {
      emit(
        'define-height',
        socialFeedsList.value.length * 42 + totalType * 42 + 40,
      )
    }
    setTimeout(() => {
      socialType.value = true
      setSocialEmailTotalPages()
      menuHeight.value = false
    }, 300)
  } else if (socialType.value) {
    socialType.value = false
    setSocialEmailTotalPages()
    setTimeout(() => {
      if (emailType.value && webType.value) {
        emit(
          'define-height',
          emailFeedsList.value.length * 42 +
            webFeedsList.value.length * 42 +
            totalType * 42 +
            40,
        )
      } else if (emailType.value && !webType.value) {
        emit(
          'define-height',
          emailFeedsList.value.length * 42 + totalType * 42 + 40,
        )
      } else if (!emailType.value && webType.value) {
        emit(
          'define-height',
          webFeedsList.value.length * 42 + totalType * 42 + 40,
        )
      } else {
        emit('define-height', totalType * 42 + 40)
      }
    }, 300)
    if (!socialType.value && !emailType.value && !webType.value) {
      menuHeight.value = true
    }
  }
}
const expandEmail = () => {
  let totalType = 0
  if (
    socialFeedsList.value.length > 0 &&
    emailFeedsList.value.length > 0 &&
    webFeedsList.value.length > 0
  ) {
    totalType = 3
  } else if (
    (socialFeedsList.value.length > 0 && emailFeedsList.value.length > 0) ||
    (socialFeedsList.value.length > 0 && webFeedsList.value.length > 0) ||
    (webFeedsList.value.length > 0 && emailFeedsList.value.length > 0)
  ) {
    totalType = 2
  } else if (
    socialFeedsList.value.length > 0 ||
    emailFeedsList.value.length > 0 ||
    webFeedsList.value.length > 0
  ) {
    totalType = 1
  } else {
    totalType = 0
  }
  if (!emailType.value) {
    if (socialType.value && webType.value) {
      emit(
        'define-height',
        emailFeedsList.value.length * 42 +
          socialFeedsList.value.length * 42 +
          webFeedsList.value.length * 42 +
          totalType * 42 +
          40,
      )
    } else if (socialType.value && !webType.value) {
      emit(
        'define-height',
        socialFeedsList.value.length * 42 +
          emailFeedsList.value.length * 42 +
          totalType * 42 +
          40,
      )
    } else if (!socialType.value && webType.value) {
      emit(
        'define-height',
        emailFeedsList.value.length * 42 +
          webFeedsList.value.length * 42 +
          totalType * 42 +
          40,
      )
    } else {
      emit(
        'define-height',
        emailFeedsList.value.length * 42 + totalType * 42 + 40,
      )
    }
    setTimeout(() => {
      emailType.value = true
      setSocialEmailTotalPages()
      menuHeight.value = false
    }, 300)
  } else if (emailType.value) {
    emailType.value = false
    setSocialEmailTotalPages()
    setTimeout(() => {
      if (socialType.value && webType.value) {
        emit(
          'define-height',
          socialFeedsList.value.length * 42 +
            webFeedsList.value.length * 42 +
            totalType * 42 +
            40,
        )
      } else if (socialType.value && !webType.value) {
        emit(
          'define-height',
          socialFeedsList.value.length * 42 + totalType * 42 + 40,
        )
      } else if (!socialType.value && webType.value) {
        emit(
          'define-height',
          webFeedsList.value.length * 42 + totalType * 42 + 40,
        )
      } else {
        emit('define-height', totalType * 42 + 40)
      }
    }, 300)
    if (!socialType.value && !emailType.value && !webType.value) {
      menuHeight.value = true
    }
  }
}
const expandWeb = () => {
  let totalType = 0
  if (
    socialFeedsList.value.length > 0 &&
    emailFeedsList.value.length > 0 &&
    webFeedsList.value.length > 0
  ) {
    totalType = 3
  } else if (
    (socialFeedsList.value.length > 0 && emailFeedsList.value.length > 0) ||
    (socialFeedsList.value.length > 0 && webFeedsList.value.length > 0) ||
    (webFeedsList.value.length > 0 && emailFeedsList.value.length > 0)
  ) {
    totalType = 2
  } else if (
    socialFeedsList.value.length > 0 ||
    emailFeedsList.value.length > 0 ||
    webFeedsList.value.length > 0
  ) {
    totalType = 1
  } else {
    totalType = 0
  }
  if (!webType.value) {
    if (socialType.value && emailType.value) {
      emit(
        'define-height',
        webFeedsList.value.length * 42 +
          socialFeedsList.value.length * 42 +
          emailFeedsList.value.length * 42 +
          totalType * 42 +
          40,
      )
    } else if (socialType.value && !emailType.value) {
      emit(
        'define-height',
        webFeedsList.value.length * 42 +
          socialFeedsList.value.length * 42 +
          totalType * 42 +
          40,
      )
    } else if (!socialType.value && emailType.value) {
      emit(
        'define-height',
        emailFeedsList.value.length * 42 +
          webFeedsList.value.length * 42 +
          totalType * 42 +
          40,
      )
    } else {
      emit(
        'define-height',
        webFeedsList.value.length * 42 + totalType * 42 + 40,
      )
    }
    setTimeout(() => {
      webType.value = true
      setSocialEmailTotalPages()
      menuHeight.value = false
    }, 300)
  } else if (webType.value) {
    webType.value = false
    setSocialEmailTotalPages()
    setTimeout(() => {
      if (socialType.value && emailType.value) {
        emit(
          'define-height',
          socialFeedsList.value.length * 42 +
            emailFeedsList.value.length * 42 +
            totalType * 42 +
            40,
        )
      } else if (socialType.value && !emailType.value) {
        emit(
          'define-height',
          socialFeedsList.value.length * 42 + totalType * 42 + 40,
        )
      } else if (!socialType.value && emailType.value) {
        emit(
          'define-height',
          emailFeedsList.value.length * 42 + totalType * 42 + 40,
        )
      } else {
        emit('define-height', totalType * 42 + 40)
      }
    }, 300)
    if (!socialType.value && !emailType.value && !webType.value) {
      menuHeight.value = true
    }
  }
}
const selectedFeed = (listItem) => {
  let username = ''
  if (listItem.provider === 'Facebook') {
    username = listItem.name ? listItem.name : listItem.username
  } else {
    username = listItem.username ? listItem.username : listItem.name
  }
  router.push('/home')
  nuxtApp.$bus.$emit('clear-all-date-range')
  store.commit('home/RESET_START_END_DATE')
  if (showSinglePost.value || showSingleImagePost.value) {
    setTimeout(() => {
      store.commit('home/SET_CURRENT_SOCIAL_COMPONENT', {
        provider: listItem.provider,
        username,
        id: listItem.id,
        selectedFeed: listItem,
      })
      store.commit('home/SET_TEMP_ARRAY', [])
      store.commit('socialFeed/SET_SHOW_TWITTER', true)
      store.commit('home/SET_FACEBOOK_CURRENT_TAB', 'all')
    }, 305)
  } else if (
    listItem.provider === 'Google' ||
    listItem.provider === 'Microsoft'
  ) {
    store.commit('home/SET_EMAIL_DYNAMIC_COMP', {
      comp: 'EmailContent',
    })
    store.commit('home/EXPAND_FULL_IMAGE', false)
    store.commit('home/SET_TEMP_ARRAY', [])
    setTimeout(() => {
      store.commit('home/SET_CURRENT_SOCIAL_COMPONENT', {
        provider: listItem.provider,
        username,
        id: listItem.id,
        selectedFeed: listItem,
      })
    }, 500)
  } else {
    store.commit('home/SET_CURRENT_SOCIAL_COMPONENT', {
      provider: listItem.provider,
      username,
      id: listItem.id,
      selectedFeed: listItem,
    })
    store.commit('home/SET_FACEBOOK_CURRENT_TAB', 'all')
    store.commit('home/SET_YOUTUBE_TYPE', 'All')
    store.commit('home/SET_TEMP_ARRAY', [])
  }
  expand()
  emit('change-feed')
  store.commit('home/RESET_WEB_SEARCH')
  store.commit('home/SET_SHOW_COMP', false)
  store.commit('home/SET_CURRENT_TAB', 'All')
  store.commit('home/SET_CURRENT_HEADER', 'RealTimeFeed')
  store.dispatch('socialFeed/singlePostClose', false)
  store.dispatch('socialFeed/singleImagePostClose', false)
  store.commit('home/RESET_TWITTER_COMMENTS')
  store.commit('home/RESET_PREVIOUS_TWITTER_COMMENTS')
  store.commit('socialFeed/RESET_PREVIOUS_SINGLE_POST')
}
const showEditFeed = () => {
  router.push('/settings/services')
}
const showAddFeedsComp = () => {
  store.commit('socialFeed/SHOW_ADD_FEED_MODAL', true)
}
const checkSelectedItem = () => {
  if (socialType.value && emailType.value && webType.value) {
    let tempArray = []
    if (socialTop.value) {
      tempArray = [
        ...socialFeedsList.value,
        ...emailFeedsList.value,
        ...webFeedsList.value,
      ]
    } else if (emailTop.value) {
      tempArray = [
        ...emailFeedsList.value,
        ...socialFeedsList.value,
        ...webFeedsList.value,
      ]
    } else if (webTop.value) {
      tempArray = [
        ...webFeedsList.value,
        ...socialFeedsList.value,
        ...emailFeedsList.value,
      ]
    }

    let enter = true
    if (enter) {
      for (const item of tempArray.slice(
        socialInitialItem.value,
        socialLoadCount.value,
      )) {
        if (item.backgroundColor === true) {
          enter = false
          socialStatus.value = true
          emailStatus.value = true
          webStatus.value = true
        }
      }
    }
    if (
      !socialStatus.value &&
      !emailStatus.value &&
      !webStatus.value &&
      enter
    ) {
      clickNextPage(false)
      checkSelectedItem()
    }
  }
}
const finalCollapsed = () => {
  if (isDesktop.value) {
    setTimeout(() => {
      // window.dispatchEvent(new Event('resize'))
      // if (
      //   socialPage.every((item) => item.backgroundColor === false) &&
      //   totalPages !== currentPage
      // ) {
      //   clickNextPage()
      // } else if (currentPage !== 1) {
      //   clickPreviousPage()
      // }
    }, 800)
  }
  toggle.value = false
  // if (!toggle && progress) {
  document.querySelectorAll('.overall_scroll').forEach((item) => {
    item.removeEventListener('scroll', handleScroll)
    item.scrollTop = 0
  })
  scroll.value = false
  increaseHeight.value = false
  store.commit('feedsDropdown/SET_SHOW_DROPDOWN', false)
  setTimeout(() => {
    emit('collapse-header', false)
  }, 140)
  setTimeout(() => {
    round.value = true
    menuOpen.value = false
    menuHeight.value = false
    emit('expand', false)
    store.commit('home/SET_FEEDS_DROPDOWN', false)
  }, 400)
  setTimeout(() => {
    progress.value = false
  }, 900)
  // }
}
const collapsed = ($event) => {
  if (progress.value) {
    if (isDesktop.value) {
      setTimeout(() => {
        // window.dispatchEvent(new Event('resize'))
        // if (
        //   socialPage.every((item) => item.backgroundColor === false) &&
        //   totalPages !== currentPage
        // ) {
        //   clickNextPage()
        // } else if (currentPage !== 1) {
        //   clickPreviousPage()
        // }
      }, 800)
    }
    toggle.value = false
  }
  if (!toggle.value && progress.value) {
    document.querySelectorAll('.overall_scroll').forEach((item) => {
      item.removeEventListener('scroll', handleScroll)
      item.scrollTop = 0
    })
    scroll.value = false
    increaseHeight.value = false
    store.commit('feedsDropdown/SET_SHOW_DROPDOWN', false)
    setTimeout(() => {
      emit('collapse-header', false)
    }, 140)
    setTimeout(() => {
      round.value = true
      menuOpen.value = false
      menuHeight.value = false
      emit('expand', false)
      store.commit('home/SET_FEEDS_DROPDOWN', false)
    }, 400)
    setTimeout(() => {
      progress.value = false
    }, 900)
  }
}
const expand = ($event) => {
  if (!progress.value) {
    store.commit('feedsDropdown/SET_SOCIAL_INITIAL_ITEM', 0)
    store.commit('feedsDropdown/SET_SOCIAL_LOAD_COUNT', socialPerPage.value)
    store.commit('feedsDropdown/SET_OLD_SCROLL_TOP', 0)
    store.commit('feedsDropdown/SET_CURRENT_SCROLL_POSITION', 0)
    store.commit('feedsDropdown/SET_CURRENT_OLD_SCROLL_TOP', 0)
    store.commit('feedsDropdown/SET_SOCIAL_CURRENT_PAGE', 1)
    activeFeed()
    setTimeout(() => {
      checkSelectedItem()
    }, 380)
    if (isDesktop.value && showSingleImagePost.value) {
      setTimeout(() => {
        window.dispatchEvent(new Event('resize'))
        // if (
        //   socialPage.every((item) => item.backgroundColor === false) &&
        //   totalPages !== currentPage
        // ) {
        //   clickNextPage()
        // } else if (currentPage !== 1) {
        //   clickPreviousPage()
        // }
      }, 100)
    }
    toggle.value = true
  } else if (progress.value) {
    if (isDesktop.value && showSingleImagePost.value) {
      setTimeout(() => {
        window.dispatchEvent(new Event('resize'))
        // if (
        //   socialPage.every((item) => item.backgroundColor === false) &&
        //   totalPages !== currentPage
        // ) {
        //   clickNextPage()
        // } else if (currentPage !== 1) {
        //   clickPreviousPage()
        // }
      }, 800)
    }
    toggle.value = false
  }
  if (
    toggle.value &&
    !progress.value &&
    (emailType.value || socialType.value || webType.value) &&
    (emailFeedsList.value.length > 0 ||
      socialFeedsList.value.length > 0 ||
      webFeedsList.value.length > 0)
  ) {
    dropdownOpen()
  } else if (!toggle.value && progress.value) {
    dropdownClose()
  }
}
const dropdownOpen = () => {
  store.commit(
    'feedsDropdown/SET_SOCIAL_TOTAL_PAGES',
    Math.ceil(
      (socialFeedsList.value.length +
        emailFeedsList.value.length +
        webFeedsList.value.length +
        3) /
        socialPerPage.value,
    ),
  )
  document.querySelectorAll('.overall_scroll').forEach((item) => {
    if (item.offsetHeight === 0) {
      setTimeout(() => {
        item.addEventListener('scroll', handleScroll)
      }, 500)
    }
  })
  menuOpen.value = true
  menuHeight.value = false
  round.value = false
  emit('collapse-header', true)
  if (
    socialFeedsList.value.length > 0 &&
    emailFeedsList.value.length > 0 &&
    webFeedsList.value.length > 0
  ) {
    if (
      socialFeedsList.value.length +
        emailFeedsList.value.length +
        webFeedsList.value.length +
        3 >=
      socialPerPage.value
    ) {
      emit('define-height', socialPerPage.value * 42 + 3 * 42 + 40)
    } else if (
      socialFeedsList.value.length +
        emailFeedsList.value.length +
        webFeedsList.value.length +
        3 <
      socialPerPage.value
    ) {
      emit(
        'define-height',
        socialFeedsList.value.length * 42 +
          emailFeedsList.value.length * 42 +
          webFeedsList.value.length * 42 +
          3 * 42 +
          40,
      )
    }
  } else if (
    socialFeedsList.value.length > 0 &&
    emailFeedsList.value.length > 0
  ) {
    if (
      socialFeedsList.value.length + emailFeedsList.value.length + 2 >=
      socialPerPage.value
    ) {
      emit('define-height', socialPerPage.value * 42 + 2 * 42 + 40)
    } else if (
      socialFeedsList.value.length + emailFeedsList.value.length + 2 <
      socialPerPage.value
    ) {
      emit(
        'define-height',
        socialFeedsList.value.length * 42 +
          emailFeedsList.value.length * 42 +
          2 * 42 +
          40,
      )
    }
  } else if (
    socialFeedsList.value.length > 0 &&
    webFeedsList.value.length > 0
  ) {
    if (
      socialFeedsList.value.length + webFeedsList.value.length + 2 >=
      socialPerPage.value
    ) {
      emit('define-height', socialPerPage.value * 42 + 2 * 42 + 40)
    } else if (
      socialFeedsList.value.length + webFeedsList.value.length + 2 <
      socialPerPage.value
    ) {
      emit(
        'define-height',
        socialFeedsList.value.length * 42 +
          webFeedsList.value.length * 42 +
          2 * 42 +
          40,
      )
    }
  } else if (emailFeedsList.value.length > 0 && webFeedsList.value.length > 0) {
    if (
      emailFeedsList.value.length + webFeedsList.value.length + 2 >=
      socialPerPage.value
    ) {
      emit('define-height', socialPerPage.value * 42 + 2 * 42 + 40)
    } else if (
      emailFeedsList.value.length + webFeedsList.value.length + 2 <
      socialPerPage.value
    ) {
      emit(
        'define-height',
        emailFeedsList.value.length * 42 +
          webFeedsList.value.length * 42 +
          2 * 42 +
          40,
      )
    }
  } else if (socialFeedsList.value.length > 0) {
    if (socialFeedsList.value.length + 1 >= socialPerPage.value) {
      emit('define-height', socialPerPage.value * 42 + 42 + 40)
    } else {
      emit('define-height', socialFeedsList.value.length * 42 + 42 + 40)
    }
  } else if (emailFeedsList.value.length > 0) {
    if (emailFeedsList.value.length + 1 >= socialPerPage.value) {
      emit('define-height', socialPerPage.value * 42 + 42 + 40)
    } else {
      emit('define-height', emailFeedsList.value.length * 42 + 42 + 40)
    }
  } else if (webFeedsList.value.length > 0) {
    if (webFeedsList.value.length + 1 >= socialPerPage.value) {
      emit('define-height', socialPerPage.value * 42 + 42 + 40)
    } else {
      emit('define-height', webFeedsList.value.length * 42 + 42 + 40)
    }
  }
  store.commit('home/SET_FEEDS_DROPDOWN', true)
  emit('expand', true)
  setTimeout(() => {
    increaseHeight.value = true
    store.commit('feedsDropdown/SET_SHOW_DROPDOWN', true)
  }, 300)
  setTimeout(() => {
    scroll.value = true
    progress.value = true
  }, 900)
}
const dropdownClose = () => {
  document.querySelectorAll('.overall_scroll').forEach((item) => {
    item.removeEventListener('scroll', handleScroll)
    item.scrollTop = 0
  })
  scroll.value = false
  increaseHeight.value = false
  store.commit('feedsDropdown/SET_SHOW_DROPDOWN', false)
  setTimeout(() => {
    emit('collapse-header', false)
  }, 140)
  setTimeout(() => {
    round.value = true
    menuOpen.value = false
    menuHeight.value = false
    emit('expand', false)
    store.commit('home/SET_FEEDS_DROPDOWN', false)
  }, 400)
  setTimeout(() => {
    progress.value = false
    activeFeed()
  }, 900)
}
const hideMobileHaeder = () => {
  store.dispatch('header/closeMobileHeader')
}
</script>

<style lang="scss" scoped>
@import url('https://fonts.googleapis.com/css2?family=Neuton');

name-font {
  font-family: 'Neuton', serif;
}

.feedsShadow {
  box-shadow: 0 25px 50px 20px rgb(0 0 0 / 25%);
}

.bg-white-opasity-50 {
  background-color: #ffffff80;
}
.fade-enter-active,
.fade-leave-active {
  transition: all 0.5s;
}
.fade-enter,
.fade-leave-to {
  height: 100%;
  opacity: 100;
}
.tooltip {
  @apply invisible;
}
.has-tooltip:hover .tooltip {
  @apply visible;
  left: -30px;
  padding: 3px 10px;
}
.text-xxs {
  font-size: 11px;
  line-height: 16px;
}
.min-w-7-2 {
  min-width: 1.875rem !important;
}
.min-h-7-2 {
  min-height: 1.875rem !important;
}

.menu-wrapper {
  .background {
    opacity: 0;
    position: absolute;
    z-index: 10;
    transition:
      margin-top 0.5s ease-in-out,
      opacity 0.3s ease 0.5s,
      background 0.3s ease 0.5s;
    height: 2.75rem;
    @apply w-full;

    .corner-top {
      position: absolute;
      top: -20px;
      right: 0px;
      display: inline-block;
      transition: color 0.3s ease 0.5s;
    }

    .corner-bottom {
      position: absolute;
      bottom: -20px;
      right: 0px;
      transform: rotate(270deg);
      display: inline-block;
      transition: color 0.3s ease 0.5s;
    }

    &.active {
      opacity: 1;
      .corner-top,
      .corner-bottom {
        // opacity: 1;
      }
    }
  }
}

.balance__menu {
  // z-index: 80;
  transition:
    border-radius 0.5s ease-in-out,
    border-top-left-radius 0.5s ease-in-out,
    border-top-right-radius 0.5s ease-in-out;
  max-width: 16rem;
  min-width: 16rem;
  @apply md:w-full w-full;
  .btn-wrapper {
    background: #4c5764;
    @apply w-full px-4 flex justify-between items-center shadow-sm;

    .dropdown-btn {
      direction: ltr;
      line-height: 2.15rem !important;
      @apply text-white w-full h-10 text-lg focus:outline-none;

      * {
        // pointer-events: none;
      }
    }
  }
  .dropdown-btn {
    direction: ltr;
    @apply text-white w-full h-10 px-3.5 text-lg flex justify-between items-center shadow-sm focus:outline-none;

    * {
      // pointer-events: none;
    }
  }
}

/* these classname shuld be provided through the `source` props. */
.archive {
  background: #8db230 !important;
}

.search {
  background: #7d80bd !important;
}

.pricing {
  background: #a22a2a !important;
}
.agentBook {
  background: #5f9fc7 !important;
}

.alert {
  background: #e05252 !important;
}
.source {
  background: #4A71D4 !important;
}
.settings {
  background: #e0ad1f !important;
}

.help {
  background: #e05252 !important;
}

.feeds-button {
  color: #e4801d;
}
.username__archive,
.feeds-button__archive {
  color: #8db230 !important;
}
.username__search,
.feeds-button__search {
  color: #7d80bd !important;
}
.username__pricing,
.feeds-button__pricing {
  color: #a22a2a !important;
}
.feeds-button__agentBook {
  color: #5f9fc7 !important;
}
.username__alert,
.feeds-button__alert {
  color: #e05252 !important;
}
.username__source,
.feeds-button__source {
  color: #4A71D4 !important;
}
.username__settings,
.feeds-button__settings {
  color: #e0ad1f !important;
}

.dropdown {
  max-width: 16rem;
  overflow: hidden;
  @apply w-full;
  z-index: 100;
  overflow-x: hidden;
  height: 0;
  max-height: calc(100% - 132px);
  transform-origin: top;
  transition:
    transform 0.3s linear,
    height 0.5s linear;
  .dropdown-btn,
  .menu-title,
  .list-title,
  .equity {
    //direction: ltr;
    // background: #b76d1d;
    background: #e4801d;
    line-height: 2.15rem !important;
    @apply text-white w-full h-10 px-3 text-lg flex justify-between items-center shadow-sm focus:outline-none;

    * {
      // pointer-events: none;
    }

    &__archive {
      background: #5f822d !important;
    }

    &__search {
      background: #5a57a2 !important;
    }

    &__pricing {
      background: #a22a2a !important;
    }

    &__agentBook {
      background: #5f9fc7 !important;
    }

    &__alert {
      background: #9d1616 !important;
    }
    &__source {
      background: #4A71D4 !important;
    }
    &__settings {
      background: #695316 !important;
    }

    &__help {
      background: #e05252 !important;
    }
  }

  .equity {
    @apply py-2;
    padding-left: 13px;
    padding-right: 13px;
  }

  // .scroll-hidden {
  //   overflow: hidden;
  // }

  .scroll {
    scroll-behavior: smooth;
    overflow-y: auto;
    overflow-x: hidden;
    -ms-overflow-style: none; /* IE 11 */
    scrollbar-width: thin;
    scrollbar-color: #b76d1d #ececec; /* Firefox 64 */
    &::-webkit-scrollbar {
      width: 6px;
    }

    /* Track */
    &::-webkit-scrollbar-track {
      border-radius: 3px;
      background: #ececec;
    }

    /* Handle */
    &::-webkit-scrollbar-thumb {
      background: #b76d1d;
      border-radius: 3px;
    }

    /* Handle on hover */
    &::-webkit-scrollbar-thumb:hover {
      background: #b76d1d;
    }
  }
  .scroll__archive {
    scroll-behavior: smooth;
    scrollbar-color: #5f822d #ececec; /* Firefox 64 */
    /* Handle */
    &::-webkit-scrollbar-thumb {
      background: #5f822d;
      border-radius: 3px;
    }

    /* Handle on hover */
    &::-webkit-scrollbar-thumb:hover {
      background: #5f822d;
    }
  }
  .scroll__search {
    scroll-behavior: smooth;
    scrollbar-color: #5a57a2 #ececec; /* Firefox 64 */
    /* Handle */
    &::-webkit-scrollbar-thumb {
      background: #5a57a2;
      border-radius: 3px;
    }

    /* Handle on hover */
    &::-webkit-scrollbar-thumb:hover {
      background: #5a57a2;
    }
  }
  .scroll__pricing {
    scroll-behavior: smooth;
    scrollbar-color: #a22a2a #ececec; /* Firefox 64 */
    /* Handle */
    &::-webkit-scrollbar-thumb {
      background: #a22a2a;
      border-radius: 3px;
    }

    /* Handle on hover */
    &::-webkit-scrollbar-thumb:hover {
      background: #a22a2a;
    }
  }
  .scroll__agentBook {
    scroll-behavior: smooth;
    scrollbar-color: #5f9fc7 #ececec; /* Firefox 64 */
    /* Handle */
    &::-webkit-scrollbar-thumb {
      background: #5f9fc7;
      border-radius: 3px;
    }

    /* Handle on hover */
    &::-webkit-scrollbar-thumb:hover {
      background: #5f9fc7;
    }
  }
  .scroll__alert {
    scroll-behavior: smooth;
    scrollbar-color: #9d1616 #ececec; /* Firefox 64 */
    /* Handle */
    &::-webkit-scrollbar-thumb {
      background: #9d1616;
      border-radius: 3px;
    }

    /* Handle on hover */
    &::-webkit-scrollbar-thumb:hover {
      background: #9d1616;
    }
  }
  .scroll__source {
    scroll-behavior: smooth;
    scrollbar-color: #2153d3 #ececec; /* Firefox 64 */
    /* Handle */
    &::-webkit-scrollbar-thumb {
      background: #2153d3;
      border-radius: 3px;
    }

    /* Handle on hover */
    &::-webkit-scrollbar-thumb:hover {
      background: #2153d3;
    }
  }
  .scroll__settings {
    scroll-behavior: smooth;
    scrollbar-color: #695316 #ececec; /* Firefox 64 */
    /* Handle */
    &::-webkit-scrollbar-thumb {
      background: #695316;
      border-radius: 3px;
    }

    /* Handle on hover */
    &::-webkit-scrollbar-thumb:hover {
      background: #695316;
    }
  }

  .list-wrapper {
    .group-archive {
      background: #b76d1d;
      font-style: normal;
      font-variant: normal;
      font-weight: normal;
      border-bottom: 0px;
      letter-spacing: 0;
      font-size: 1.125rem;
      color: #f2f2f2;

      &__name {
        height: 42px;
      }
      &__archive {
        background: #5f822d !important;
      }
      &__search {
        background: #5a57a2 !important;
      }
      &__pricing {
        background: #a22a2a !important;
      }
      &__agentBook {
        background: #5f9fc7 !important;
      }
      &__alert {
        background: #9d1616 !important;
      }
      &__source {
        background: #2153d3 !important;
      }
      &__settings {
        background: #695316 !important;
      }
      &__help {
        background: #e05252 !important;
      }

      .list {
        .list-item {
          height: 42px;
          background: #e4801d;
          border-color: #b76d1d;
        }
        .__archive {
          background: #8db230;
          border-color: #5f822d !important;
        }
        .__search {
          background: #7d80bd;
          border-color: #5a57a2 !important;
        }
        .__pricing {
          background: #d94848;
          border-color: #a22a2a !important;
        }
        .__agentBook {
          background: #96d4fb;
          border-color: #5f9fc7 !important;
        }
        .__alert {
          background: #e05252;
          border-color: #9d1616 !important;
        }
        .__source {
          background: #4A71D4;
          border-color: #2153d3 !important;
        }
        .__settings {
          background: #e0ad1f;
          border-color: #695316 !important;
        }
        .__help {
          background: #8db230;
          border-color: #5f822d !important;
        }

        .__active_home {
          background: #cc6f15;
        }
        .__active_archive {
          background: #7b9a29;
          border-color: #5f822d !important;
        }
        .__active_search {
          background: #696db4;
          border-color: #5a57a2 !important;
        }
        .__active_pricing {
          background: #c44040;
          border-color: #a22a2a !important;
        }
        .__active_agentBook {
          background: #96d4fb;
          border-color: #5f9fc7 !important;
        }
        .__active_alert {
          background: #9d1616;
          border-color: #9d1616 !important;
        }
        .__active_source {
          background: #3460d1;
          border-color: #2153d3 !important;
        }
        .__active_settings {
          background: #b18714;
          border-color: #695316 !important;
        }
      }
    }

    /*.list {
        .list-item {
          direction: ltr;
          height: 45px;
          .background {
            opacity: 0;
            // transition: opacity 1s;
            // outline: none;
            // overflow: hidden;

            transform: translateY(-100%);
            transition: transform 0.5s ease-in-out,
              opacity 0.3s ease-in-out 0.5s;
          }
          &.active {
            // z-index: 1000;
            // @apply relative;

            .background {
              @apply h-full w-full;
              margin-left: -10px;
              position: absolute;
              left: 10px;
              opacity: 1;
              transform: translateY(0);
              z-index: 10;
              width: 100%;
              &__circle {
                @apply h-10 w-10 rounded-full inline-block;
              }

              .corner-top {
                position: absolute;
                top: -19px;
                right: 0px;
                display: inline-block;
              }

              .corner-bottom {
                position: absolute;
                bottom: -19px;
                right: 0px;
                transform: rotate(270deg);
                display: inline-block;
              }
            }
          }
          &-content {
            z-index: 20;
            transition: all 0.5s ease-in-out;
            @apply relative w-full;
          }
        }
      }*/
  }

  .list-item {
    // direction: ltr;
    text-align: left;
  }

  &.expand {
    height: var(--height);
    // height: calc(100vh - 172px);
  }
  &.expand1 {
    height: auto;
  }
}

.dropdown-icon {
  pointer-events: none;

  &.rotate {
    transform: rotate(180deg) !important;
  }
}
.line-height {
  line-height: 16px;
}
.list-inner-width {
  width: calc(100% - 40px);
}
.username-width {
  width: calc(100% - 90px);
}
.username-clamp {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.mail-icon-wrapper {
  @apply flex justify-center items-center absolute w-3.5 h-3.5 rounded-full bg-white;
  bottom: -2px;
  right: -1px;
}
.social-icon {
  @apply absolute w-3.5 h-3.5;
  bottom: -2px;
  right: -1px;
}
.letter-spacing-1px {
  letter-spacing: 1px;
}
@media (max-width: 767px) {
  .dropdown {
    // transition: height 1s linear;
    max-width: 100%;
    max-height: var(--maxHeight);
  }
  .balance__menu {
    max-width: 100% !important;
  }
  .mobile-inner-width {
    width: calc(100% - 30px);
  }
  .mobile-username-width {
    width: calc(100% - 100px);
  }
  .mobile-clamp {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .expand {
    max-height: var(--maxHeight);
  }
}
/* for page */
.fadeIn-enter-active,
.fadeIn-leave-active {
  transition: opacity 0.5s;
}
.fadeIn-enter,
.fadeIn-leave-to {
  opacity: 0;
}
</style>
