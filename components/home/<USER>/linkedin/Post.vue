<!-- eslint-disable vue/no-useless-template-attributes -->
<template>
  <div
    class="w-full my-3 bg-white border border-gray-500 rounded-lg cursor-pointer card"
  >
    <div
      class="flex flex-row justify-between w-full px-4 pt-4"
      @click="
        article.category === 'NONE' ||
        article.category === 'VIDEO' ||
        article.category === 'poll' ||
        article.category === ''
          ? showSinglePost(article)
          : showSingleImagePost(article, 0)
      "
    >
      <div class="flex flex-row space-x-3">
        <SharedIconLinkedinIcon
          v-if="!profilePic"
          class="border border-gray-400 profile-image"
        ></SharedIconLinkedinIcon>
        <img
          v-if="profilePic"
          class="border border-gray-400 profile-image"
          :src="profilePic"
          alt=""
        />

        <div class="leading-5">
          <h2 class="text-lg text-ash-primary">
            {{ article.name ? article.name : articles.name }}
          </h2>
          <!-- <div class="text-gray-1700">5,659,315 followers</div> -->
          <div class="flex items-center space-x-1">
            <div class="text-gray-1700">
              <date-time
                :datetime="article.createdAt"
                format="h"
                :friendly="true"
              ></date-time>
            </div>
            <span
              v-if="article.createdAt !== article.updatedAt"
              class="text-gray-1600 inline-block text-3xl"
              >·</span
            >
            <div
              v-if="article.createdAt !== article.updatedAt"
              class="text-gray-1700"
            >
              Edited
            </div>
            <span
              v-if="article.visibility === 'PUBLIC'"
              class="text-gray-1600 inline-block text-3xl"
              >·</span
            >
            <div v-if="article.visibility === 'PUBLIC'" class="text-gray-1600">
              <ClientOnly>
                <fa
                  class="text-md font-normal"
                  :icon="['fas', 'earth-americas']"
                />
              </ClientOnly>
            </div>
          </div>
        </div>
      </div>
      <!-- <div class="cursor-pointer">....</div> -->
    </div>
    <div
      :id="article.id"
      v-see-more="200"
      class="w-full px-4 mt-4 text-md break-words"
      :class="
        article.provider === 'LinkedIn'
          ? 'linkedIn_Text font-medium'
          : 'text-ash-primary'
      "
      @click="
        article.category === 'NONE' ||
        article.category === 'VIDEO' ||
        article.category === 'poll' ||
        article.category === ''
          ? showSinglePost(article)
          : showSingleImagePost(article, 0)
      "
      v-html="addLineBreak(article.text, article.provider)"
    ></div>
    <div
      v-if="article.text && article.text.length > 200"
      class="w-full h-6"
    ></div>
    <div
      v-if="
        (article.category === 'IMAGE' || article.category === 'multiImage') &&
        article.mediaUrls.length > 0
      "
      class="relative flex flex-wrap justify-center w-full mt-3"
    >
      <div
        v-for="(feature, imageIndex) in article.mediaUrls.slice(0, 5)"
        :key="feature"
        class="flex items-center justify-center cursor-pointer"
        :class="imageIndex < 2 ? 'column-2 ' : 'column-3 '"
        @click="showSingleImagePost(article, imageIndex)"
      >
        <img class="object-cover w-full h-full hover-border" :src="feature" />
        <div v-if="imageIndex === 4" class="absolute z-0 w-full post-overlay">
          <div class="sub-overlay"></div>
          <p
            class="relative text-xl font-bold text-white opacity-100 xl:text-5xl md:text-3xl"
          >
            + {{ article.mediaUrls.length - 4 }}
          </p>
        </div>
      </div>
    </div>

    <template
      v-if="article.category === 'VIDEO' && article.mediaUrls.length > 0"
      class="w-full mt-3"
      @click="
        article.category === 'NONE' ||
        article.category === 'VIDEO' ||
        article.category === 'poll' ||
        article.category === ''
          ? showSinglePost(article)
          : ''
      "
    >
      <div v-for="(video, videoIndex) in article.mediaUrls" :key="videoIndex">
        <video class="w-full video-height" height="400" controls="controls">
          <source :src="video" />
          <source :src="article.thumbnailUrl" />
        </video>
      </div>
      <div
        v-if="article.title"
        class="flex items-center w-full px-3 py-2 bg-blue-mainlyblue"
      >
        <h2
          class="flex-grow block overflow-hidden text-sm font-semibold text-black break-words"
        >
          {{ article.title }}
        </h2>
      </div>
    </template>

    <template
      v-if="article.category === 'poll' && article.poll.options.length > 0"
    >
      <div
        class="w-full mt-6 px-4"
        @click="article.category === 'poll' ? showSinglePost(article) : ''"
      >
        <div class="border border-gray-200 rounded-2xl p-2">
          <p class="linkedIn_Text font-semibold">{{ article.poll.question }}</p>
          <p class="text-xs text-gray-1700">
            You can see how people vote.
            <a
              href="https://www.linkedin.com/help/linkedin/answer/119171?lang=en"
              target="_blank"
              class="text-blue-linkedin hover:underline"
              >Learn more</a
            >
          </p>
          <div
            v-for="(pollOption, pollOptionIndex) in article.poll.options"
            :key="pollOptionIndex"
            class="flex flex-col my-2 justify-between space-x-2 items-center w-full p-2 relative"
          >
            <div
              class="absolute top-0 left-0 h-full poll_bgColor"
              :class="[
                votingPercentage(article.poll.options, pollOption.voteCount) ===
                0
                  ? 'rounded-lg'
                  : 'rounded-md',
              ]"
              :style="{
                '--bgValue':
                  votingPercentage(
                    article.poll.options,
                    pollOption.voteCount,
                  ) === 0
                    ? '4px'
                    : votingPercentage(
                          article.poll.options,
                          pollOption.voteCount,
                        ) === 100
                      ? `${
                          votingPercentage(
                            article.poll.options,
                            pollOption.voteCount,
                          ) - 16
                        }%`
                      : `${
                          votingPercentage(
                            article.poll.options,
                            pollOption.voteCount,
                          ) - 10
                        }%`,
              }"
            ></div>

            <div class="w-full flex justify-between text-sm">
              <span class="linkedIn_Text font-semibold z-1 break-all">
                {{ pollOption.text }}
              </span>

              <span class="linkedIn_Text font-semibold z-1">
                {{
                  votingPercentage(article.poll.options, pollOption.voteCount)
                }}%
              </span>
            </div>
          </div>

          <div class="flex space-x-2 items-center">
            <span
              v-if="totalVotes(article.poll.options) === 1"
              class="text-blue-linkedin mt-4 inline-block font-bold"
              >{{ totalVotes(article.poll.options) }} vote</span
            >

            <span v-else class="text-blue-linkedin mt-4 inline-block font-bold"
              >{{ totalVotes(article.poll.options) }} votes</span
            >

            <span class="text-gray-1600 mt-4 inline-block text-3xl">·</span>

            <span class="text-gray-1700 mt-4 inline-block">
              {{
                article.poll.voting_status === 'closed'
                  ? 'Poll closed'
                  : totalTimeLeft(article)
              }}
            </span>
          </div>
        </div>
      </div>
    </template>

    <div
      class="w-full"
      @click="
        article.category === 'NONE' ||
        article.category === 'VIDEO' ||
        article.category === 'poll' ||
        article.category === ''
          ? showSinglePost(article)
          : showSingleImagePost(article, 0)
      "
    >
      <div
        class="flex flex-row items-center justify-start py-2 mx-5 mb-4 space-x-2 border-b-2 border-gray-500"
      >
        <SharedIconLinkedInLike class="w-5" />
        <SharedIconLinkedInLove class="w-5" />
        <SharedIconLinkedInClap class="w-5" />
        <span class="text-gray-1700">
          {{ article.likesCount }}
        </span>
        <span class="mx-px font-normal text-xxs text-gray-1700">•</span>
        <span class="text-gray-1700"
          >{{ article.aggregatedCommentsCount }} comments</span
        >
      </div>
    </div>
    <div class="w-ful">
      <div
        class="flex flex-row items-center justify-center py-3 space-x-2"
        @click="loadFeedComment"
      >
        <SharedIconLinkedInComment class="w-5" />
        <span class="text-xs text-gray-1700"> Comment</span>
      </div>
    </div>
    <HomeRealtimeFeedRssLinkedinComments
      v-if="feedComments"
      :comments="feedComments"
    ></HomeRealtimeFeedRssLinkedinComments>
  </div>
</template>

<script setup lang="ts">
import { useStore } from 'vuex'
import { subDays } from 'date-fns'
import { useFeedComment } from '../../../../composables/feeds/useComment'
import {
  playVideos,
  pauseVideos,
} from '../../../../composables/feeds/playVideos'

const props = defineProps({
  article: {
    type: Object,
    default: () => {},
  },
  profilePic: {
    type: String,
    default: '',
  },
})
const store = useStore()
const { loadComment } = useFeedComment()
const { addLineBreak } = useLineBreak()
const feedComments = ref(null)
const collapseable = ref(false)
const loadFeedComment = async () => {
  if (collapseable.value) {
    feedComments.value = null
    collapseable.value = false
  } else {
    try {
      const { data } = await loadComment({
        provider: 'LinkedIn',
        id: props.article.id,
      })
      if (data.length) {
        feedComments.value = data
        collapseable.value = true
      }
    } catch (error) {
      console.log(error)
    }
  }
}

const articles = computed(() => store.state.home.articles)
onMounted(() => {
  playVideos()

  setTimeout(() => {
    document
      .getElementById('desktop-body-wrapper')
      ?.addEventListener('scroll', pauseVideos)
  }, 1000)
})

onUnmounted(() => {
  document
    .getElementById('desktop-body-wrapper')
    ?.removeEventListener('scroll', pauseVideos)
})

const showSinglePost = (singleItem) => {
  store.dispatch('socialFeed/singlePostShow', {
    singleItem,
    show: true,
  })
}
const votingPercentage = (value: any, singleVote: number) => {
  const totalValue = value.reduce((vote, item) => vote + item.voteCount, 0)
  const votePercentage = totalValue > 0 ? (singleVote * 100) / totalValue : 0

  return votePercentage === 0
    ? 0
    : votePercentage === 100
      ? 100
      : votePercentage.toFixed(1)
}
const totalVotes = (value: any) => {
  return value.reduce((total, item) => total + item.voteCount, 0)
}

const totalTimeLeft = (article: any) => {
  const daysMap = {
    ONE_DAY: -1,
    THREE_DAYS: -2,
    SEVEN_DAYS: -6,
    FOURTEEN_DAYS: -13,
  }
  const needSubtract = ref(daysMap[article.poll.settings.duration] || 0)
  const endDate = subDays(new Date(article.createdAt), needSubtract.value)
  const endDateUpdate = new Date() - new Date(endDate)
  const days = Math.floor(endDateUpdate / 1000 / 60 / 60 / 24)
  const hours = Math.floor(endDateUpdate / 1000 / 60 / 60)
  const remainingDay = ref(`${Math.abs(days)}d`)

  Math.abs(days) === 14
    ? (remainingDay.value = '2w')
    : Math.abs(days) === 7
      ? (remainingDay.value = '1w')
      : Math.abs(days) < 14 && Math.abs(days) > 7
        ? (remainingDay.value = `1w ${Math.abs(days) - 7}d`)
        : hours > -24
          ? (remainingDay.value = ref(`${Math.abs(hours)}h`))
          : (remainingDay.value = `${Math.abs(days)}d`)

  return days >= 0 && hours >= 0 ? 'Poll closed' : remainingDay.value + ' left'
}
const showSingleImagePost = (singleItem:any, index: number) => {
  store.dispatch('socialFeed/singleImagePostShow', {
    singleItem,
    show: true,
    index,
  })
}
</script>

<style lang="scss" scoped>
.poll_bgColor {
  background: rgba(0, 0, 0, 0.08);
  width: var(--bgValue);
}
.linkedIn_Text {
  color: #000000e6;
}
.cross-icon {
  color: #e4801d;
  font-size: 26.2px;
}

.circle-icon {
  padding: 8px 12px;
  border-radius: 50%;
}

.profile-image {
  /* @apply w-14.5 h-14.5; */
  width: 66px;
  height: 66px;
  border-radius: 0%;
}
.column-2 {
  flex: 48%;
  margin: 2px;
  overflow: hidden;
  text-align: center;
  clear: both;
  position: relative;
  height: 331.188px;
}

.column-3 {
  margin: 2px;
  flex: 32%;
  overflow: hidden;
  clear: both;
  text-align: center;
  position: relative;
  height: 220.797px;
}

.hover-border {
  border: 1px solid transparent;
}
.post-overlay {
  align-items: center;
  border-radius: inherit;
  display: flex;
  justify-content: center;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: auto;
  z-index: 150;
  color: #fff;
  transition:
    0.3s cubic-bezier(0.25, 0.8, 0.5, 1),
    z-index 1ms;
}

.text-xxs {
  font-size: 6px;
}
.video-height {
  height: 400px;
}
</style>
