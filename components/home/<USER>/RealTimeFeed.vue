<template>
  <section
    class="overflow-hidden md:rounded-2xl rounded-t-none rounded-b-2xl md:h-full mobile-height w-full"
  >
    <div
      v-if="isDesktop"
      class="hidden md:block rounded-3xl bg-gray-2000 overflow-hidden h-full w-full"
    >
      <div
        class="title flex justify-center items-center px-4"
        @click="expand()"
      >
        <p
          class="py-2 text-white font-bold xl:text-xl md:text-base flex-grow pl-5"
        >
          Viewer
          <span
            v-if="
              currentComp === 'Web' &&
              (initialWebArchiveDate || selectedWebArchiveDate)
            "
            class="opacity-80 text-opacity-80"
            >(
            <date-time
              :datetime="
                currentComp === 'Web' && selectedWebArchiveDate
                  ? selectedWebArchiveDate
                  : currentComp === 'Web' && initialWebArchiveDate
                  ? initialWebArchiveDate
                  : ''
              "
              format="MMMM dd, yyyy"
              :show-time="false"
              :friendly="false"
              :zone-wise="false"
            ></date-time
            >)</span
          >
        </p>

        <span
          class="static select-toggle smallDesktop:hidden transition-all duration-500 transform"
          :class="feedIsExpanded ? 'rotate-180' : ' rotate-0'"
        >
          <ClientOnly>
            <fa :icon="['fas', 'caret-down']" />
          </ClientOnly>
        </span>
      </div>

      <!-- Body start -->

      <div
        id="desktop-body-wrapper"
        class="real-time-feed-body_wrapper"
        :class="hideScroll || loadArticles ? 'overflow-hidden' : 'scroll'"
      >
        <div
          id="desktop-body"
          class="middle-body_wrapper min-width-400 max-width-690 w-106 relative md:my-0"
          :class="[
            show &&
            currentComp !== 'Google' &&
            currentComp !== 'Microsoft' &&
            currentComp !== 'YouTube' &&
            currentComp !== 'Web' &&
            currentComp !== 'Pinterest' &&
            currentComp !== 'TikTok'
              ? 'max-width-690'
              : 'max-width-full',
            hideScroll ||
            currentComp === 'Google' ||
            currentComp === 'Microsoft' ||
            currentComp === 'YouTube' ||
            currentComp === 'Web' ||
            loadArticles ||
            !showComp ||
            (articles && articles.items && articles.items.length === 0)
              ? 'h-full'
              : 'h-auto',
            redditDevice && currentComp === 'Reddit'
              ? 'max-width-720 w-44_rem'
              : '',
          ]"
        >
          <transition name="page">
            <div
              v-if="
                loadArticles &&
                currentComp !== 'Google' &&
                currentComp !== 'Microsoft' &&
                currentComp !== 'YouTube' &&
                currentComp !== 'Web' &&
                currentComp !== 'Pinterest' &&
                currentComp !== 'TikTok'
              "
              class="mt-1 p-3 h-full lg:min-width-690 md:min-width-400 animate-pulse"
            >
              <h2 class="bg-gray-1500 w-8 h-2"></h2>

              <h2 class="bg-gray-1500 w-20 h-2 mt-2"></h2>

              <div class="w-full bg-gray-1500 lg:h-1/4 h-52 mt-2">
                <div
                  class="w-full flex flex-row items-center justify-start p-4 space-x-3"
                >
                  <div class="w-10 h-10 bg-white rounded-full"></div>

                  <div class="w-full">
                    <h2 class="bg-white w-20 h-2"></h2>

                    <h2 class="bg-white w-40 h-4 mt-2"></h2>
                  </div>
                </div>
              </div>

              <div class="w-full bg-gray-1500 lg:h-1/4 h-52 mt-2">
                <div
                  class="w-full flex flex-row items-center justify-start p-4 space-x-3"
                >
                  <div class="w-10 h-10 bg-white rounded-full"></div>

                  <div class="w-full">
                    <h2 class="bg-white w-20 h-2"></h2>

                    <h2 class="bg-white w-40 h-4 mt-2"></h2>
                  </div>
                </div>
              </div>

              <div class="w-full bg-gray-1500 lg:h-1/4 h-52 mt-2">
                <div
                  class="w-full flex flex-row items-center justify-start p-4 space-x-3"
                >
                  <div class="w-10 h-10 bg-white rounded-full"></div>

                  <div class="w-full">
                    <h2 class="bg-white w-20 h-2"></h2>

                    <h2 class="bg-white w-40 h-4 mt-2"></h2>
                  </div>
                </div>
              </div>
            </div>

            <div
              v-else-if="
                loadArticles &&
                (currentComp === 'Google' ||
                  currentComp === 'Microsoft' ||
                  currentComp === 'YouTube' ||
                  currentComp === 'Web') &&
                currentComp !== 'Pinterest' &&
                currentComp !== 'TikTok'
              "
              class="w-full h-full flex md:flex-row flex-col flex-nowrap animate-pulse"
            >
              <!-- Start Email Feed Header -->

              <div
                class="transition-all duration-500 ease-in-out md:w-1/3 w-full md:h-full mobile-skeleton_height opacity-100"
              >
                <div
                  class="bg-orange-200 flex w-full h-9 text-md font-bold text-ash-primary items-center"
                >
                  <div class="px-4">
                    <div class="heading bg-gray-1500 w-26 h-5"></div>
                  </div>
                </div>

                <div class="flex w-full email-view-wrapper pb-0">
                  <!-- Start Email Messages -->

                  <div
                    class="email-messages w-full h-full md:border-r border-yellow-300"
                  >
                    <div
                      v-for="(skeleton, index) in 10"
                      :key="index"
                      class="w-full pt-4 bg-white"
                    >
                      <div
                        class="singleMessageWrapper px-4 cursor-pointer relative"
                      >
                        <div
                          class="platform text-lg font-bold bg-gray-1500 lg:space-x-0 space-x-2 whitespace-normal break-words w-26 h-8 mb-1"
                        ></div>

                        <div
                          class="flex justify-between lg:space-x-0 space-x-2"
                        >
                          <div
                            class="heading text-sm text-ash-primary whitespace-normal break-words bg-gray-1500 w-28 h-5 mb-1"
                          ></div>

                          <div
                            class="attachment text-gray-light cursor-pointer bg-gray-1500 w-5 h-5"
                          ></div>
                        </div>

                        <div
                          class="flex text-sm justify-between lg:space-x-0 space-x-2"
                        >
                          <div
                            class="short-message bg-gray-1500 w-24 h-4 italic whitespace-normal break-words mb-1"
                          ></div>

                          <div
                            class="time text-sm bg-gray-1500 w-4 h-4 whitespace-nowrap mb-1"
                          ></div>
                        </div>
                      </div>
                    </div>

                    <div class="w-full flex justify-center py-4">
                      <div
                        class="bg-gray-1500 h-10 w-34 rounded-full relative flex justify-around space-x-1 items-center px-4 font-medium"
                      ></div>
                    </div>
                  </div>

                  <!-- End Email Messages -->
                </div>
              </div>

              <!-- End Email Feed Header -->

              <div class="md:hidden px-2 w-full h-px bg-gray-1500 my-2"></div>

              <!-- Start Email View -->

              <div
                class="transition-all duration-500 ease-in-out bg-white md:w-2/3 w-full md:h-full mobile-skeleton_height"
              >
                <div
                  class="bg-orange-200 flex w-full h-9 text-md font-bold items-center"
                >
                  <div class="flex justify-between items-center px-4 w-full">
                    <div class="heading bg-gray-1500 w-26 h-5"></div>

                    <div class="extend cursor-pointer bg-gray-1500 h-5 w-5">
                      <div class="plus_button" data-title="Extend All"></div>
                    </div>
                  </div>
                </div>

                <div class="flex w-full email-view-wrapper pb-0">
                  <!-- Start Email Content Wrapper-->

                  <div class="email-content h-full w-full px-4 pt-2">
                    <!-- Start Email Body -->

                    <div
                      v-for="(skeleton, index) in 3"
                      :key="index"
                      class="email-body"
                    >
                      <transition name="fadeIn">
                        <div
                          class="flex justify-between lg:flex-nowrap lg:space-y-0 flex-wrap space-y-1 transition-all duration-300 cursor-pointer py-1"
                        >
                          <div
                            class="name-wrapper flex space-x-2 justify-between items-center w-2/3"
                          >
                            <div
                              class="mail-name text-lg bg-gray-1500 w-26 h-6"
                            ></div>

                            <div
                              class="attachment-icon-flag cursor-pointer self-start pt-1 h-5 w-5 bg-gray-1500"
                            ></div>
                          </div>

                          <div class="date bg-gray-1500 w-20 h-6"></div>
                        </div>
                      </transition>

                      <transition name="fadeIn">
                        <div
                          class="flex flex-col space-y-2 transition-all duration-300 pb-3 pt-2 text-lg"
                        >
                          <div
                            class="subject-wrapper flex md:flex-nowrap md:justify-between flex-wrap cursor-pointer"
                          >
                            <div class="subject bg-gray-1500 w-26 h-6"></div>

                            <div
                              class="time text-base text-gray-1700 bg-gray-1500 w-20 h-6"
                            ></div>
                          </div>

                          <div class="from bg-gray-1500 w-24 h-6"></div>

                          <div class="to bg-gray-1500 w-22 h-6"></div>

                          <div
                            class="text-message w-full h-6 bg-gray-1500"
                          ></div>

                          <div
                            class="text-message w-full h-6 bg-gray-1500"
                          ></div>

                          <div
                            class="text-message w-full h-6 bg-gray-1500"
                          ></div>

                          <div
                            class="text-message w-full h-6 bg-gray-1500"
                          ></div>
                        </div>
                      </transition>
                    </div>

                    <!-- End Email Body -->
                  </div>

                  <!-- End Email Content -->
                </div>
              </div>

              <!-- End Email View -->
            </div>

            <div v-else-if="loadArticles && currentComp === 'Pinterest'">
              <div class="h-full w-full animate-pulse">
                <div class="py-5 md:px-5 px-2 h-full">
                  <div
                    class="mb-11 mx-auto w-full flex flex-col justify-center items-center"
                  >
                    <div
                      class="h-6 bg-gray-1500 rounded-full dark:bg-gray-700 w-32 mb-4"
                    ></div>
                    <div
                      class="h-6 bg-gray-1500 rounded-full dark:bg-gray-700 w-48 mb-4"
                    ></div>
                  </div>
                  <div class="h-auto">
                    <div class="columns-3 space-y-2">
                      <div
                        v-for="(item, index) in pinterestItems"
                        :key="index"
                        class="break-inside-avoid"
                      >
                        <div
                          class="bg-gray-1500 dark:bg-gray-700 rounded-lg"
                          :style="{ height: item.height }"
                        ></div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div v-else-if="loadArticles && currentComp === 'TikTok'">
              <div class="h-full w-full animate-pulse">
                <div class="py-5 md:px-5 px-2 h-full">
                  <div class="mb-6 w-full flex flex-col md:ml-4">
                    <div
                      class="h-6 bg-gray-1500 rounded-full dark:bg-gray-700 w-32 mb-4"
                    ></div>
                    <div
                      class="h-6 bg-gray-1500 rounded-full dark:bg-gray-700 w-48 mb-4"
                    ></div>
                  </div>
                  <div
                    class="w-full flex flex-col justify-center md:justify-start items-center md:items-start md:ml-4"
                  >
                    <div class="flex space-x-2">
                      <div
                        class="h-6 bg-gray-1500 rounded-full dark:bg-gray-700 w-24 mb-1"
                      ></div>
                      <div
                        class="h-6 bg-gray-1500 rounded-full dark:bg-gray-700 w-24 mb-4"
                      ></div>
                    </div>
                    <div
                      class="h-2 bg-gray-1500 rounded-full dark:bg-gray-700 w-48"
                    ></div>
                  </div>
                  <div
                    class="flex flex-wrap md:justify-start justify-center md:ml-2"
                  >
                    <div v-for="(item, index) in 10" :key="index" class="">
                      <div
                        class="w-full my-3 flex flex-col space-y-3 mx-2.5 tiktok-whole-card overflow-hidden"
                      >
                        <div
                          class="bg-gray-1500 dark:bg-gray-700 rounded-lg h-full overflow-hidden relative tiktokCard"
                        ></div>
                        <div
                          class="h-5 bg-gray-1500 rounded-sm dark:bg-gray-700 w-auto"
                        ></div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </transition>

          <transition name="page" mode="out-in">
            <div v-if="showComp" class="w-full h-full">
              <component :is="currentComp" class="w-full h-full"></component>
              <!-- v-bind="currentProperties" -->
            </div>
          </transition>
        </div>
      </div>

      <!-- Body end -->
    </div>

    <!-- Small Device -->

    <div
      v-if="!isDesktop"
      id="mobile-body-wrapper"
      class="mobile-body_wrapper bg-gray-2000 md:hidden flex justify-center items-start my-0 overflow-x-hidden h-full"
      :class="[
        hideScroll || loadArticles || !showComp ? 'overflow-hidden' : 'scroll',
      ]"
    >
      <div
        id="mobile-body"
        class="middle-body_wrapper md:min-width-400 w-full relative"
        :class="[
          hideScroll ||
          loadArticles ||
          !showComp ||
          (articles && articles.items && articles.items.length === 0)
            ? 'h-full'
            : 'h-auto',
        ]"
      >
        <transition name="page">
          <div
            v-if="
              loadArticles &&
              currentComp !== 'Google' &&
              currentComp !== 'Microsoft' &&
              currentComp !== 'YouTube' &&
              currentComp !== 'Web' &&
              currentComp !== 'Pinterest' &&
              currentComp !== 'TikTok'
            "
            class="my-6 px-2 h-full animate-pulse block md:hidden"
          >
            <h2 class="bg-gray-1500 w-8 h-2"></h2>

            <h2 class="bg-gray-1500 w-20 h-2 mt-2"></h2>

            <div class="w-full bg-gray-1500 lg:h-1/4 h-52 mt-2">
              <div
                class="w-full flex flex-row items-center justify-start pl-2 space-x-3"
              >
                <div class="w-10 h-10 bg-white rounded-full"></div>

                <div class="w-full">
                  <h2 class="bg-white w-20 h-2"></h2>

                  <h2 class="bg-white w-40 h-4 mt-2"></h2>
                </div>
              </div>
            </div>

            <div class="w-full bg-gray-1500 lg:h-1/4 h-52 mt-2">
              <div
                class="w-full flex flex-row items-center justify-start p-4 space-x-3"
              >
                <div class="w-10 h-10 bg-white rounded-full"></div>

                <div class="w-full">
                  <h2 class="bg-white w-20 h-2"></h2>

                  <h2 class="bg-white w-40 h-4 mt-2"></h2>
                </div>
              </div>
            </div>

            <div class="w-full bg-gray-1500 lg:h-1/4 h-52 mt-2">
              <div
                class="w-full flex flex-row items-center justify-start p-4 space-x-3"
              >
                <div class="w-10 h-10 bg-white rounded-full"></div>

                <div class="w-full">
                  <h2 class="bg-white w-20 h-2"></h2>

                  <h2 class="bg-white w-40 h-4 mt-2"></h2>
                </div>
              </div>
            </div>
          </div>

          <div
            v-else-if="
              loadArticles &&
              (currentComp === 'Google' ||
                currentComp === 'Microsoft' ||
                currentComp === 'YouTube' ||
                currentComp === 'Web') &&
              currentComp !== 'Pinterest' &&
              currentComp !== 'TikTok'
            "
            class="w-full h-full flex md:flex-row flex-col flex-nowrap animate-pulse"
          >
            <!-- Start Email Feed Header -->

            <div
              class="transition-all duration-500 ease-in-out md:w-1/3 w-full md:h-full opacity-100"
              :class="
                currentComp === 'YouTube' ? 'h-full' : 'mobile-skeleton_height'
              "
            >
              <div
                class="bg-orange-200 flex w-full h-9 text-md font-bold text-ash-primary items-center"
              >
                <div class="px-4">
                  <div class="heading bg-gray-1500 w-26 h-5"></div>
                </div>
              </div>

              <div class="flex w-full email-view-wrapper pb-0">
                <!-- Start Email Messages -->

                <div
                  class="email-messages w-full h-full md:border-r border-yellow-300 overflow-hidden"
                >
                  <div
                    v-for="(skeleton, index) in 10"
                    :key="index"
                    class="w-full pt-4 bg-white"
                  >
                    <div
                      class="singleMessageWrapper px-4 cursor-pointer relative"
                    >
                      <div
                        class="platform text-lg font-bold bg-gray-1500 lg:space-x-0 space-x-2 whitespace-normal break-words w-26 h-8 mb-1"
                      ></div>

                      <div class="flex justify-between lg:space-x-0 space-x-2">
                        <div
                          class="heading text-sm text-ash-primary whitespace-normal break-words bg-gray-1500 w-28 h-5 mb-1"
                        ></div>

                        <div
                          class="attachment text-gray-light cursor-pointer bg-gray-1500 w-5 h-5"
                        ></div>
                      </div>

                      <div
                        class="flex text-sm justify-between lg:space-x-0 space-x-2"
                      >
                        <div
                          class="short-message bg-gray-1500 w-24 h-4 italic whitespace-normal break-words mb-1"
                        ></div>

                        <div
                          class="time text-sm bg-gray-1500 w-4 h-4 whitespace-nowrap mb-1"
                        ></div>
                      </div>
                    </div>
                  </div>

                  <div class="w-full flex justify-center py-4">
                    <div
                      class="bg-gray-1500 h-10 w-34 rounded-full relative flex justify-around space-x-1 items-center px-4 font-medium"
                    ></div>
                  </div>
                </div>

                <!-- End Email Messages -->
              </div>
            </div>

            <!-- End Email Feed Header -->

            <div class="md:hidden px-2 w-full h-px bg-gray-1500 my-2"></div>

            <!-- Start Email View -->

            <div
              class="transition-all duration-500 ease-in-out bg-white md:w-2/3 w-full md:h-full mobile-skeleton_height overflow-hidden"
            >
              <div
                class="bg-orange-200 flex w-full h-9 text-md font-bold items-center"
              >
                <div class="flex justify-between items-center px-4 w-full">
                  <div class="heading bg-gray-1500 w-26 h-5"></div>

                  <div class="extend cursor-pointer bg-gray-1500 h-5 w-5">
                    <div class="plus_button" data-title="Extend All"></div>
                  </div>
                </div>
              </div>

              <div class="flex w-full email-view-wrapper pb-0">
                <!-- Start Email Content Wrapper-->

                <div class="email-content h-full w-full px-4 pt-2">
                  <!-- Start Email Body -->

                  <div
                    v-for="(skeleton, index) in 3"
                    :key="index"
                    class="email-body"
                  >
                    <transition name="fadeIn">
                      <div
                        class="flex justify-between lg:flex-nowrap lg:space-y-0 flex-wrap space-y-1 transition-all duration-300 cursor-pointer py-1"
                      >
                        <div
                          class="name-wrapper flex space-x-2 justify-between items-center w-2/3"
                        >
                          <div
                            class="mail-name text-lg bg-gray-1500 w-26 h-6"
                          ></div>

                          <div
                            class="attachment-icon-flag cursor-pointer self-start pt-1 h-5 w-5 bg-gray-1500"
                          ></div>
                        </div>

                        <div class="date bg-gray-1500 w-20 h-6"></div>
                      </div>
                    </transition>

                    <transition name="fadeIn">
                      <div
                        class="flex flex-col space-y-2 transition-all duration-300 pb-3 pt-2 text-lg"
                      >
                        <div
                          class="subject-wrapper flex md:flex-nowrap md:justify-between flex-wrap cursor-pointer"
                        >
                          <div class="subject bg-gray-1500 w-26 h-6"></div>

                          <div
                            class="time text-base text-gray-1700 bg-gray-1500 w-20 h-6"
                          ></div>
                        </div>

                        <div class="from bg-gray-1500 w-24 h-6"></div>

                        <div class="to bg-gray-1500 w-22 h-6"></div>

                        <div class="text-message w-full h-6 bg-gray-1500"></div>

                        <div class="text-message w-full h-6 bg-gray-1500"></div>

                        <div class="text-message w-full h-6 bg-gray-1500"></div>

                        <div class="text-message w-full h-6 bg-gray-1500"></div>
                      </div>
                    </transition>
                  </div>

                  <!-- End Email Body -->
                </div>

                <!-- End Email Content -->
              </div>
            </div>

            <!-- End Email View -->
          </div>

          <div v-else-if="loadArticles && currentComp === 'Pinterest'">
            <div class="h-full w-full animate-pulse">
              <div class="py-5 md:px-5 px-2 h-full">
                <div
                  class="mb-11 mx-auto w-full flex flex-col justify-center items-center"
                >
                  <div
                    class="h-6 bg-gray-1500 rounded-full dark:bg-gray-700 w-32 mb-4"
                  ></div>
                  <div
                    class="h-6 bg-gray-1500 rounded-full dark:bg-gray-700 w-48 mb-4"
                  ></div>
                </div>
                <div class="h-auto">
                  <div class="columns-2 space-y-2">
                    <div
                      v-for="(item, index) in pinterestItems"
                      :key="index"
                      class="break-inside-avoid"
                    >
                      <div
                        class="bg-gray-1500 dark:bg-gray-700 rounded-lg"
                        :style="{ height: item.height }"
                      ></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div v-else-if="loadArticles && currentComp === 'TikTok'">
            <div class="h-full w-full animate-pulse">
              <div class="py-5 md:px-5 px-2 h-full">
                <div class="mb-6 w-full flex flex-col md:ml-4">
                  <div
                    class="h-6 bg-gray-1500 rounded-full dark:bg-gray-700 w-32 mb-4"
                  ></div>
                  <div
                    class="h-6 bg-gray-1500 rounded-full dark:bg-gray-700 w-48 mb-4"
                  ></div>
                </div>
                <div
                  class="w-full flex flex-col justify-center md:justify-start items-center md:items-start md:ml-4"
                >
                  <div class="flex space-x-2">
                    <div
                      class="h-6 bg-gray-1500 rounded-full dark:bg-gray-700 w-24 mb-1"
                    ></div>
                    <div
                      class="h-6 bg-gray-1500 rounded-full dark:bg-gray-700 w-24 mb-4"
                    ></div>
                  </div>
                  <div
                    class="h-2 bg-gray-1500 rounded-full dark:bg-gray-700 w-48"
                  ></div>
                </div>
                <div
                  class="flex flex-wrap md:justify-start justify-center md:ml-2"
                >
                  <div v-for="(item, index) in 10" :key="index" class="">
                    <div
                      class="w-full my-3 flex flex-col space-y-3 mx-2.5 tiktok-whole-card overflow-hidden"
                    >
                      <div
                        class="bg-gray-1500 dark:bg-gray-700 rounded-lg h-full overflow-hidden relative tiktokCard"
                      ></div>
                      <div
                        class="h-5 bg-gray-1500 rounded-sm dark:bg-gray-700 w-auto"
                      ></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <component :is="currentComp" v-else-if="showComp"></component>
        </transition>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { breakpointsTailwind, useBreakpoints } from '@vueuse/core'
import Facebook from './facebook/Facebook.vue'
import Twitter from './twitter/Twitter.vue'
import LinkedIn from './linkedin/Index.vue'
import Instagram from './instagram/Index.vue'
import Google from './email/Email.vue'
import Microsoft from './microsoft-mail/Email.vue'
import YouTube from './youtube-video/Youtube.vue'
import Web from './websites/Websites.vue'
import Pinterest from './pinterest/Pinterest.vue'
import Reddit from './reddit/Reddit.vue'
import TikTok from './tiktok/Tiktok.vue'
import { useStore } from 'vuex'

defineOptions({
  components: {
    Facebook,
    Twitter,
    Instagram,
    LinkedIn,
    Google,
    Microsoft,
    YouTube,
    Web,
    Pinterest,
    Reddit,
    TikTok,
  }
})

const props = defineProps({
  feedIsExpanded: {
    type: Boolean,
  },
})
interface pinterestItemsType{
  id: number,
    height: string,
}
const show = ref<boolean>(true)
const desktopScrollPosition = ref<number>(0)
const mobileScrollPosition = ref<number>(0)
const hideScroll = ref<boolean>(false)
const pinterestItems = ref<pinterestItemsType[]>([
  {
    id: 0,
    height: '240px',
  },
  {
    id: 1,
    height: '327px',
  },
  {
    id: 2,
    height: '167px',
  },
  {
    id: 3,
    height: '150px',
  },
  {
    id: 4,
    height: '250px',
  },
  {
    id: 5,
    height: '327px',
  },
  {
    id: 6,
    height: '150px',
  },
  {
    id: 7,
    height: '180px',
  },
  {
    id: 8,
    height: '210px',
  },
  {
    id: 9,
    height: '327px',
  },
])

const emit=defineEmits<{
  (event:'expand-height'):void
}>()
const store = useStore()
const breakpoints = useBreakpoints(breakpointsTailwind)
const customBreakpoints = useBreakpoints({
  redditFeeds: 1024,
})

const isDesktop = breakpoints.greaterOrEqual('md')
const redditDevice = customBreakpoints.greaterOrEqual('redditFeeds')

const loggedIn = computed(() => store.state.auth.loggedIn)
const articles = computed(() => store.state.home.articles)
const loadArticles = computed(() => store.state.home.loadArticles)

const showComp = computed(() => store.state.home.showComp)
const selectedWebArchiveDate = computed(
  () => store.state.home.selectedWebArchiveDate,
)
const initialWebArchiveDate = computed(
  () => store.state.home.initialWebArchiveDate,
)

const showSinglePost = computed(() => store.state.socialFeed.showSinglePost)
const showSingleImagePost = computed(
  () => store.state.socialFeed.showSingleImagePost,
)
const previousSinglePost = computed(
  () => store.state.socialFeed.previousSinglePost,
)
const showTwitter = computed(() => store.state.socialFeed.showTwitter)

const currentComp = computed(() => store.getters['home/currentComp']);


// watch
watch(
  () => loadArticles.value,
  (data) => {
    if (!data) {
      setTimeout(() => {
        store.commit('home/SET_SHOW_COMP', true)
      }, 2000)
    } else {
      store.commit('home/SET_SHOW_COMP', false)
    }
  },
)

watch(
  () => currentComp.value,
  (data) => {
    if (data) {
      scrollTop()
      fullScreen()
    }
  },
)
watch(
  () => previousSinglePost.value,
  (data) => {
    if (data) {
      scrollTop()
    }
  },
)

watch(
  () => showSinglePost.value,
  (data) => {
    if (currentComp.value !== 'Twitter') {
      if (data) {
        removeScrollListener()
        setTimeout(() => {
          show.value = false
          scrollTop()
        }, 500)
      } else {
        setTimeout(() => {
          show.value = true
          setTimeout(() => {
            setScrollPosition()
            addScrollListener()
          })
        }, 300)
      }
    }
  },
)
watch(
  () => showSingleImagePost.value,
  (data) => {
    if (currentComp.value !== 'Twitter') {
      if (data) {
        removeScrollListener()
        if (isDesktop.value) {
          const height =
            document.getElementById('desktop-body-wrapper').clientHeight - 32
          store.dispatch('home/getHeight', height)
        } else {
          let mobileHeight
          document.querySelectorAll('.mobile-body_wrapper').forEach((item) => {
            mobileHeight = item.clientHeight - 32
          })
          store.dispatch('home/getMobileHeight', mobileHeight)
        }
        setTimeout(() => {
          show.value = false
          scrollTop()
        }, 500)
      } else if (!data) {
        if (currentComp.value !== 'Pinterest') {
          setTimeout(() => {
            show.value = true
            if (isDesktop.value) {
              if (document.getElementById('desktop-body-wrapper') as HTMLElement | null) {
                document.getElementById('desktop-body-wrapper').scrollTop = 0
              }
            }
            setTimeout(() => {
              setScrollPosition()
              addScrollListener()
            })
          }, 300)
        } else if (currentComp.value === 'Pinterest') {
          setTimeout(() => {
            show.value = true
            if (isDesktop.value) {
              if (document.getElementById('desktop-body-wrapper') as HTMLElement | null) {
                document.getElementById('desktop-body-wrapper').scrollTop = 0
              }
            }
            setTimeout(() => {
              setScrollPosition()
              addScrollListener()
            })
          }, 1000)
        } else {
          setTimeout(() => {
            show.value = true
            if (isDesktop.value) {
              if (document.getElementById('desktop-body-wrapper') as HTMLElement | null) {
                document.getElementById('desktop-body-wrapper').scrollTop = 0
              }
            }
            setTimeout(() => {
              setScrollPosition()
              addScrollListener()
            })
          }, 470)
        }
      }
    } else if (currentComp.value === 'Twitter') {
      if (data) {
        if (isDesktop.value) {
          const height =
            document.getElementById('desktop-body-wrapper')?.clientHeight - 32
          store.dispatch('home/getHeight', height)
        } else {
          let mobileHeight
          document.querySelectorAll('.mobile-body_wrapper').forEach((item) => {
            mobileHeight = item.clientHeight - 32
          })
          store.dispatch('home/getMobileHeight', mobileHeight)
        }
      }
    }
  },
)

watch(
  () => showTwitter.value,
  (data) => {
    if (!data) {
      removeScrollListener()
      if (showSingleImagePost.value) {
        if (isDesktop.value) {
          const height =
            document.getElementById('desktop-body-wrapper').clientHeight - 32
          store.dispatch('home/getHeight', height)
        } else {
          let mobileHeight
          document.querySelectorAll('.mobile-body_wrapper').forEach((item) => {
            mobileHeight = item.clientHeight - 32
          })
          store.dispatch('home/getMobileHeight', mobileHeight)
        }
      }
      setTimeout(() => {
        show.value = false
        scrollTop()
      }, 450)
    } else {
      setTimeout(() => {
        show.value = true
        if (showSingleImagePost.value) {
          if (isDesktop.value) {
            if (document.getElementById('desktop-body-wrapper')) {
              document.getElementById('desktop-body-wrapper').scrollTop = 0
            }
          }
        }
        setTimeout(() => {
          setScrollPosition()
          addScrollListener()
        })
      }, 100)
    }
  },
)

onMounted(() => {
  fullScreen()
  addScrollListener()
})
onUnmounted(() => {
  removeScrollListener()
})
// methods
const expand = () => {
  emit('expand-height')
}
const handleScroll = (event) => {
  const element = event.target
  let value
  if (isDesktop.value) {
    if (document.getElementById('desktop-body-wrapper') as HTMLElement | null) {
      value = document.getElementById('desktop-body-wrapper').scrollTop
    }
  } else {
    value = document.getElementById('mobile-body-wrapper').scrollTop
  }
  const mobileValue = element.scrollTop
  desktopScrollPosition.value = value
  localStorage.setItem('scrollPosition', desktopScrollPosition.value)
  mobileScrollPosition.value = mobileValue
  localStorage.setItem('scrollMobilePosition', mobileScrollPosition.value)
}
const fullScreen = () => {
  if (
    currentComp.value === 'Google' ||
    currentComp.value === 'Microsoft' ||
    currentComp.value === 'Faxes' ||
    currentComp.value === 'Calls' ||
    currentComp.value === 'Texts' ||
    currentComp.value === 'Websites' ||
    currentComp.value === 'YouTube' ||
    currentComp.value === 'Web'
  ) {
    show.value = true
    hideScroll.value = true
  } else {
    setTimeout(() => {
      hideScroll.value = false
      show.value = true
    }, 500)
  }
}
const scrollTop = () => {
  if (isDesktop.value) {
    if (document.getElementById('desktop-body-wrapper') as HTMLElement | null) {
      document.getElementById('desktop-body-wrapper').scrollTop = 0
    }
  } else {
    document.querySelectorAll('.mobile-body_wrapper').forEach((item) => {
      item.scrollTop = 0
    })
  }
}
const addScrollListener = () => {
  if (isDesktop.value) {
    setTimeout(() => {
      if (document.getElementById('desktop-body-wrapper')) {
        document
          .getElementById('desktop-body-wrapper')
          ?.addEventListener('scroll', handleScroll)
      }
    }, 1000)
  } else {
    setTimeout(() => {
      document.querySelectorAll('.mobile-body_wrapper').forEach((item) => {
        item?.addEventListener('scroll', handleScroll)
      })
    }, 1000)
  }
}
const removeScrollListener = () => {
  if (isDesktop.value) {
    document
      .getElementById('desktop-body-wrapper')
      .removeEventListener('scroll', handleScroll)
  } else {
    document.querySelectorAll('.mobile-body_wrapper').forEach((item) => {
      item.removeEventListener('scroll', handleScroll)
    })
  }
}
const setScrollPosition = () => {
  if (isDesktop.value) {
    document.getElementById('desktop-body-wrapper').scrollTop =
      localStorage.getItem('scrollPosition')
  } else {
    document.querySelectorAll('.mobile-body_wrapper').forEach((item) => {
      item.scrollTop = localStorage.getItem('scrollMobilePosition')
    })
  }
}


</script>

<style lang="scss" scoped>
.columns-2 {
  columns: 2;
}
.columns-3 {
  columns: 3;
}
.break-inside-avoid {
  break-inside: avoid;
  img {
    @apply rounded-lg;
  }
}
.tiktok-whole-card {
  max-width: 200px;
  min-width: 200px;
  width: 200px;
}
.tiktokCard {
  box-shadow: 0px 1px 2px #22283126;
  border-radius: 5px;
  max-width: 200px;
  min-width: 200px;
  width: 200px;
  height: 270px;
  max-height: 270px;
  min-height: 270px;
}

@media (max-width: 767px) {
  .tiktok-whole-card {
    max-width: 250px;
    min-width: 250px;
    width: 250px;
  }
  .tiktokCard {
    max-width: 250px;
    min-width: 250px;
    width: 250px;
    height: 320px;
    max-height: 320px;
    min-height: 320px;
  }
}
.select-toggle {
  color: #ffffff;
  // top: 5.2px;
  // @apply absolute right-3 pointer-events-none;
  svg {
    @apply text-3xl;
  }
  // padding-left: 2px !important;
}
.icon-position {
  position: absolute;
  right: 16px !important;
  top: 5px !important;
}
@media (min-width: 1360px) {
  .icon-position {
    top: 7px !important;
  }
}
.mobile-body_wrapper {
  // height: calc(100% - 56px);
}
.title {
  background-color: #e4801d;
  @apply text-center;
}
.real-time-feed-body_wrapper {
  @apply flex justify-center items-start w-full;
  height: calc(100% - 44px);
}
.left-side-body_wrapper {
  @apply col-span-1 py-6 pt-2 overflow-y-auto;
}
.footer {
  margin-top: 230px !important;
}

.min-width-2 {
  min-width: 2.5rem;
}

.max-width-690 {
  max-width: 690px;
}

.max-width-720 {
  max-width: 720px;
}

.w-44_rem {
  width: 44.3rem;
}
.min-width-400 {
  // min-width: 400px;
  min-width: 350px;
}
.min-width-690 {
  min-width: 690px;
}
.max-width-full {
  min-width: 100%;
  width: 690px;
}
.scroll {
  overflow-y: auto;
  overflow-x: hidden;
  // scroll-behavior: smooth;
  -ms-overflow-style: none; /* IE 11 */
  scrollbar-width: thin;
  scrollbar-color: #ff8308 #ececec; /* Firefox 64 */
  &::-webkit-scrollbar {
    width: 6px;
  }

  /* Track */
  &::-webkit-scrollbar-track {
    border-radius: 3px;
    background: #ececec;
  }

  /* Handle */
  &::-webkit-scrollbar-thumb {
    background: #ff8308;
    border-radius: 3px;
  }

  /* Handle on hover */
  &::-webkit-scrollbar-thumb:hover {
    background: #ff8308;
  }
}

@media (max-width: 767px) {
  .mobile-height {
    height: calc(100% - 50px);
  }
  .mobile-skeleton_height {
    height: calc(50% - 17px);
  }
}

.email-view-wrapper {
  height: calc(100% - 36px);
}
@media (max-width: 392px) {
  .mobile-height {
    height: calc(100% - 80px);
  }
}
</style>
