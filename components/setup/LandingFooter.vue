<template>
  <footer role="contentinfo" @click.stop="closeStarterModal()">
    <div
      class="footer-body"
      :class="[
        route.name !== 'seo' ? 'pb-4' : 'pb-[130px]',
        route.name === 'set_appointment' ? 'bg-white !pt-20' : 'bg-white',
      ]"
    >
      <div class="footer_menu">
        <!-- Communities Footer Menu -->
        <div class="footer_menu_item min-width-12 block lg:block 2xl:block">
          <p class="font-bold pt-0 pb-2 text-xl text-gray-700">Communities</p>
          <p>
            <NuxtLink
              class="text-yellow-primary cursor-pointer text-lg lg:py-0 py-0 opacity-1"
              to="/finance"
              @click.native="
                store.commit(
                  'community/GET_SINGLE_COMMUNITY_CONTENT',
                  'finance',
                )
              "
            >
              Finance
            </NuxtLink>
          </p>
          <p v-if="$config.public.workflow === 'dev'">
            <nuxt-link
              class="text-yellow-primary cursor-pointer text-lg lg:py-0 py-0 opacity-1"
              to="/finance-boston"
              @click.native="
                store.commit(
                  'community/GET_SINGLE_COMMUNITY_CONTENT',
                  'finance-boston',
                )
              "
            >
              Finance Boston
            </nuxt-link>
          </p>
          <p v-if="$config.public.workflow === 'dev'">
            <nuxt-link
              class="text-yellow-primary cursor-pointer text-lg lg:py-0 py-0 opacity-1"
              to="/finance-sacramento"
              @click.native="
                store.commit(
                  'community/GET_SINGLE_COMMUNITY_CONTENT',
                  'finance-sacramento',
                )
              "
            >
              Finance Sacramento
            </nuxt-link>
          </p>
          <p>
            <NuxtLink
              class="text-yellow-primary cursor-pointer text-lg lg:py-0 py-0 opacity-1"
              to="/publicSchools"
              @click.native="
                store.commit(
                  'community/GET_SINGLE_COMMUNITY_CONTENT',
                  'publicSchools',
                )
              "
            >
              Public Schools
            </NuxtLink>
          </p>
          <p v-if="$config.public.workflow === 'dev'">
            <NuxtLink
              class="text-yellow-primary cursor-pointer text-lg lg:py-0 py-0 opacity-1"
              to="/public-schools-boston"
              @click.native="
                store.commit(
                  'community/GET_SINGLE_COMMUNITY_CONTENT',
                  'public-schools-boston',
                )
              "
            >
              Public Schools Boston
            </NuxtLink>
          </p>
          <p v-if="$config.public.workflow === 'dev'">
            <NuxtLink
              class="text-yellow-primary cursor-pointer text-lg lg:py-0 py-0 opacity-1"
              to="/public-schools-sacramento"
              @click.native="
                store.commit(
                  'community/GET_SINGLE_COMMUNITY_CONTENT',
                  'public-schools-sacramento',
                )
              "
            >
              Public Schools Sacramento
            </NuxtLink>
          </p>
          <p>
            <nuxt-link
              class="text-yellow-primary cursor-pointer text-lg lg:py-0 py-0 opacity-1"
              to="/governmentAgencies"
              @click.native="
                store.commit(
                  'community/GET_SINGLE_COMMUNITY_CONTENT',
                  'governmentAgencies',
                )
              "
              >Government Agencies</nuxt-link
            >
          </p>
          <p v-if="$config.public.workflow === 'dev'">
            <nuxt-link
              class="text-yellow-primary cursor-pointer text-lg lg:py-0 py-0 opacity-1"
              to="/government-agencies-boston"
              @click.native="
                store.commit(
                  'community/GET_SINGLE_COMMUNITY_CONTENT',
                  'government-agencies-boston',
                )
              "
              >Government Agencies Boston</nuxt-link
            >
          </p>
          <p v-if="$config.public.workflow === 'dev'" class="min-width-12">
            <nuxt-link
              class="text-yellow-primary cursor-pointer text-lg lg:py-0 py-0 opacity-1"
              to="/government-agencies-sacramento"
              @click.native="
                store.commit(
                  'community/GET_SINGLE_COMMUNITY_CONTENT',
                  'government-agencies-sacramento',
                )
              "
              >Government Agencies Sacramento</nuxt-link
            >
          </p>
          <p>
            <nuxt-link
              class="text-yellow-primary cursor-pointer text-lg lg:py-0 py-0 opacity-1"
              to="/citiesTowns"
              @click.native="
                store.commit(
                  'community/GET_SINGLE_COMMUNITY_CONTENT',
                  'citiesTowns',
                )
              "
              >Cities and Towns</nuxt-link
            >
          </p>
          <p v-if="$config.public.workflow === 'dev'">
            <nuxt-link
              class="text-yellow-primary cursor-pointer text-lg lg:py-0 py-0 opacity-1"
              to="/cities-towns-boston"
              @click.native="
                store.commit(
                  'community/GET_SINGLE_COMMUNITY_CONTENT',
                  'cities-towns-boston',
                )
              "
              >Cities and Towns Boston</nuxt-link
            >
          </p>
          <p v-if="$config.public.workflow === 'dev'">
            <nuxt-link
              class="text-yellow-primary cursor-pointer text-lg lg:py-0 py-0 opacity-1"
              to="/cities-towns-sacramento"
              @click.native="
                store.commit(
                  'community/GET_SINGLE_COMMUNITY_CONTENT',
                  'cities-towns-sacramento',
                )
              "
              >Cities and Towns Sacramento</nuxt-link
            >
          </p>
          <p>
            <nuxt-link
              class="text-yellow-primary cursor-pointer text-lg lg:py-0 py-0 opacity-1"
              to="/policeStations"
              @click.native="
                store.commit(
                  'community/GET_SINGLE_COMMUNITY_CONTENT',
                  'policeStations',
                )
              "
              >Police Stations</nuxt-link
            >
          </p>
          <p v-if="$config.public.workflow === 'dev'">
            <nuxt-link
              class="text-yellow-primary cursor-pointer text-lg lg:py-0 py-0 opacity-1"
              to="/police-stations-boston"
              @click.native="
                store.commit(
                  'community/GET_SINGLE_COMMUNITY_CONTENT',
                  'police-stations-boston',
                )
              "
              >Police Stations Boston</nuxt-link
            >
          </p>
          <p v-if="$config.public.workflow === 'dev'">
            <nuxt-link
              class="text-yellow-primary cursor-pointer text-lg lg:py-0 py-0 opacity-1"
              to="/police-stations-sacramento"
              @click.native="
                store.commit(
                  'community/GET_SINGLE_COMMUNITY_CONTENT',
                  'police-stations-sacramento',
                )
              "
              >Police Stations Sacramento</nuxt-link
            >
          </p>
          <p>
            <nuxt-link
              class="text-yellow-primary cursor-pointer text-lg lg:py-0 py-0 opacity-1"
              to="/investmentFirms"
              @click.native="
                store.commit(
                  'community/GET_SINGLE_COMMUNITY_CONTENT',
                  'investmentFirms',
                )
              "
              >Investment Firms</nuxt-link
            >
          </p>
          <p v-if="$config.public.workflow === 'dev'">
            <nuxt-link
              class="text-yellow-primary cursor-pointer text-lg lg:py-0 py-0 opacity-1"
              to="/investment-firms-boston"
              @click.native="
                store.commit(
                  'community/GET_SINGLE_COMMUNITY_CONTENT',
                  'investment-firms-boston',
                )
              "
              >Investment Firms Boston</nuxt-link
            >
          </p>
          <p>
            <nuxt-link
              class="text-yellow-primary cursor-pointer text-lg lg:py-0 py-0 opacity-1"
              to="/collegesUniversities"
              @click.native="
                store.commit(
                  'community/GET_SINGLE_COMMUNITY_CONTENT',
                  'collegesUniversities',
                )
              "
              >Colleges and Universities</nuxt-link
            >
          </p>
          <p v-if="$config.public.workflow === 'dev'" class="min-width-12">
            <nuxt-link
              class="text-yellow-primary cursor-pointer text-lg lg:py-0 py-0 opacity-1"
              to="/colleges-universities-boston"
              @click.native="
                store.commit(
                  'community/GET_SINGLE_COMMUNITY_CONTENT',
                  'colleges-universities-boston',
                )
              "
              >Colleges and Universities Boston</nuxt-link
            >
          </p>
          <p>
            <nuxt-link
              class="text-yellow-primary cursor-pointer text-lg lg:py-0 py-0 opacity-1"
              to="/fireStations"
              @click.native="
                store.commit(
                  'community/GET_SINGLE_COMMUNITY_CONTENT',
                  'fireStations',
                )
              "
              >Fire Stations</nuxt-link
            >
          </p>
          <p v-if="$config.public.workflow === 'dev'">
            <nuxt-link
              class="text-yellow-primary cursor-pointer text-lg lg:py-0 py-0 opacity-1"
              to="/fire-stations-boston"
              @click.native="
                store.commit(
                  'community/GET_SINGLE_COMMUNITY_CONTENT',
                  'fire-stations-boston',
                )
              "
              >Fire Stations Boston</nuxt-link
            >
          </p>
          <p v-if="$config.public.workflow === 'dev'">
            <nuxt-link
              class="text-yellow-primary cursor-pointer text-lg lg:py-0 py-0 opacity-1"
              to="/email-archiving-finance"
              @click.native="
                store.commit(
                  'community/GET_SINGLE_COMMUNITY_CONTENT',
                  'email-archiving-finance',
                )
              "
              >Email Archiving Finance</nuxt-link
            >
          </p>
          <p v-if="$config.public.workflow === 'dev'" class="min-width-12">
            <nuxt-link
              class="text-yellow-primary cursor-pointer text-lg lg:py-0 py-0 opacity-1"
              to="/website-archiving-government-agencies"
              @click.native="
                store.commit(
                  'community/GET_SINGLE_COMMUNITY_CONTENT',
                  'website-archiving-government-agencies',
                )
              "
              >Website Archiving Government Agencies</nuxt-link
            >
          </p>
        </div>
        <!-- Archive Footer Menu -->
        <div
          class="footer_menu_item min-width-12 block lg:block 2xl:block padding-left-middle"
        >
          <p class="font-bold pt-6 lg:pt-0 pb-2 text-xl text-gray-700">
            Archive
          </p>
          <p>
            <nuxt-link
              class="text-yellow-primary cursor-pointer text-lg lg:py-0 py-0 opacity-1"
              to="/feeds/facebook"
            >
              Facebook
            </nuxt-link>
          </p>
          <p>
            <NuxtLink
              class="text-yellow-primary cursor-pointer text-lg lg:py-0 py-0 opacity-1"
              to="/feeds/instagram"
            >
              Instagram
            </NuxtLink>
          </p>
          <p>
            <nuxt-link
              class="text-yellow-primary cursor-pointer text-lg lg:py-0 py-0 opacity-1"
              to="/feeds/twitter"
              >X (Twitter)</nuxt-link
            >
          </p>
          <p>
            <nuxt-link
              class="text-yellow-primary cursor-pointer text-lg lg:py-0 py-0 opacity-1"
              to="/feeds/linkedin"
              >LinkedIn</nuxt-link
            >
          </p>
          <p>
            <nuxt-link
              class="text-yellow-primary cursor-pointer text-lg lg:py-0 py-0 opacity-1"
              to="/feeds/microsoftmail"
              >Microsoft</nuxt-link
            >
          </p>
          <p>
            <nuxt-link
              class="text-yellow-primary cursor-pointer text-lg lg:py-0 py-0 opacity-1"
              to="/feeds/gmail"
              >Gmail</nuxt-link
            >
          </p>
          <p>
            <nuxt-link
              class="text-yellow-primary cursor-pointer text-lg lg:py-0 py-0 opacity-1"
              to="/feeds/youtube"
              >YouTube</nuxt-link
            >
          </p>
          <p>
            <nuxt-link
              class="text-yellow-primary cursor-pointer text-lg lg:py-0 py-0 opacity-1"
              to="/feeds/website"
              >Website</nuxt-link
            >
          </p>
          <p>
            <nuxt-link
              class="text-yellow-primary cursor-pointer text-lg lg:py-0 py-0 opacity-1"
              to="/feeds/pinterest"
              >Pinterest</nuxt-link
            >
          </p>
          <p>
            <nuxt-link
              class="text-yellow-primary cursor-pointer text-lg lg:py-0 py-0 opacity-1"
              to="/feeds/reddit"
              >Reddit</nuxt-link
            >
          </p>
          <p>
            <nuxt-link
              class="text-yellow-primary cursor-pointer text-lg lg:py-0 py-0 opacity-1"
              to="/feeds/tiktok"
              >TikTok</nuxt-link
            >
          </p>
        </div>
        <!-- Contact Us Footer Menu -->
        <div
          class="footer_menu_item min-width-12 hidden lg:block 2xl:hidden padding-left-right"
        >
          <p class="font-bold pt-6 lg:pt-0 pb-2 text-xl text-gray-700">
            Contact Us
          </p>
          <div>
            <a :href="`tel:3033359577`" class="text-yellow-primary text-lg">
              (303)&nbsp;335&#8209;9577
            </a>
          </div>
          <div class="relative lg:-ml-[38px] bg-white">
            <a
              :href="`mailto:<EMAIL>`"
              class="text-yellow-primary text-lg lg:pl-[38px]"
            >
              <EMAIL>
            </a>
          </div>
          <div class="lg:mt-0 mt-0 relative lg:-ml-[38px] bg-white">
            <ul class="flex justify-start lg:pl-[38px]">
              <li class="m-1 lg:ml-0">
                <a
                  href="https://www.facebook.com/SharpArchive"
                  target="_blank"
                  rel="noopener noreferrer"
                  aria-label="facebook"
                >
                  <SharedIconFacebookIcon
                    :width="'1.75rem'"
                    :height="'1.75rem'"
                    class="social_icon mx-2 lg:mx-0"
                  ></SharedIconFacebookIcon>
                </a>
              </li>

              <li class="m-1">
                <a
                  href="https://twitter.com/Sharp_Archive"
                  target="_blank"
                  rel="noopener noreferrer"
                  aria-label="twitter"
                >
                  <div class="social_icon mx-2 lg:mx-0 twitter-icon"></div>
                </a>
              </li>

              <li class="m-1">
                <a
                  href="https://www.linkedin.com/company/sharp-archive"
                  rel="noopener noreferrer"
                  target="_blank"
                  aria-label="linkedin"
                >
                  <SharedIconLinkedinIcon
                    :width="'1.75rem'"
                    :height="'26.67px'"
                    class="social_icon mx-2 lg:mx-0"
                  ></SharedIconLinkedinIcon>
                </a>
              </li>

              <li class="m-1">
                <a
                  href="https://www.instagram.com/sharparchive/"
                  rel="noopener noreferrer"
                  target="_blank"
                  aria-label="instagram"
                >
                  <SharedIconInstagramWebpIcon
                    class="social_icon mx-2 lg:mx-0"
                  />
                </a>
              </li>
              <li class="m-1 bg-red-soft rounded-full w-7 h-7 mx-2 lg:mx-1">
                <a
                  href="https://www.youtube.com/@sharp_archive"
                  target="_blank"
                  rel="noopener noreferrer"
                  aria-label="youtube"
                >
                  <SharedIconYoutubeCircleIcon
                    class="social_icon lg:mx-0"
                  ></SharedIconYoutubeCircleIcon>
                </a>
              </li>
              <li class="m-1">
                <a
                  href="https://www.reddit.com/user/Sharp_Archive/"
                  target="_blank"
                  rel="noopener noreferrer"
                  aria-label="reddit"
                >
                  <SharedIconRedditIcon
                    class="social_icon mx-2 lg:mx-0"
                  ></SharedIconRedditIcon>
                </a>
              </li>
            </ul>
          </div>
          <iframe
            class="footer-form"
            src="https://share.hsforms.com/1ITLw-QUIS0CzILcOEeiuXwd7410"
            frameborder="0"
            title="Hubspot Contact Form"
          ></iframe>
        </div>
        <!-- Product Menu lg:hidden 2xl:block-->
        <div class="footer_menu_item min-width-12 block 2xl:mt-0 second-row">
          <div class="footer_menu_item min-width-12">
            <p class="font-bold pt-6 lg:pt-0 pb-2 text-xl text-gray-700">
              Product
            </p>
            <p
              class="text-yellow-primary cursor-pointer text-lg lg:py-0 py-0 opacity-1"
              @click="gotoTryDemoSite()"
            >
              See it Work
            </p>
            <p>
              <nuxt-link
                class="text-yellow-primary cursor-pointer text-lg lg:py-0 py-0 opacity-1"
                to="/status"
                >Service Report</nuxt-link
              >
            </p>
          </div>
          <!-- Resources Footer Menu -->
          <div class="footer_menu_item min-width-12 block lg:!mt-5 mt-0">
            <p class="font-bold pt-6 lg:pt-0 pb-2 text-xl text-gray-700">
              Resources
            </p>
            <a href="https://sharparchive.com/blog/">
              <p class="text-yellow-primary text-lg lg:py-0 py-0 opacity-1">
                Blog
              </p>
            </a>
            <NuxtLink to="/release-notes">
              <p class="text-yellow-primary text-lg lg:py-0 py-0 opacity-1">
                Release Notes
              </p>
            </NuxtLink>
            <NuxtLink to="/industries">
              <p class="text-yellow-primary text-lg lg:py-0 py-0 opacity-1">
                Industries
              </p>
            </NuxtLink>
          </div>
        </div>
        <!-- Company Footer Menu block lg:hidden-->
        <div
          class="footer_menu_item min-width-12 block 2xl:mt-0 second-row padding-left-middle"
        >
          <p class="font-bold pt-6 lg:pt-0 pb-2 text-xl text-gray-700">
            Company
          </p>
          <NuxtLink to="/about-us">
            <p class="text-yellow-primary text-lg lg:py-0 py-0 opacity-1">
              About Us
            </p>
          </NuxtLink>
          <NuxtLink to="/faq">
            <p class="text-yellow-primary text-lg lg:py-0 py-0 opacity-1">
              FAQ
            </p>
          </NuxtLink>
          <nuxt-link to="/appointment">
            <p class="text-yellow-primary text-lg lg:py-0 py-0 opacity-1">
              Book a Demo
            </p>
          </nuxt-link>
          <nuxt-link to="/contact">
            <p class="text-yellow-primary text-lg lg:py-0 py-0 opacity-1">
              Contact
            </p>
          </nuxt-link>
          <nuxt-link to="/partner-program">
            <p class="text-yellow-primary text-lg lg:py-0 py-0 opacity-1">
              Partner Program
            </p>
          </nuxt-link>
          <nuxt-link to="/community/getting-started">
            <p class="text-yellow-primary text-lg lg:py-0 py-0 opacity-1">
              Community
            </p>
          </nuxt-link>
          <NuxtLink
            to="https://partner.sharparchive.com/login/"
            target="_blank"
          >
            <p class="text-yellow-primary text-lg lg:py-0 py-0 opacity-1">
              Agent Portal Login
            </p>
          </NuxtLink>
        </div>
        <!-- Address Footer Menu -->
        <div
          class="footer_menu_item min-width-12 block 2xl:mt-0 second-row padding-left-right"
        >
          <p class="font-bold pt-6 lg:pt-0 pb-2 text-xl text-gray-700">
            Addresses
          </p>
          <div
            class="flex flex-col space-y-5 text-yellow-primary text-lg lg:py-0 py-0 relative"
          >
            <div class="flex space-x-2">
              <SharedIconUsaFlag class="mt-[3px]" />
              <div>
                <p class="font-medium">United States Headquarters</p>
                <p>6494 S Quebec St</p>
                <p>Centennial, CO 80121</p>
              </div>
            </div>
            <NuxtLink to="/trinax">
              <div class="flex space-x-2">
                <SharedIconSingaporeFlag class="mt-[3px] lg:block hidden" />
                <div>
                  <div class="flex">
                    <SharedIconSingaporeFlag class="mt-[3px] lg:hidden mr-2" />
                    <p class="font-medium">Singapore Headquarters</p>
                  </div>
                  <p>68 Kallang Pudding Road</p>
                  <p>#01-01 SYH Logistics Building</p>
                  <p>Singapore 349327</p>
                </div>
              </div>
            </NuxtLink>
          </div>
        </div>
        <!-- Contact Us Footer Menu -->
        <div
          class="footer_menu_item min-width-12 block lg:hidden 2xl:block padding-left-right"
        >
          <p class="font-bold pt-6 lg:pt-0 pb-2 text-xl text-gray-700">
            Contact Us
          </p>
          <div>
            <a :href="`tel:3033359577`" class="text-yellow-primary text-lg">
              (303)&nbsp;335&#8209;9577
            </a>
          </div>
          <div class="relative lg:-ml-[38px] bg-white">
            <a
              :href="`mailto:<EMAIL>`"
              class="text-yellow-primary text-lg lg:pl-[38px]"
              aria-label="<EMAIL>"
            >
              <EMAIL>
            </a>
          </div>
          <div class="lg:mt-0 mt-0 relative lg:-ml-[38px] bg-white">
            <ul class="flex justify-start lg:pl-[38px]">
              <li class="m-1 lg:ml-0">
                <a
                  href="https://www.facebook.com/SharpArchive"
                  target="_blank"
                  rel="noopener noreferrer"
                  aria-label="facebook"
                >
                  <!-- <SharedIconFacebookIcon
                    :width="'1.75rem'"
                    :height="'1.75rem'"
                    class="social_icon mx-2 lg:mx-0"
                  ></SharedIconFacebookIcon> -->
                  <NuxtImg
                    loading="lazy"
                    format="webp"
                    width="28"
                    height="28"
                    class="mx-2 lg:mx-0"
                    src="/images/icon/FacebookIcon/<EMAIL>"
                    alt=""
                  />
                </a>
              </li>

              <li class="m-1">
                <a
                  href="https://twitter.com/Sharp_Archive"
                  target="_blank"
                  rel="noopener noreferrer"
                  aria-label="twitter"
                >
                  <div class="social_icon mx-2 lg:mx-0 twitter-icon"></div>
                </a>
              </li>

              <li class="m-1">
                <a
                  href="https://www.linkedin.com/company/sharp-archive"
                  rel="noopener noreferrer"
                  target="_blank"
                  aria-label="linkedin"
                >
                  <!-- <SharedIconLinkedinIcon
                    :width="'1.75rem'"
                    :height="'26.67px'"
                    class="social_icon mx-2 lg:mx-0"
                  ></SharedIconLinkedinIcon> -->
                  <NuxtImg
                    loading="lazy"
                    format="webp"
                    width="28"
                    height="27"
                    class="mx-2 lg:mx-0"
                    src="/images/icon/LinkedInIcon/<EMAIL>"
                    alt=""
                  />
                </a>
              </li>

              <li class="m-1">
                <a
                  href="https://www.instagram.com/sharparchive/"
                  rel="noopener noreferrer"
                  target="_blank"
                  aria-label="instagram"
                >
                  <!-- <SharedIconInstagramWebpIcon
                    class="social_icon mx-2 lg:mx-0"
                  /> -->
                  <NuxtImg
                    loading="lazy"
                    format="webp"
                    width="28"
                    height="28"
                    class="mx-2 lg:mx-0"
                    src="/images/logo/<EMAIL>"
                    alt=""
                  />
                </a>
              </li>
              <li
                class="m-1 lg:ml-0 bg-red-soft w-7 h-7 mx-2 lg:mx-1 rounded-full"
              >
                <a
                  href="https://www.youtube.com/@sharp_archive"
                  target="_blank"
                  rel="noopener noreferrer"
                  aria-label="youtube"
                >
                  <!-- <SharedIconYoutubeCircleIcon
                    class="social_icon lg:mx-0"
                  ></SharedIconYoutubeCircleIcon> -->
                  <NuxtImg
                    loading="lazy"
                    format="webp"
                    width="28"
                    height="28"
                    class="lg:mx-0"
                    src="/images/png/youtube_social_circle_red.png"
                    alt=""
                  />
                </a>
              </li>
              <li class="m-1 lg:ml-0">
                <a
                  href="https://www.reddit.com/user/Sharp_Archive/"
                  target="_blank"
                  rel="noopener noreferrer"
                  aria-label="reddit"
                >
                  <!-- <SharedIconRedditIcon
                    class="social_icon mx-2 lg:mx-0"
                  ></SharedIconRedditIcon> -->
                  <NuxtImg
                    loading="lazy"
                    format="webp"
                    width="28"
                    height="28"
                    class="mx-2 lg:mx-0"
                    src="/images/icon/RedditIcon/reddit-for-sa.webp"
                    alt=""
                  />
                </a>
              </li>
            </ul>
          </div>
          <iframe
            class="footer-form"
            src="https://share.hsforms.com/1ITLw-QUIS0CzILcOEeiuXwd7410"
            frameborder="0"
            title="Hubspot Contact Form"
          ></iframe>
        </div>
      </div>
      <div class="w-full h-0.5 my-4 bg-gray-1700"></div>
      <div class="footer_bottom_menu">
        <p class="footer_bottom_menu_item">
          &copy; Sharp Archive {{ year }}
          <a
            class="hidden"
            href="https://devxhub.com/"
            target="_blank"
            rel="noopener noreferrer"
            aria-label="Develop By Developer eXperience Hub"
            >Develop By Developer eXperience Hub</a
          >
        </p>
        <p class="footer_bottom_menu_item">
          <NuxtLink to="/terms-and-conditions"> Terms & Conditions </NuxtLink>
        </p>
        <p class="footer_bottom_menu_item">
          <NuxtLink to="/terms-of-service"> Terms of Service </NuxtLink>
        </p>
        <p class="footer_bottom_menu_item">
          <NuxtLink to="/privacy-policy"> Privacy Policy </NuxtLink>
        </p>
      </div>
      <div
        class="md:hidden flex justify-center items-center pt-20"
        @click="topFunction()"
      >
        <SharedIconUpArrowIcon />
      </div>
    </div>
  </footer>
</template>

<script setup lang="ts">
import { useStore } from 'vuex'

const store = useStore()
const route = useRoute()
const year = computed(() => new Date().getFullYear())
const starterExpanded = computed(() => store.state.starterExpanded)

const disableSecurityPaymentTab = () =>
  store.dispatch('feeds/disableSecurityPaymentTab')
const setStorageFormatSelect = (value) =>
  store.dispatch('setStorageFormatSelect', value)
const maximizeStarterModal = (value) =>
  store.dispatch('maximize_starter_modal', value)
const expandStarterModal = (value) =>
  store.dispatch('expand_starter_modal', value)
const collapseStarterModal = (value) =>
  store.dispatch('collapse_starter_modal', value)
const showSignUp = (value) => store.dispatch('show_sign_up', value)
const updateSetupContent = (content) =>
  store.dispatch('updateSetupContent', content)

const closeStarterModal = () => {
  setStorageFormatSelect(false)
  disableSecurityPaymentTab()
  if (starterExpanded.value) {
    collapseStarterModal(true)
    showSignUp(false)
  }
  setTimeout(() => {
    expandStarterModal(false)
  }, 100)
  maximizeStarterModal(false)
  updateSetupContent('SetupStarterButton')
}

const gotoTryDemoSite = () => {
  window.open('https://demo.sharparchive.com/', '_blank')
}

const topFunction = () => {
  document.body.scrollTop = 0
  document.documentElement.scrollTop = 0
}
</script>

<style lang="scss" scoped>
.footer-body {
  @apply md:block lg:px-101 p-4 pt-0 justify-items-center;
  .footer_menu {
    @apply flex lg:flex-row flex-col 2xl:flex-nowrap 2xl:space-x-4 flex-wrap lg:justify-between lg:items-start items-center lg:text-left text-center pt-0;
  }
  .footer_bottom_menu {
    @apply w-full max-w-[1539px] flex lg:flex-row flex-col lg:justify-between lg:items-start items-center lg:text-left text-center sm:mt-0;
    .footer_bottom_menu_item {
      @apply text-yellow-primary text-lg py-1;
    }
  }
}
.social_icon {
  @apply w-7 h-7;
}
.footer-form {
  @apply h-full md:max-w-[400px] 2xl:max-w-[300px] max-w-full -mt-[56px] lg:-ml-[38px];
  background: transparent;
  height: 439px;
  width: 100%;
}
.twitter-icon {
  background-size: cover;
  animation: twitter 6s ease-in-out;
  animation-iteration-count: infinite;
}
@keyframes twitter {
  0% {
    background-image: url('~/assets/img/icon/TwitterIcon/twitter.svg');
  }
  50% {
    background-image: url('~/assets/img/icon/TwitterIcon/X_logo.png');
  }
  100% {
    background-image: url('~/assets/img/icon/TwitterIcon/twitter.svg');
  }
}
.footer-terms p {
  @apply py-1;
}
a {
  @apply text-yellow-primary;
}
.min-width-12 {
  @apply lg:mt-0 2xl:mt-0 mx-0;
  min-width: 254px;
}
@media (min-width: 1024px) and (max-width: 1210px) {
  .min-width-12 {
    max-width: 254px;
    min-width: 230px;
  }
  .footer-form {
    min-width: 264px;
    max-width: 264px;
  }
  .padding-left-middle {
    padding-left: 25px;
  }
  .padding-left-right {
    padding-left: 0px; // 50px
  }
}
@media (min-width: 1211px) and (max-width: 1302px) {
  .min-width-12 {
    max-width: 308px;
    min-width: 308px;
  }
  .footer-form {
    min-width: 312px;
    max-width: 312px;
  }
  .padding-left-middle {
    padding-left: 50px;
  }
  .padding-left-right {
    padding-left: 0px; // 100px
  }
}
@media (min-width: 1303px) and (max-width: 1369px) {
  .min-width-12 {
    max-width: 338px;
    min-width: 338px;
  }
  .footer-form {
    min-width: 338px;
    max-width: 338px;
  }
  .padding-left-middle {
    padding-left: 60px;
  }
  .padding-left-right {
    padding-left: 0px; // 120px
  }
}
@media (min-width: 1370px) and (max-width: 1535px) {
  .min-width-12 {
    max-width: 316px;
    min-width: 316px;
  }
  .footer-form {
    min-width: 316px;
    max-width: 316px;
  }
  .padding-left-middle {
    padding-left: 50px;
  }
  .padding-left-right {
    padding-left: 0px; // 100px
  }
}
@media (min-width: 1536px) {
  .footer-body {
    padding-left: 10%;
    padding-right: 10%;
  }
  .min-width-12 {
    min-width: 200px;
  }
}
@media (max-width: 1535px) and (min-width: 1024px) {
  .second-row {
    margin-top: 40px;
  }
}
</style>
