<template>
  <div class="flex flex-col space-y-3">
    <SummaryAlertsTab />
    <OpenCloseTab />
    <FeedsGroupsResponse
      v-if="currentComp === 'GraphComp' || currentComp === 'SummaryTable'"
    />
    <ActivityLogEditTab v-else-if="currentComp === 'AlertOpenEdit'" />
    <ListSeveritySourceGroupsTab v-else />
    <div
      v-if="currentComp !== 'AlertOpenEdit'"
      class="flex space-x-4 justify-end"
    >
      <button
        class="text-white bg-red-deep w-32 h-10 flex justify-center items-center xl:text-lg md:text-md text-sm font-bold rounded-full"
        @click="store.showNewAlertComp(true)"
      >
        New
      </button>
      <div
        class="flex justify-center items-center w-10 h-10 bg-red-deep rounded-full cursor-pointer"
        @click="
          store.setCurrentComp({
            currentComp: 'AlertSystemSetting',
            previousCurrentComp: currentComp,
          })
        "
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 19.99 20.01"
          width="20"
          height="20"
        >
          <g id="Layer_2" data-name="Layer 2">
            <g id="Layer_1-2" data-name="Layer 1">
              <g id="customize">
                <path
                  id="Path_2114"
                  data-name="Path 2114"
                  class="clss-1"
                  d="M14.68,3.58H10.11a.23.23,0,0,0-.27.18h0a2.84,2.84,0,0,1-5.39,0,.24.24,0,0,0-.27-.2h0q-1.72,0-3.45,0A.72.72,0,0,1,0,2.87a.74.74,0,0,1,.51-.7,1,1,0,0,1,.29,0H4.19A.22.22,0,0,0,4.44,2h0a2.84,2.84,0,0,1,5.4,0c.06.17.14.2.3.2h9.1a.71.71,0,0,1,.7,1,.7.7,0,0,1-.69.44ZM8.57,2.87A1.43,1.43,0,1,0,7.13,4.29h0A1.44,1.44,0,0,0,8.57,2.88Z"
                />
                <path
                  id="Path_2115"
                  data-name="Path 2115"
                  class="clss-1"
                  d="M5.31,9.3H9.88a.25.25,0,0,0,.28-.2,2.84,2.84,0,0,1,5.38,0,.27.27,0,0,0,.31.22c1.14,0,2.29,0,3.43,0a.71.71,0,0,1,.2,1.39.67.67,0,0,1-.28,0H15.83a.26.26,0,0,0-.29.22,2.84,2.84,0,0,1-5.38,0,.26.26,0,0,0-.3-.21H.76A.71.71,0,0,1,0,10.06a.68.68,0,0,1,.06-.34.69.69,0,0,1,.7-.42Zm9,.71a1.43,1.43,0,1,0-1.43,1.43h0A1.43,1.43,0,0,0,14.28,10Z"
                />
                <path
                  id="Path_2116"
                  data-name="Path 2116"
                  class="clss-1"
                  d="M14.68,17.86H10.1a.23.23,0,0,0-.26.19h0a2.84,2.84,0,0,1-5.4,0,.23.23,0,0,0-.26-.19h0c-1.15,0-2.3,0-3.46,0a.72.72,0,0,1-.2-*********,0,0,1,.28,0H4.17a.25.25,0,0,0,.28-.2h0a2.83,2.83,0,0,1,5.38,0,.27.27,0,0,0,.31.22q4.54,0,9.1,0a.7.7,0,0,1,.***********,0,0,1,0,.33.7.7,0,0,1-.69.43Zm-7.54.72a1.43,1.43,0,1,0-1.43-1.44A1.45,1.45,0,0,0,7.14,18.58Z"
                />
              </g>
            </g>
          </g>
        </svg>
      </div>
    </div>
  </div>
</template>

<script>
import { storeToRefs } from 'pinia'
// import { defineComponent } from '@nuxtjs/composition-api'
import SummaryAlertsTab from '~/components/alert/header-tab/SummaryAlertsTab.vue'
import OpenCloseTab from '~/components/alert/header-tab/OpenCloseTab.vue'
import FeedsGroupsResponse from '~/components/alert/header-tab/FeedsGroupsResponse.vue'
import ListSeveritySourceGroupsTab from '~/components/alert/header-tab/ListSeveritySourceGroupsTab.vue'
import ActivityLogEditTab from '~/components/alert/ActivityLogEditTab.vue'
import { useAlert } from '~/stores/alert'

export default defineComponent({
  components: {
    SummaryAlertsTab,
    OpenCloseTab,
    FeedsGroupsResponse,
    ListSeveritySourceGroupsTab,
    ActivityLogEditTab,
  },
  setup() {
    const store = useAlert()

    const { currentComp } = storeToRefs(store)

    return {
      currentComp,
      store,
    }
  },
})
</script>

<style lang="scss" scoped>
.clss-1 {
  fill: #ffffff;
}
</style>
