<template>
  <div>
    <div class="w-full h-full padding relative md:pb-[88px] pb-26">
      <h1 class="release-header pb-20">Release notes</h1>
      <img
        class="shape"
        :src="ShapesImage"
        alt="SharpArchive Release notes Image"
      />

      <div v-if="pending && !releaseNotes">
        <div role="status" class="max-w-full animate-pulse">
          <div class="flex">
            <img
              class="h-10 w-10 pr-2 text-ash-default"
              :src="PointSkeleton"
              alt="SharpArchive Release notes Hand Ponter Icon"
            />
            <div
              class="bg-gray-200 rounded-2xl dark:bg-gray-700 w-48 h-12 mb-4"
            ></div>
          </div>

          <div
            class="ml-10 h-4 bg-gray-200 rounded-full dark:bg-gray-700 w-60 mb-10"
          ></div>

          <div
            v-for="(skeleton, skeletonIndex) in skeletonContent"
            :key="skeletonIndex"
          >
            <div
              class="ml-10 h-8 bg-gray-200 rounded-full dark:bg-gray-700 w-40 mb-2.5 mt-7"
            ></div>

            <div class="w-full ml-10 flex space-x-4 mt-3 items-center">
              <div class="rounded-full bg-gray-700 h-2 w-2"></div>
              <div
                class="h-4 bg-gray-200 rounded-full dark:bg-gray-700 w-4/5 md:w-full"
              ></div>
            </div>
            <div class="w-full ml-10 flex space-x-4 mt-3 items-center">
              <div class="rounded-full h-2 w-2"></div>
              <div
                class="h-4 bg-gray-200 rounded-full dark:bg-gray-700 w-1/2"
              ></div>
            </div>
            <div class="w-full ml-10 flex space-x-4 mt-3 items-center">
              <div class="rounded-full bg-gray-700 h-2 w-2"></div>
              <div
                class="h-4 bg-gray-200 rounded-full dark:bg-gray-700 w-3/4"
              ></div>
            </div>
            <div class="w-full ml-10 flex space-x-4 mt-3 items-center">
              <div class="rounded-full bg-gray-700 h-2 w-2"></div>
              <div
                class="h-4 bg-gray-200 rounded-full dark:bg-gray-700 w-1/2"
              ></div>
            </div>
            <div class="w-full ml-10 flex space-x-4 mt-3 items-center">
              <div class="rounded-full bg-gray-700 h-2 w-2"></div>
              <div
                class="h-4 bg-gray-200 rounded-full dark:bg-gray-700 w-4/5 md:w-full"
              ></div>
            </div>
          </div>
        </div>
      </div>

      <div v-else>
        <div
          :id="`releaseNotes${noteIndex}`"
          v-for="(releaseNote, noteIndex) in releaseNotes"
          :key="noteIndex"
        >
          <div v-if="releaseNote.notes.length > 0">
            <h2 class="flex items-center font-bold text-32px">
              <img
                :id="`img${noteIndex}`"
                class="h-10 w-10 pr-2 text-ash-default"
                :src="PointIcon"
                alt="SharpArchive Release notes Hand Ponter Icon"
              /><span :id="`title${noteIndex}`">{{ releaseNote.title }}</span>
            </h2>

            <p :id="`date${noteIndex}`" class="pl-10 text-gray-light text-base">
              Published on
              <!-- <span>{{ releaseNote.date }}</span> -->
              <DateTime
                :id="`dateTime${noteIndex}`"
                :datetime="releaseNote.date"
                format="ccc, MMM dd, yyyy"
              />
            </p>
          </div>

          <div v-if="releaseNote.notes.length > 0" class="pl-10 pb-6">
            <div
              :id="`notes${itemIndex}`"
              v-for="(item, itemIndex) in releaseNote.notes"
              :key="itemIndex"
            >
              <h3
                :id="`title${itemIndex}`"
                class="text-2xl text-orange-dark pt-7 pb-2"
              >
                {{ item.title }}
              </h3>
              <ul class="list-disc pl-5">
                <li
                  :id="`description${listIndex}`"
                  v-for="(list, listIndex) in item.description"
                  :key="listIndex"
                  class="text-gray-light text-lg pb-2"
                >
                  <span v-html="list"></span>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import PointIcon from '~/assets/img/svg/release-notes/point.svg'
import PointSkeleton from '~/assets/img/svg/release-notes/point-skeleton.svg'
import ShapesImage from '~/assets/img/svg/release-notes/shapes.svg'

const $config = useRuntimeConfig()
const route = useRoute()

useHead(() => ({
  title: 'Release Notes',
  link: [
    {
      hid: 'canonical',
      rel: 'canonical',
      href: `${$config.public.siteUrl}/${route.name}`,
    },
  ],
}))

const {
  data: releaseNotes,
  pending,
  error,
  execute,
} = useLazyFetch('https://releases.sharparchive.com/api/release_notes/')

const skeletonContent = ref([
  {
    name: "What's New",
  },
  {
    name: 'Upcoming',
  },
  {
    name: 'Bug Fixes',
  },
])
</script>

<style lang="scss" scoped>
.release-header {
  @apply text-center font-bold;
  padding-top: 156px;
  font-size: 50px;
  letter-spacing: 12px;
  color: #e28227;
  text-transform: uppercase;
}
.shape {
  width: 374px;
  position: absolute;
  right: -48px;
  top: -15px;
}
.padding {
  @apply px-6 md:px-101;
}
.text-32px {
  font-size: 32px;
}
li::marker {
  color: #e4801d;
}

@media (max-width: 1279px) {
  .shape {
    width: 280px;
    right: -48px;
    top: 14px;
  }
}
@media (max-width: 1023px) {
  .shape {
    width: 220px;
    right: -44px;
    top: 30px;
  }
  .release-header {
    padding-top: 180px;
    font-size: 44px;
    letter-spacing: 10px;
  }
}
@media (max-width: 767px) {
  .release-header {
    padding-top: 160px;
    font-size: 44px;
    letter-spacing: 10px;
  }
  .shape {
    width: 180px;
    right: -32px;
    top: 40px;
  }
}
</style>
