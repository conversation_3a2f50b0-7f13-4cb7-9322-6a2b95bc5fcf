<template>
  <div v-if="isDesktop" class="md:block hidden p-4 h-full relative">
    <div
      class="content flex flex-col transition-all duration-2000"
      :class="[showHomeContent ? 'opacity-0' : '']"
    >
      <Transition name="page" mode="out-in">
        <SourceCreatePostHeader
          @toggle-skew="showSkew($event)"
          v-if="route.name === 'source-create-post'"
        />
        <SourceHeader v-else @toggle-skew="showSkew($event)" />
      </Transition>
      <div
        class="page-content overflow-hidden h-[calc(100%-0px)] transition-all duration-[500ms]"
        :class="[skew ? 'm-xl:ml-70' : '']"
      >
        <NuxtPage />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { breakpointsTailwind, useBreakpoints } from '@vueuse/core'
import { useStore } from 'vuex'

useHead(() => ({
  title: 'Sharp Archive',
  meta: [
    {
      hid: 'robots',
      name: 'robots',
      content: 'noindex, nofollow',
    },
  ],
}))
definePageMeta({
  layoutTransition: false,
  layout: 'dashboard',
  middleware: ['payment', 'auth', 'color'],
})
const store = useStore()
const route = useRoute()
const breakpoints = useBreakpoints(breakpointsTailwind)
const isDesktop = breakpoints.greaterOrEqual('md')
// store state
const showHomeContent = computed(
  () => store.state.loginAnimation.showHomeContent,
)
const skew = ref<boolean>(false)
const showSkew = ($event: boolean) => {
  skew.value = $event
}
</script>
