<script setup>
import { useStore } from 'vuex'

const store = useStore()
const showManagerOnboarding = computed(
  () => store.state.social.showManagerOnboarding,
)
const showConfidentialModeModal = computed(
  () => store.state.emails.showConfidentialModeModal,
)
const showDriveInsertFileModal = computed(
  () => store.state.emails.showDriveInsertFileModal,
)
const showInsertPhotoFileModal = computed(
  () => store.state.emails.showInsertPhotoFileModal,
)
const showAddRecipients = computed(() => store.state.emails.showAddRecipients)
const setManagerOnboarding = ($event) => {
  if (sideBarAccountItems.value && sideBarAccountItems.value.length > 0) {
    store.commit('social/SET_MANAGER_ONBOARDING', $event)
  }
}
const sideBarAccountItems = computed(() => {
  if (store.state.social.sideBarAccountItems.length === 0) {
    store.commit('social/SET_MANAGER_ONBOARDING', true)
  }
  return store.state.social.sideBarAccountItems
})
onMounted(() => {
  if (sideBarAccountItems.value.length === 0) {
    store.commit('social/SET_MANAGER_ONBOARDING', true)
  }
})
</script>

<template>
  <div class="w-full h-full pt-6 overflow-hidden">
    <div class="w-full h-full rounded-2xl flex flex-col overflow-hidden">
      <div
        class="w-full h-[35px] min-h-[35px] bg-blue-200 flex justify-center items-center text-2xl text-white font-bold"
      ></div>
      <div
        class="w-full h-[calc(100%-35px)] overflow-hidden flex-grow bg-[#FFFFFF]"
      >
        <div
          v-if="sideBarAccountItems && sideBarAccountItems.length > 0"
          class="w-full h-full overflow-hidden grid grid-cols-[288px_1fr] auto-rows-auto grid-flow-col relative"
        >
          <div class="w-full h-full overflow-hidden max-w-[288px]">
            <div
              class="w-full h-full overflow-hidden flex flex-col space-y-[29px] border-r-[3px] border-[#F1F2F6]"
            >
              <SourceHubSocialEmailTextTab />
              <SourceHubTheSideBar />
            </div>
          </div>
          <NuxtPage />
        </div>
      </div>
    </div>
    <Transition name="page" mode="out-in">
      <div v-if="showManagerOnboarding">
        <SourceHubManagerOnboarding
          @hide-manager-onboarding="setManagerOnboarding(false)"
        />
        <div
          @click.stop="setManagerOnboarding(false)"
          data-v-c9d2025d=""
          class="fixed inset-0 transition-opacity"
        >
          <div
            data-v-c9d2025d=""
            class="absolute inset-0 bg-gray-600 opacity-75"
          ></div>
        </div>
      </div>
    </Transition>
    <Transition name="page" mode="out-in">
      <div v-if="showConfidentialModeModal">
        <SourceHubEmailsConfidentialModeModal />
        <div
          @click.stop="
            store.commit('emails/SET_CONFIDENTIAL_MODE_MODAL', false)
          "
          data-v-c9d2025d=""
          class="fixed inset-0 transition-opacity"
        >
          <div
            data-v-c9d2025d=""
            class="absolute inset-0 bg-gray-600 opacity-75"
          ></div>
        </div>
      </div>
    </Transition>
    <Transition name="page" mode="out-in">
      <div v-if="showDriveInsertFileModal">
        <SourceHubEmailsDriveInsertFileModal />
        <div
          @click.stop="
            store.commit('emails/SET_DRIVE_INSERT_FILE_MODAL', false)
          "
          data-v-c9d2025d=""
          class="fixed inset-0 transition-opacity"
        >
          <div
            data-v-c9d2025d=""
            class="absolute inset-0 bg-gray-600 opacity-75"
          ></div>
        </div>
      </div>
    </Transition>
    <Transition name="page" mode="out-in">
      <div v-if="showInsertPhotoFileModal">
        <SourceHubEmailsInsertPhotoFileModal />
        <div
          @click.stop="
            store.commit('emails/SET_PHOTO_INSERT_FILE_MODAL', false)
          "
          data-v-c9d2025d=""
          class="fixed inset-0 transition-opacity"
        >
          <div
            data-v-c9d2025d=""
            class="absolute inset-0 bg-gray-600 opacity-75"
          ></div>
        </div>
      </div>
    </Transition>
    <Transition name="page" mode="out-in">
      <div v-if="showAddRecipients">
        <SourceHubMicrosoftModalAddRecipients />
        <div
          @click.stop="store.commit('emails/SET_SHOW_ADD_RECIPIENTS', false)"
          data-v-c9d2025d=""
          class="fixed inset-0 transition-opacity"
        >
          <div
            data-v-c9d2025d=""
            class="absolute inset-0 bg-gray-600 opacity-75"
          ></div>
        </div>
      </div>
    </Transition>
  </div>
</template>
