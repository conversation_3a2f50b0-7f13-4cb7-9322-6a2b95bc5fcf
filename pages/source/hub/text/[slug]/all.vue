<script setup lang="ts">
import { useStore } from 'vuex'

const store = useStore()

const personLists = computed(() => store.state.social.personLists)

const { changePersonListSelectValue } = useSourceApi()
store.commit('social/SET_SINGLE_CONVERTIONS')
</script>

<template>
  <div
    class="flex-grow w-full h-[calc(100%-228px)] grid grid-cols-[1fr_1fr] auto-rows-auto grid-flow-col bg-white"
  >
    <div
      class="h-full overflow-y-auto custom-scroll border-r-[1px] border-[#2E2B2B4D]"
    >
      <div class="flex flex-col space-y-2">
        <SourceHubSocialsMessagesPersonList
          v-for="item in personLists.items"
          :key="item.id"
          :item="item"
          :person-lists="personLists"
          @click="changePersonListSelectValue(item.id)"
        />
      </div>
    </div>
    <div class="h-full !overflow-y-hidden border-r-[1px] border-[#2E2B2B4D]">
      <div class="h-full">
        <SourceHubSocialsMessagesAllTextMessages />
      </div>
    </div>
  </div>
</template>
