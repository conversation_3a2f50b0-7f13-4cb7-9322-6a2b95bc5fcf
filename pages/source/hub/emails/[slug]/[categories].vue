<script setup lang="ts">
import { useStore } from 'vuex'

const store = useStore()

const personLists = computed(() => store.state.social.personLists)
const accountItem = computed(() => store.state.social.accountItem)

const { changePersonListSelectValue } = useSourceApi()
store.commit('social/SET_SINGLE_CONVERTIONS')
</script>

<template>
  <div
    class="flex-grow w-full h-[calc(100%-228px)] grid grid-cols-[186px_1fr] auto-rows-auto grid-flow-col bg-white"
  >
    <div
      class="h-full overflow-y-auto custom-scroll border-r-[1px] border-[#2E2B2B4D]"
    >
      <div class="flex flex-col space-y-2">
        <SourceHubEmailsSidebar v-if="accountItem.provider === 'Google'" />
        <SourceHubMicrosoftSidebar
          v-else-if="accountItem.provider === 'Microsoft'"
        />
      </div>
    </div>
    <div class="h-full !overflow-y-hidden border-r-[1px] border-[#2E2B2B4D]">
      <div class="h-full overflow-hidden">
        <NuxtPage />
      </div>
    </div>
  </div>
</template>
