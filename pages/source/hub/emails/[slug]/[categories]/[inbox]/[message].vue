<script setup lang="ts">
import { useStore } from 'vuex'

const store = useStore()

const accountItem = computed(() => {
  return store.state.social.accountItem
})
</script>

<template>
  <div class="w-full h-auto">
    <SourceHubEmailsSingleMessage v-if="accountItem.provider === 'Google'" />
    <SourceHubMicrosoftSingleMessage
      v-else-if="accountItem.provider === 'Microsoft'"
    />
  </div>
</template>

<style scoped></style>
