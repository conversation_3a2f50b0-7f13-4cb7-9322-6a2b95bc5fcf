<script setup lang="ts">
import { useStore } from 'vuex'

const store = useStore()

const showComposeSection = computed(() => store.state.emails.showComposeSection)
const accountItem = computed(() => store.state.social.accountItem)

onMounted(() => {
  store.commit('emails/SET_UNCHECKED_ALL_EMAIL_MESSAGES')
})
</script>

<template>
  <div class="w-full h-full overflow-hidden">
    <div
      v-if="accountItem.provider === 'Google'"
      class="w-full h-full overflow-hidden"
    >
      <SourceHubEmailsGmailMessageSection v-if="!showComposeSection" />
      <SourceHubEmailsComposeSection v-else />
    </div>
    <div
      v-if="accountItem.provider === 'Microsoft'"
      class="w-full h-full overflow-hidden"
    >
      <SourceHubMicrosoftMessageSection />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.toggle-check-1 {
  &:checked {
    @apply bg-[var(--checkBoxBgColor)];
  }
  &:checked + .check-1 {
    @apply opacity-100;
    color: var(--checkIconColor);
  }
}
</style>
