<template>
  <section class="w-full h-full pb-1 md:px-[20px] lg:px-101 px-6">
    <div
      :id="`usingSharpArchive${index}`"
      v-for="(item, index) in usingSharpArchive"
      :key="item.id"
    >
      <div class="flex space-x-2.5 items-center mb-2">
        <SharedIconFacebookIcon v-if="index === 0" class="w-6 h-6 lg:w-7 lg:h-7" />
        <SharedIconInstagramIcon v-if="index === 1" class="w-6 h-6 lg:w-7 lg:h-7" />
        <div
          v-if="index === 2"
          class="w-6 h-6 lg:w-7 lg:h-7 twitter-icon rounded-full"
        ></div>
        <SharedIconLinkedinIcon v-if="index === 3" class="w-6 h-6 lg:w-7 lg:h-7" />
        <SharedIconYoutubeCircleIcon v-if="index === 4" class="w-6 h-6 lg:w-7 lg:h-7" />
        <SharedIconTiktokIcon v-if="index === 5" class="w-6 h-6 lg:w-7 lg:h-7" />
        <SharedIconPinterestIcon v-if="index === 6" class="w-6 h-6 lg:w-7 lg:h-7" />
        <SharedIconRedditIcon v-if="index === 7" class="w-6 h-6 lg:w-7 lg:h-7" />
        <SharedIconMicrosoftIcon v-if="index === 8" class="w-6 h-6 lg:w-7 lg:h-7" />
        <img
          v-if="index === 9"
          class="ww-6 h-6 lg:w-7 lg:h-7"
          src="~/assets/img/svg/website.svg"
          alt="Web Icon"
        />
        <SharedIconGmailIcon v-if="index === 10" class="w-6 h-6 lg:w-7 lg:h-7" />
        <h2 class="text-xl md:text-2xl text-gray-light leading-8 font-bold">
          {{ item.title }}
        </h2>
      </div>
      <ul
        class="list-outside pl-6 lg:pl-8 list-disc grid grid-cols-1 md:grid-cols-2 gap-y-2 gap-x-6 lg:gap-x-20"
      >
        <li
          :id="`questions${question.id}`"
          v-for="question in item.questions"
          :key="question.id"
          class="text-base md:text-lg leading-6 text-orange-dark"
        >
          <!-- @click.native="
                store.commit('community/SET_CURRENT_QUESTION', question)
              " -->
          <ClientOnly>
            <NuxtLink
              :id="`link${question.id}`"
              :to="`/community/using-sharp-archive/${question.slug.toLowerCase().replace(/\s+/g, '-')}`"
              v-html="question.question"
            >
            </NuxtLink>
          </ClientOnly>
        </li>
      </ul>
      <div class="my-10 md:my-16 lg:my-20 h-0.5 w-full bg-gray-400"></div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { useStore } from 'vuex'

const store = useStore()

const usingSharpArchive = computed(
  () => store.state.community.usingSharpArchive,
)
</script>

<style scoped>
ul li::marker {
  @apply text-xl text-gray-light;
}
.twitter-icon {
  background-size: cover;
  animation: twitter 6s ease-in-out;
  animation-iteration-count: infinite;
}
@keyframes twitter {
  0% {
    background-image: url('~/assets/img/icon/TwitterIcon/twitter.svg');
  }
  50% {
    background-image: url('~/assets/img/icon/TwitterIcon/X_logo.png');
  }
  100% {
    background-image: url('~/assets/img/icon/TwitterIcon/twitter.svg');
  }
}
</style>
